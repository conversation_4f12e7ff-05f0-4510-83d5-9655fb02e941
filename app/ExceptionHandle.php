<?php

namespace app;

use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Response;
use Throwable;
use Twig\Error\LoaderError;
use yunwuxin\auth\exception\AuthenticationException;

/**
 * 应用异常处理类
 */
class ExceptionHandle extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
        LoaderError::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     *
     * @access public
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 使用内置的方式记录异常日志
        if (!$this->isIgnoreReport($exception)) {
            $data = [
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'message' => $this->getMessage($exception),
                'code'    => $this->getCode($exception),
            ];

            $log = $data['message'];
            $log .= PHP_EOL . $this->app->request->url(true);
            $log .= PHP_EOL . "{$data['file']}:{$data['line']}";

            if ($this->app->config->get('log.record_trace')) {
                $log .= PHP_EOL . $exception->getTraceAsString();
            }

            try {
                $this->app->log->record($log, 'error');
            } catch (Exception) {
            }
        }
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @access public
     * @param \think\Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 添加自定义异常处理机制
        switch (true) {
            case $e instanceof AuthenticationException:
                if (!$request->isJson()) {
                    return redirect('/login')->remember();
                }
                break;
            case $e instanceof ValidateException:
                $error = $e->getError();
                if (is_string($error)) {
                    return response($error, 422);
                } else {
                    return json($error, 422);
                }
            case $e instanceof ModelNotFoundException:
                $e = new HttpException(404, $e->getMessage(), $e);
                break;
            case $e instanceof LoaderError:
                $e = new HttpException(404, 'Not Found', $e);
                break;
            case $e instanceof HttpException :
                if ($e->getStatusCode() == 422) {
                    return response($e->getMessage(), 422);
                }
                break;
        }

        // 其他错误交给系统处理
        return parent::render($request, $e);
    }

    protected function renderHttpException(\think\Request $request, HttpException $e): Response
    {
        if (!$request->isJson()) {
            $status = $e->getStatusCode();

            if (in_array($status, [401, 403, 404])) {
                return view('error', code: $status)->header($e->getHeaders())->assign(['e' => $e]);
            }
        }
        return parent::renderHttpException($request, $e);
    }
}
