<?php

namespace app\db\clickhouse;

use think\db\BaseQuery;
use think\db\exception\DbException as Exception;
use think\db\Fetch;
use think\db\Raw;

class Query extends BaseQuery
{
    public function final()
    {
        $this->options['final'] = true;

        return $this;
    }

    public function preWhere($preWhere = true)
    {
        $this->options['pre_where'] = $preWhere;
        return $this;
    }

    public function getFieldType($field)
    {
        $fieldType = $this->options['field_type'] ?? [];

        return $fieldType[$field] ?? null;
    }

    /**
     * 获取当前数据表的主键.
     *
     * @return string|array
     */
    public function getPk()
    {
        return $this->pk ?: $this->connection->getConfig('pk');
    }

    /**
     * 获取字段类型信息.
     *
     * @return array
     */
    public function getFieldsBindType(): array
    {
        return [];
    }

    /**
     * 指定group查询.
     *
     * @param string|array $group GROUP
     *
     * @return $this
     */
    public function group($group)
    {
        $this->options['group'] = $group;

        return $this;
    }

    /**
     * 表达式方式指定查询字段.
     *
     * @param string $field 字段名
     *
     * @return $this
     */
    public function fieldRaw(string $field)
    {
        $this->options['field'][] = new Raw($field);

        return $this;
    }

    /**
     * 创建子查询SQL.
     *
     * @param bool $sub 是否添加括号
     *
     * @return string
     * @throws Exception
     *
     */
    public function buildSql(bool $sub = true): string
    {
        return $sub ? '( ' . $this->fetchSql()->select() . ' )' : $this->fetchSql()->select();
    }

    /**
     * 获取执行的SQL语句而不进行实际的查询.
     *
     * @param bool $fetch 是否返回sql
     *
     * @return $this|Fetch
     */
    public function fetchSql(bool $fetch = true)
    {
        $this->options['fetch_sql'] = $fetch;

        if ($fetch) {
            return new Fetch($this);
        }

        return $this;
    }

    /**
     * 分批数据返回处理.
     *
     * @param int $count 每次处理的数据数量
     * @param callable $callback 处理回调方法
     * @param string|array $column 分批处理的字段名
     * @param string $order 字段排序
     *
     * @return bool
     * @throws Exception
     *
     */
    public function chunk(int $count, callable $callback, string|array $column = null, string $order = 'asc'): bool
    {
        $options = $this->getOptions();
        $column  = $column ?: $this->getPk();

        if (isset($options['order'])) {
            unset($options['order']);
        }

        $bind = $this->bind;

        if (is_array($column)) {
            $times = 1;
            $query = $this->options($options)->page($times, $count);
        } else {
            $query = $this->options($options)->limit($count);

            if (str_contains($column, '.')) {
                [$alias, $key] = explode('.', $column);
            } else {
                $key = $column;
            }
        }

        $resultSet = $query->order($column, $order)->select();

        while (count($resultSet) > 0) {
            if (false === call_user_func($callback, $resultSet)) {
                return false;
            }

            if (isset($times)) {
                $times++;
                $query = $this->options($options)->page($times, $count);
            } else {
                $end    = $resultSet->pop();
                $lastId = is_array($end) ? $end[$key] : $end->getData($key);

                $query = $this->options($options)
                    ->limit($count)
                    ->where($column, 'asc' == strtolower($order) ? '>' : '<', $lastId);
            }

            $resultSet = $query->bind($bind)->order($column, $order)->select();
        }

        return true;
    }
}
