<?php

namespace app\db\clickhouse;

use think\db\BaseQuery as Query;

class Builder extends \think\db\Builder
{

    protected $selectSql = 'SELECT%DISTINCT%%EXTRA% %FIELD% FROM %TABLE%%FINAL%%JOIN%%WHERE%%GROUP%%HAVING%%UNION%%ORDER%%LIMIT%';

    protected function parseFinal(Query $query, bool $final): string
    {
        return $final ? ' FINAL' : '';
    }

    public function select(Query $query, bool $one = false): string
    {
        $options = $query->getOptions();

        $final    = $options['final'] ?? false;
        $preWhere = $options['pre_where'] ?? false;

        $where = $this->parseWhere($query, $options['where']);

        if ($final && !empty($where) && $preWhere) {
            $where = str_replace('WHERE', 'PREWHERE', $where);
        }

        return str_replace(
            ['%TABLE%', '%DISTINCT%', '%EXTRA%', '%FIELD%', '%JOIN%', '%WHERE%', '%GROUP%', '%HAVING%', '%ORDER%', '%LIMIT%', '%UNION%', '%FINAL%'],
            [
                $this->parseTable($query, $options['table']),
                $this->parseDistinct($query, $options['distinct']),
                $this->parseExtra($query, $options['extra']),
                $this->parseField($query, $options['field'] ?? []),
                $this->parseJoin($query, $options['join']),
                $where,
                $this->parseGroup($query, $options['group']),
                $this->parseHaving($query, $options['having']),
                $this->parseOrder($query, $options['order']),
                $this->parseLimit($query, $one ? '1' : $options['limit']),
                $this->parseUnion($query, $options['union']),
                $this->parseFinal($query, $final),
            ],
            $this->selectSql
        );
    }
}
