<?php

namespace app\db\clickhouse;

use ClickHouseDB\Client;
use Closure;
use think\db\BaseQuery;
use think\db\exception\DbException;
use think\helper\Arr;

class Connection extends \think\db\Connection
{

    protected $config = [
        'hostname' => '',
        'hostport' => '',
        'username' => '',
        'password' => '',
    ];

    public function getQueryClass(): string
    {
        return Query::class;
    }

    public function getBuilderClass(): string
    {
        return Builder::class;
    }

    public function connect(array $config = [], $linkNum = 0)
    {
        if (!isset($this->links[$linkNum])) {
            if (empty($config)) {
                $config = $this->config;
            } else {
                $config = array_merge($this->config, $config);
            }

            $client = new Client([
                'host'     => $config['hostname'],
                'port'     => $config['hostport'],
                'username' => $config['username'],
                'password' => $config['password'],
            ]);
            $client->database($config['database']);
            $client->ping(true);

            $this->links[$linkNum] = $client;
        }

        return $this->links[$linkNum];
    }

    public function close()
    {
        $this->linkID     = null;
        $this->linkWrite  = null;
        $this->linkRead   = null;
        $this->links      = [];
        $this->transTimes = 0;

        return $this;
    }

    public function find(BaseQuery $query): array
    {
        $query->preWhere();
        return $this->ckQuery($query, function ($query) {
            return $this->builder->select($query, true);
        })->fetchOne() ?? [];
    }

    public function select(BaseQuery $query): array
    {
        return $this->ckQuery($query, function ($query) {
            return $this->builder->select($query);
        })->rows();
    }

    public function insert(BaseQuery $query, bool $getLastInsID = false)
    {
        $data = $query->getOptions('data');

        $this->linkID = $this->connect();

        $this->linkID->insert($query->getTable(), [array_values($data)], array_keys($data));
    }

    public function insertAll(BaseQuery $query, array $dataSet = []): int
    {
        // TODO: Implement insertAll() method.
    }

    public function update(BaseQuery $query): int
    {
        // TODO: Implement update() method.
    }

    public function delete(BaseQuery $query): int
    {
        // TODO: Implement delete() method.
    }

    public function value(BaseQuery $query, string $field, $default = null, bool $one = true)
    {
        $options = $query->parseOptions();

        if (isset($options['field'])) {
            $query->removeOption('field');
        }

        if (isset($options['group'])) {
            $query->group('');
        }

        $query->setOption('field', (array) $field);

        // 生成查询SQL
        $sql = $this->builder->select($query, $one);

        if (isset($options['field'])) {
            $query->setOption('field', $options['field']);
        } else {
            $query->removeOption('field');
        }

        if (isset($options['group'])) {
            $query->setOption('group', $options['group']);
        }

        // 执行查询操作
        $statement = $this->getStatement($sql, $query->getBind());

        $result = $statement->fetchOne();

        return !empty($result) ? Arr::first($result) : $default;
    }

    public function column(BaseQuery $query, array|string $column, string $key = ''): array
    {
        // TODO: Implement column() method.
    }

    public function aggregate(BaseQuery $query, string $aggregate, $field, bool $force = false, bool $one = true)
    {
        if (is_string($field) && 0 === stripos($field, 'DISTINCT ')) {
            [$distinct, $field] = explode(' ', $field);
        }

        $field = $aggregate . '(' . (!empty($distinct) ? 'DISTINCT ' : '') . $this->builder->parseKey($query, $field, true) . ') AS think_' . strtolower($aggregate);

        $result = $this->value($query, $field, 0, $one);

        return $force ? (float) $result : $result;
    }

    public function transaction(callable $callback)
    {
        throw new DbException('ClickHouse not support transaction');
    }

    public function startTrans()
    {
        throw new DbException('ClickHouse not support transaction');
    }

    public function commit()
    {
        throw new DbException('ClickHouse not support transaction');
    }

    public function rollback()
    {
        throw new DbException('ClickHouse not support transaction');
    }

    public function getTableFields(string $tableName): array
    {
        return [];
    }

    public function getLastSql(): string
    {
        return $this->queryStr;
    }

    public function getLastInsID(BaseQuery $query, string $sequence = null)
    {
        // TODO: Implement getLastInsID() method.

    }

    protected function getStatement($sql, $bind = [])
    {
        $this->linkID = $this->connect();

        $this->queryStartTime = microtime(true);

        $sql = $this->getRealSql("{$sql} ", $bind);

        $this->queryStr = $sql;

        $statement = $this->linkID->select($sql);

        // SQL监控
        if (!empty($this->config['trigger_sql'])) {
            $this->trigger($sql, true);
        }

        return $statement;
    }

    protected function ckQuery(BaseQuery $query, $sql)
    {
        $query->parseOptions();

        if ($sql instanceof Closure) {
            $sql = $sql($query);
        }

        $bind = $query->getBind();

        return $this->getStatement($sql, $bind);
    }
}
