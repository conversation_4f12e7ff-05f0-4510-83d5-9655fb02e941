<?php

namespace app\lib;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use think\exception\ValidateException;

class Certbase
{
    protected Client $client;
    protected string $apiKey = 'WcNOov/JfdiOVeAnvdHXITS7cNCzm0oEwf/ERpyLWizXXyj4+DVC6F24x2CR740j';
    protected string $baseUri = 'https://cert.topthink.com/certificates/';

    public function __construct()
    {
        $this->client = new Client([
            'base_uri'        => $this->baseUri,
            'timeout'         => 5,
            'connect_timeout' => 5,
            'headers'         => ['APIKEY' => $this->apiKey],
        ]);
    }

    public function csr($domain, $org = [])
    {
        return $this->request('post', 'csr', [
            'json' => array_merge([
                'country'    => 'CN',
                'domain'     => $domain,
                'encryption' => 'RSA',
                'hashSign'   => 'SHA256',
                'keySize'    => 2048,
            ], $org),
        ]);
    }

    public function create(int $id, array $data)
    {
        return $this->request('post', "id/{$id}", ['json' => $data]);
    }

    public function cancel(string $orderNo)
    {
        return $this->request('get', "cancel/{$orderNo}");
    }

    public function verifyDomains(string $orderNo)
    {
        return $this->request('put', "verifyDomains/{$orderNo}");
    }

    public function domains(string $orderNo)
    {
        $result = $this->request('get', "domains/{$orderNo}");
        return $result['domainList'];
    }

    public function status(string $orderNo)
    {
        $result = $this->request('get', "status/{$orderNo}");
        return $result['status'];
    }

    public function download(string $orderNo)
    {
        $result = $this->request('get', "download/{$orderNo}");
        return $result['certInfo'];
    }

    protected function request($method, $uri, $options = [])
    {
        try {
            $response = $this->client->request($method, $uri, $options);
        } catch (ClientException $e) {
            if (!$e->hasResponse()) {
                throw $e;
            }

            $response = $e->getResponse();
        }

        $result = json_decode($response->getBody()->getContents(), true);

        if ($result['code'] !== 200) {
            throw new ValidateException("[{$result['code']}] {$result['msg']}");
        }

        return $result['data'];
    }
}
