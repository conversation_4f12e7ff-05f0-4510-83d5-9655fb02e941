<?php

namespace app\lib;

use think\Cache;
use think\Env;

class Otp
{
    public function __construct(protected Cache $cache, protected Env $env)
    {

    }

    public function verify(string $id, $code, $prefix = '')
    {
        if ($this->env->get('APP_TEST', false)) {
            return true;
        }
        $key     = $this->key($id, $prefix);
        $payload = $this->cache->get($key);

        if ($payload && $code == $payload['code'] && time() - 60 * 10 < $payload['time']) {
            $this->cache->delete($key);
            return true;
        }
        return false;
    }

    public function create(string $id, $prefix = '')
    {
        $code = rand(100000, 999999);

        $this->cache->set($this->key($id, $prefix), [
            'code' => $code,
            'time' => time(),
        ]);

        return $code;
    }

    protected function key(string $id, $prefix = '')
    {
        return md5(self::class . '.' . $prefix . $id);
    }
}
