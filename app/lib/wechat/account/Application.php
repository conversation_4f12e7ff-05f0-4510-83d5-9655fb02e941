<?php

namespace app\lib\wechat\account;

use app\lib\wechat\Account;
use app\lib\wechat\Encryptor;
use app\lib\wechat\support\AccessTokenAwareClient;
use app\lib\wechat\traits\InteractWithCache;
use app\lib\wechat\traits\InteractWithClient;
use app\lib\wechat\traits\InteractWithHttpClient;
use Symfony\Component\OptionsResolver\OptionsResolver;
use yunwuxin\social\channel\Wechat;

class Application
{
    use InteractWithHttpClient;
    use InteractWithClient;
    use InteractWithCache;

    protected $config;

    protected $account;
    protected $encryptor;
    protected $accessToken;

    public function __construct($config)
    {
        $resolver = new OptionsResolver();
        $resolver->setRequired(['app_id']);
        $resolver->setDefined(['app_secret', 'token', 'aes_key']);
        $resolver->setDefaults([
            'app_secret' => '',
            'token'      => '',
            'aes_key'    => '',
        ]);
        $this->config = $resolver->resolve($config);
    }

    public function getEncryptor(): Encryptor
    {
        if (!$this->encryptor) {
            $token  = $this->getAccount()->getToken();
            $aesKey = $this->getAccount()->getAesKey();

            if (empty($token) || empty($aesKey)) {
                throw new \InvalidArgumentException('token or aes_key cannot be empty.');
            }

            $this->encryptor = new Encryptor(
                appId: $this->getAccount()->getAppId(),
                token: $token,
                aesKey: $aesKey,
                receiveId: $this->getAccount()->getAppId()
            );
        }

        return $this->encryptor;
    }

    public function setEncryptor(Encryptor $encryptor): static
    {
        $this->encryptor = $encryptor;

        return $this;
    }

    public function setAccessToken(\app\lib\wechat\contracts\AccessToken $accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    public function getAccount()
    {
        if (!$this->account) {
            $this->account = new Account(
                appId: (string) $this->config['app_id'],
                secret: (string) $this->config['app_secret'],
                token: (string) $this->config['token'],
                aesKey: (string) $this->config['aes_key'],
            );
        }
        return $this->account;
    }

    public function getAccessToken(): \app\lib\wechat\contracts\AccessToken
    {
        if (!$this->accessToken) {
            $this->accessToken = new AccessToken(
                appId: $this->getAccount()->getAppId(),
                appSecret: $this->getAccount()->getSecret(),
                cache: $this->getCache(),
                httpClient: $this->getHttpClient(),
            );
        }

        return $this->accessToken;
    }

    public function getOAuth()
    {
        return app(Wechat::class, ['name' => 'wechat', 'config' => [
            'client_id'     => $this->getAccount()->getAppId(),
            'client_secret' => $this->getAccount()->getSecret(),
        ]]);
    }

    protected function getHttpClientDefaultOptions(): array
    {
        return ['base_uri' => 'https://api.weixin.qq.com/'];
    }

    public function createClient(): AccessTokenAwareClient
    {
        return (new AccessTokenAwareClient(
            client: $this->getHttpClient(),
            accessToken: $this->getAccessToken(),
        ));
    }
}
