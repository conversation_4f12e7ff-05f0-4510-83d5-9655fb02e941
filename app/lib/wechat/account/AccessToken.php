<?php

namespace app\lib\wechat\account;

use app\lib\wechat\contracts\RefreshableAccessToken;
use Guzzle<PERSON>ttp\Client;
use Psr\SimpleCache\CacheInterface;

class AccessToken implements RefreshableAccessToken
{
    protected $key;

    public function __construct(
        protected string         $appId,
        protected string         $appSecret,
        protected CacheInterface $cache,
        protected Client         $httpClient,
    )
    {
        $this->key = sprintf('wechat.account_stable_access_token.%s', $this->appId);
    }

    public function getToken(): string
    {
        $token = $this->cache->get($this->key);

        if ((bool) $token && is_string($token)) {
            return $token;
        }

        return $this->refresh();
    }

    public function toQuery(): array
    {
        return ['access_token' => $this->getToken()];
    }

    public function refresh(): string
    {
        $response = $this->httpClient->request(
            'POST',
            'cgi-bin/stable_token',
            [
                'json' => [
                    'grant_type' => 'client_credential',
                    'appid'      => $this->appId,
                    'secret'     => $this->appSecret,
                ],
            ]
        );

        $result = json_decode($response->getBody()->getContents(), true);

        if (empty($result['access_token'])) {
            throw new \Exception('Failed to get access_token: ' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }

        $this->cache->set($this->key, $result['access_token'], intval($result['expires_in']));

        return $result['access_token'];
    }
}
