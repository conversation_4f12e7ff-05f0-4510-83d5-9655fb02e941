<?php

namespace app\lib\wechat\wxa;

use InvalidArgumentException;
use Psr\SimpleCache\CacheInterface;
use RuntimeException;

class VerifyTicket
{
    protected $key;

    public function __construct(
        protected string $appId,
        protected CacheInterface  $cache,
    )
    {
        $this->key = sprintf('wechat.verify_ticket.%s', $this->appId);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function setTicket(string $ticket): static
    {
        $this->cache->set($this->key, $ticket, 6000);

        return $this;
    }

    /**
     * @throws RuntimeException
     * @throws InvalidArgumentException
     */
    public function getTicket(): string
    {
        $ticket = $this->cache->get($this->key);

        if (!$ticket || !is_string($ticket)) {
            throw new RuntimeException('No component_verify_ticket found.');
        }

        return $ticket;
    }
}
