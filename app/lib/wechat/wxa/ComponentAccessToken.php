<?php

namespace app\lib\wechat\wxa;

use app\lib\wechat\contracts\RefreshableAccessToken;
use GuzzleHttp\Client;
use Psr\SimpleCache\CacheInterface;

class ComponentAccessToken implements RefreshableAccessToken
{
    protected $key;

    public function __construct(
        protected string         $appId,
        protected string         $appSecret,
        protected VerifyTicket   $verifyTicket,
        protected CacheInterface $cache,
        protected Client         $httpClient,
    )
    {
        $this->key = sprintf('wechat.component_access_token.%s.%s', md5($this->verifyTicket->getTicket()), $this->appId);
    }

    public function getToken(): string
    {
        $token = $this->cache->get($this->key);

        if ((bool) $token && \is_string($token)) {
            return $token;
        }

        return $this->refresh();
    }

    public function toQuery(): array
    {
        return ['component_access_token' => $this->getToken()];
    }

    public function refresh(): string
    {
        $response = $this->httpClient->request(
            'POST',
            'cgi-bin/component/api_component_token',
            [
                'json' => [
                    'component_appid'         => $this->appId,
                    'component_appsecret'     => $this->appSecret,
                    'component_verify_ticket' => $this->verifyTicket->getTicket(),
                ],
            ]
        );

        $result = json_decode($response->getBody()->getContents(), true);

        if (empty($result['component_access_token'])) {
            throw new \Exception('Failed to get component_access_token: ' . json_encode(
                    $result,
                    JSON_UNESCAPED_UNICODE
                ));
        }

        $this->cache->set(
            $this->key,
            $result['component_access_token'],
            abs(intval($result['expires_in']) - 100)
        );

        return $result['component_access_token'];
    }
}
