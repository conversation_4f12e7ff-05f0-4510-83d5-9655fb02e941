<?php

namespace app\lib\wechat\wxa;

use app\lib\wechat\contracts\AccessToken;
use JetBrains\PhpStorm\ArrayShape;
use JetBrains\PhpStorm\Pure;

class AuthorizerAccessToken implements AccessToken
{
    public function __construct(protected string $appId, protected string $accessToken)
    {
    }

    public function getAppId(): string
    {
        return $this->appId;
    }

    public function getToken(): string
    {
        return $this->accessToken;
    }

    public function __toString()
    {
        return $this->accessToken;
    }

    /**
     * @return array<string, string>
     */
    #[Pure]
    #[ArrayShape(['access_token' => 'string'])]
    public function toQuery(): array
    {
        return ['access_token' => $this->getToken()];
    }
}
