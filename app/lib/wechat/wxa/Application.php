<?php

namespace app\lib\wechat\wxa;

use app\lib\wechat\Account;
use app\lib\wechat\contracts\AccessToken;
use app\lib\wechat\Encryptor;
use app\lib\wechat\Message;
use app\lib\wechat\support\AccessTokenAwareClient;
use app\lib\wechat\traits\InteractWithCache;
use app\lib\wechat\traits\InteractWithClient;
use app\lib\wechat\traits\InteractWithHttpClient;
use Closure;
use Symfony\Component\OptionsResolver\OptionsResolver;

class Application
{

    use InteractWithHttpClient;
    use InteractWithClient;
    use InteractWithCache;

    protected $config;

    protected $account;
    protected $encryptor;
    protected $accessToken;

    protected $server;
    protected $verifyTicket;

    public function __construct($config)
    {
        $resolver = new OptionsResolver();
        $resolver->setRequired(['app_id', 'aes_key', 'app_secret', 'token']);
        $this->config = $resolver->resolve($config);
    }

    public function getAccount()
    {
        if (!$this->account) {
            $this->account = new Account(
                appId: (string) $this->config['app_id'],
                secret: (string) $this->config['app_secret'],
                token: (string) $this->config['token'],
                aesKey: (string) $this->config['aes_key'],
            );
        }
        return $this->account;
    }

    public function getEncryptor(): Encryptor
    {
        if (!$this->encryptor) {
            $this->encryptor = new Encryptor(
                appId: $this->getAccount()->getAppId(),
                token: $this->getAccount()->getToken(),
                aesKey: $this->getAccount()->getAesKey(),
                receiveId: $this->getAccount()->getAppId(),
            );
        }
        return $this->encryptor;
    }

    public function getVerifyTicket()
    {
        if (!$this->verifyTicket) {
            $this->verifyTicket = new VerifyTicket(
                appId: $this->getAccount()->getAppId(),
                cache: $this->getCache(),
            );
        }

        return $this->verifyTicket;
    }

    public function getServer()
    {
        if (!$this->server) {
            $this->server = new Server($this->getEncryptor());
        }

        if ($this->server instanceof Server) {
            $this->server->withDefaultVerifyTicketHandler(
                function (Message $message, Closure $next): mixed {
                    $ticket = $this->getVerifyTicket();
                    if (\is_callable([$ticket, 'setTicket'])) {
                        $ticket->setTicket($message->ComponentVerifyTicket);
                    }

                    return $next($message);
                }
            );
        }

        return $this->server;
    }

    public function setAccessToken(AccessToken $accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    public function getAccessToken()
    {
        if (!$this->accessToken) {
            $this->accessToken = new ComponentAccessToken(
                appId: $this->getAccount()->getAppId(),
                appSecret: $this->getAccount()->getSecret(),
                verifyTicket: $this->getVerifyTicket(),
                cache: $this->getCache(),
                httpClient: $this->getHttpClient(),
            );
        }

        return $this->accessToken;
    }

    public function refreshAuthorizerToken(string $authorizerAppId, string $authorizerRefreshToken): array
    {
        $response = $this->getClient()->request(
            'POST',
            'cgi-bin/component/api_authorizer_token',
            [
                'json' => [
                    'component_appid'          => $this->getAccount()->getAppId(),
                    'authorizer_appid'         => $authorizerAppId,
                    'authorizer_refresh_token' => $authorizerRefreshToken,
                ],
            ]
        );

        if (empty($response['authorizer_access_token'])) {
            throw new \Exception('Failed to get authorizer_access_token: ' . json_encode(
                    $response,
                    JSON_UNESCAPED_UNICODE
                ));
        }

        return $response;
    }

    public function getAuthorization(string $authorizationCode): Authorization
    {
        $response = $this->getClient()->request(
            'POST',
            'cgi-bin/component/api_query_auth',
            [
                'json' => [
                    'component_appid'    => $this->getAccount()->getAppId(),
                    'authorization_code' => $authorizationCode,
                ],
            ]
        );

        if (empty($response['authorization_info'])) {
            throw new \Exception('Failed to get authorization_info: ' . json_encode(
                    $response,
                    JSON_UNESCAPED_UNICODE
                ));
        }

        return new Authorization($response);
    }

    public function getAuthorizerInfo($appId)
    {
        $response = $this->getClient()->request(
            'POST',
            'cgi-bin/component/api_get_authorizer_info',
            [
                'json' => [
                    'component_appid'  => $this->getAccount()->getAppId(),
                    'authorizer_appid' => $appId,
                ],
            ]
        );

        if (empty($response['authorization_info'])) {
            throw new \Exception('Failed to get authorization_info: ' . json_encode(
                    $response,
                    JSON_UNESCAPED_UNICODE
                ));
        }

        return new Authorization($response);
    }

    public function getAuthorizerAccessToken(string $appId, string $refreshToken): string
    {
        $cacheKey = sprintf('wechat.authorizer_access_token.%s.%s', $appId, md5($refreshToken));

        $authorizerAccessToken = (string) $this->getCache()->get($cacheKey);

        if (!$authorizerAccessToken) {
            $response              = $this->refreshAuthorizerToken($appId, $refreshToken);
            $authorizerAccessToken = (string) $response['authorizer_access_token'];
            $this->getCache()->set($cacheKey, $authorizerAccessToken, intval($response['expires_in'] ?? 7200) - 500);
        }

        return $authorizerAccessToken;
    }

    public function getAccountAppWithRefreshToken(
        string $appId,
        string $refreshToken,
        array  $config = []
    ): \app\lib\wechat\account\Application
    {
        return $this->getAccountAppWithAccessToken(
            $appId,
            $this->getAuthorizerAccessToken($appId, $refreshToken),
            $config
        );
    }

    public function getAccountAppWithAccessToken(
        string $appId,
        string $accessToken,
        array  $config = []
    ): \app\lib\wechat\account\Application
    {
        return $this->getAccountApp(new AuthorizerAccessToken($appId, $accessToken), $config);
    }

    public function getAccountApp(
        AuthorizerAccessToken $authorizerAccessToken,
        array                 $config = []
    ): \app\lib\wechat\account\Application
    {
        $app = new \app\lib\wechat\account\Application(array_merge(
            [
                'app_id'  => $authorizerAccessToken->getAppId(),
                'token'   => $this->config['token'],
                'aes_key' => $this->config['aes_key'],
            ],
            $config
        ));

        $app->setAccessToken($authorizerAccessToken);
        $app->setEncryptor($this->getEncryptor());

        return $app;
    }

    public function getMiniAppWithRefreshToken(
        string $appId,
        string $refreshToken,
        array  $config = []
    ): \app\lib\wechat\mini\Application
    {
        return $this->getMiniAppWithAccessToken(
            $appId,
            $this->getAuthorizerAccessToken($appId, $refreshToken),
            $config
        );
    }

    public function getMiniAppWithAccessToken(
        string $appId,
        string $accessToken,
        array  $config = []
    ): \app\lib\wechat\mini\Application
    {
        return $this->getMiniApp(new AuthorizerAccessToken($appId, $accessToken), $config);
    }

    public function getMiniApp(AuthorizerAccessToken $authorizerAccessToken, array $config = []): \app\lib\wechat\mini\Application
    {
        $app = new \app\lib\wechat\mini\Application(
            array_merge(
                [
                    'app_id' => $authorizerAccessToken->getAppId(),
                ],
                $config
            )
        );

        $app->setAccessToken($authorizerAccessToken);

        return $app;
    }

    protected function getHttpClientDefaultOptions(): array
    {
        return ['base_uri' => 'https://api.weixin.qq.com/'];
    }

    public function createClient(): AccessTokenAwareClient
    {
        return (new AccessTokenAwareClient(
            client: $this->getHttpClient(),
            accessToken: $this->getAccessToken(),
        ));
    }
}
