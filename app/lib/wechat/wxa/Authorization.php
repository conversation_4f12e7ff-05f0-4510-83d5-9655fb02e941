<?php

namespace app\lib\wechat\wxa;

use app\lib\wechat\traits\HasAttributes;
use ArrayAccess;
use JetBrains\PhpStorm\Pure;
use think\contract\Arrayable;
use think\contract\Jsonable;

class Authorization implements ArrayAccess, Jsonable, Arrayable
{
    use HasAttributes;

    public function getAppId(): string
    {
        /** @phpstan-ignore-next-line */
        return (string) $this->attributes['authorization_info']['authorizer_appid'] ?? '';
    }

    #[Pure]
    public function getAccessToken(): AuthorizerAccessToken
    {
        return new AuthorizerAccessToken(
        /** @phpstan-ignore-next-line */
            $this->attributes['authorization_info']['authorizer_appid'] ?? '',

            /** @phpstan-ignore-next-line */
            $this->attributes['authorization_info']['authorizer_access_token'] ?? ''
        );
    }

    public function getRefreshToken(): string
    {
        /** @phpstan-ignore-next-line */
        return $this->attributes['authorization_info']['authorizer_refresh_token'] ?? '';
    }
}
