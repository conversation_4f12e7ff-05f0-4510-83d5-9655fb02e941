<?php

namespace app\lib\wechat\wxa;

use app\lib\wechat\Encryptor;
use app\lib\wechat\Message;
use app\lib\wechat\traits\DecryptXmlMessage;
use app\lib\wechat\traits\InteractWithHandlers;
use app\lib\wechat\traits\RespondXmlMessage;
use Closure;
use InvalidArgumentException;
use think\Request;
use think\Response;

class Server
{
    use InteractWithHandlers;
    use DecryptXmlMessage;
    use RespondXmlMessage;

    /** @var Request */
    protected $request;

    protected ?Closure $defaultVerifyTicketHandler = null;

    public function __construct(protected Encryptor $encryptor)
    {

    }

    public function serve(Request $request)
    {
        $this->request = $request;

        $message = Message::createFromRequest($request);

        $this->prepend($this->decryptRequestMessage());

        $response = $this->handle(response('success'), $message);

        if (!($response instanceof Response)) {
            $response = $this->transformToReply($response, $message, $this->encryptor);
        }

        return $response;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function withDefaultVerifyTicketHandler(callable $handler): void
    {
        $this->defaultVerifyTicketHandler = fn(): mixed => $handler(...func_get_args());
        $this->handleVerifyTicketRefreshed($this->defaultVerifyTicketHandler);
    }

    /**
     * @throws InvalidArgumentException
     */
    public function handleVerifyTicketRefreshed(callable $handler): static
    {
        if ($this->defaultVerifyTicketHandler) {
            $this->withoutHandler($this->defaultVerifyTicketHandler);
        }

        $this->with(function (Message $message, Closure $next) use ($handler): mixed {
            return $message->InfoType === 'component_verify_ticket' ? $handler($message, $next) : $next($message);
        });

        return $this;
    }

    protected function decryptRequestMessage(): Closure
    {
        $query = $this->request->get();

        return function (Message $message, Closure $next) use ($query): mixed {
            $message = $this->decryptMessage(
                message: $message,
                encryptor: $this->encryptor,
                signature: $query['msg_signature'] ?? '',
                timestamp: $query['timestamp'] ?? '',
                nonce: $query['nonce'] ?? ''
            );

            return $next($message);
        };
    }

}
