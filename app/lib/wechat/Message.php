<?php

namespace app\lib\wechat;

use app\lib\wechat\support\Xml;
use app\lib\wechat\traits\HasAttributes;
use ArrayAccess;
use Exception;
use think\Request;

/**
 * @property string $FromUserName
 * @property string $ToUserName
 * @property string $Encrypt
 * @property string $InfoType
 * @property string $MsgType
 * @property string $Event
 * @property string $EventKey
 * @property string $Content
 * @property string $ComponentVerifyTicket
 * @property string $AuthorizerAppid
 * @property string $Appid
 * @implements ArrayAccess<array-key, mixed>
 */
class Message implements ArrayAccess
{
    use HasAttributes;

    final public function __construct(array $attributes = [], protected ?string $originContent = '')
    {
        $this->attributes = $attributes;
    }

    public static function createFromRequest(Request $request): Message
    {
        $attributes = self::format($originContent = strval($request->getContent()));

        return new static($attributes, $originContent);
    }

    public static function format(string $originContent): array
    {
        if (0 === stripos($originContent, '<')) {
            $attributes = Xml::parse($originContent);
        }

        // Handle JSON format.
        $dataSet = json_decode($originContent, true);

        if (JSON_ERROR_NONE === json_last_error() && $originContent) {
            $attributes = $dataSet;
        }

        if (empty($attributes) || !is_array($attributes)) {
            throw new Exception('Failed to decode request contents.');
        }

        return $attributes;
    }
}
