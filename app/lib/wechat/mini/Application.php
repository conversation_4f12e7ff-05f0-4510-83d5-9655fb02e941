<?php

namespace app\lib\wechat\mini;

use app\lib\wechat\Account;
use app\lib\wechat\support\AccessTokenAwareClient;
use app\lib\wechat\traits\InteractWithCache;
use app\lib\wechat\traits\InteractWithClient;
use app\lib\wechat\traits\InteractWithHttpClient;
use Symfony\Component\OptionsResolver\OptionsResolver;

class Application
{
    use InteractWithHttpClient;
    use InteractWithClient;
    use InteractWithCache;

    protected $config;

    protected $account;
    protected $accessToken;

    public function __construct($config)
    {
        $resolver = new OptionsResolver();
        $resolver->setRequired(['app_id']);
        $resolver->setDefined(['app_secret']);
        $this->config = $resolver->resolve($config);
    }

    public function getAccount()
    {
        if (!$this->account) {
            $this->account = new Account(
                appId: (string) $this->config['app_id'],
                secret: (string) $this->config['app_secret'],
            );
        }
        return $this->account;
    }

    public function setAccessToken(\app\lib\wechat\contracts\AccessToken $accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    public function getAccessToken(): \app\lib\wechat\contracts\AccessToken
    {
        if (!$this->accessToken) {
            $this->accessToken = new AccessToken(
                appId: $this->getAccount()->getAppId(),
                appSecret: $this->getAccount()->getSecret(),
                cache: $this->getCache(),
                httpClient: $this->getHttpClient(),
            );
        }

        return $this->accessToken;
    }

    public function codeToSession(string $code): array
    {
        $response = $this->getClient()->get('/sns/jscode2session', [
            'query' => [
                'appid'      => $this->getAccount()->getAppId(),
                'secret'     => $this->getAccount()->getSecret(),
                'js_code'    => $code,
                'grant_type' => 'authorization_code',
            ],
        ]);

        if (empty($response['openid'])) {
            throw new \Exception('code2Session error: ' . json_encode($response, JSON_UNESCAPED_UNICODE));
        }

        return $response;
    }

    protected function getHttpClientDefaultOptions(): array
    {
        return ['base_uri' => 'https://api.weixin.qq.com/'];
    }

    public function createClient(): AccessTokenAwareClient
    {
        return (new AccessTokenAwareClient(
            client: $this->getHttpClient(),
            accessToken: $this->getAccessToken(),
        ));
    }
}
