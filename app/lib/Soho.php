<?php

namespace app\lib;

use Exception;
use GuzzleHttp\Client;
use think\helper\Str;

class Soho
{
    protected $client;

    public function register($name, $idCard, $mobile, $cardNo)
    {
        try {
            $this->request('/openapi/v1/soho/register', [
                'name'     => $name,
                'cardNo'   => $cardNo,
                'idCard'   => $idCard,
                'mobile'   => $mobile,
                'authType' => 2,
            ]);
        } catch (Exception $e) {
            //已签约
            if ($e->getCode() != 32003) {
                throw $e;
            }
        }
    }

    public function createSettlement($data)
    {
        $this->request('/openapi/v1/payment/batchPayment', [
            'totalCount'      => 1,
            'totalAmt'        => $data['amount'],
            'merchantBatchId' => $data['order_id'],
            'payItemList'     => [
                [
                    'merchantOrderId' => $data['order_id'],
                    'amt'             => $data['amount'],
                    'payeeName'       => $data['name'],
                    'payeeAcc'        => $data['account'],
                    'idCard'          => $data['identity'],
                    'mobile'          => $data['mobile'],
                ],
            ],
            'levyId'          => config('soho.levy_id'),
            'bankId'          => '600019',
            'taskId'          => config('soho.task_id'),
        ]);
    }

    public function querySettlement($id)
    {
        $result = $this->request('/openapi/v1/payment/batchPaymentQuery', [
            'merchantBatchId' => $id,
        ]);
        return $result[0];
    }

    protected function request($uri, $reqData)
    {
        $time  = time();
        $nonce = Str::random(32);

        $signData = $this->getSignData('POST', $uri, $time, $nonce, json_encode($reqData));
        $sign     = $this->signReqData($signData);

        $url = "https://open.daxiongys.com/prod-api" . $uri;

        $response = $this->getClient()->post($url, [
            'headers' => [
                'SP-ID'       => config('soho.sp_id'),
                'Merchant-ID' => config('soho.merchant_id'),
                'Timestamp'   => $time,
                'Nonce-Str'   => $nonce,
                'Signature'   => $sign,
            ],
            'json'    => $reqData,
        ]);

        $result = json_decode((string) $response->getBody(), true);

        if ($result['code'] != 0) {
            throw new Exception($result['msg'], $result['code']);
        }

        return $result['data'];
    }

    protected function getSignData($method, $uri, $time, $nonce, $body)
    {
        return "{$method}\n{$uri}\n{$time}\n{$nonce}\n{$body}\n";
    }

    protected function signReqData($data)
    {
        $priKey    = sprintf(
            "-----BEGIN PRIVATE KEY-----\n%s-----END PRIVATE KEY-----\n",
            chunk_split(config('soho.private_key'), 64, "\n")
        );
        $priKeyId  = openssl_pkey_get_private($priKey);
        $signature = '';
        openssl_sign($data, $signature, $priKeyId, OPENSSL_ALGO_SHA256);

        return base64_encode($signature);
    }

    protected function getClient()
    {
        if (empty($this->client)) {
            $this->client = new Client([
                'base_uri' => 'https://open.daxiongys.com/prod-api',
                'verify'   => false,
            ]);
        }
        return $this->client;
    }
}
