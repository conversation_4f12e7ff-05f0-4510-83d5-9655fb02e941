<?php

namespace app\lib;

use Guz<PERSON><PERSON>ttp\Client;
use <PERSON>uz<PERSON><PERSON>ttp\HandlerStack;
use Guzzle<PERSON>ttp\Middleware;
use GuzzleHttp\Utils;

trait WebhookRunner
{

    protected function run($url, $payload, $secret)
    {
        $handler = new HandlerStack(Utils::chooseHandler());

        $handler->push(Middleware::retry(function ($retries, $request, $response) {
            if ($retries >= 3) {
                return false;
            }
            if ($response && $response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
                return false;
            }

            return true;
        }), 'retry');

        $client = new Client([
            'handler' => $handler,
        ]);

        $payload['sign'] = $this->buildSign($payload, $secret);

        $client->request('POST', $url, [
            'json'    => $payload,
            'timeout' => 5,
            'verify'  => false,
        ]);
    }

    protected function buildSign($payload, $secret)
    {
        ksort($payload);
        return md5(http_build_query($payload) . $secret);
    }
}
