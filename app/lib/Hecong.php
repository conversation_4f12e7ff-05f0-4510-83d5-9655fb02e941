<?php

namespace app\lib;

use Exception;
use GuzzleHttp\Client;
use think\exception\ValidateException;
use think\helper\Arr;

class Hecong
{
    protected $appId  = '622ee76c1127ae39104cd814';
    protected $appKey = '480e2eeb11ec08f99e42f147b80f131f';

    public function __construct()
    {

    }

    public function addTeam($company, $name, $tel)
    {
        return $this->request('/agentapi/addteam', [
            'company' => $company,
            'name'    => $name,
            'tel'     => $tel,
        ]);
    }

    public function loginProxy($id)
    {
        $result = $this->request('/agentapi/loginproxy', [
            'entId' => $id,
        ]);

        return "https://work.aihecong.com/login?hash={$result['hash']}";
    }

    public function open($id, $life, $places)
    {
        $this->request('/agentapi/open', [
            'entId'  => $id,
            'life'   => $life,
            'places' => $places,
        ]);
    }

    public function renew($id, $life)
    {
        $this->request('/agentapi/renew', [
            'entId' => $id,
            'life'  => $life,
        ]);
    }

    protected function request($uri, $data)
    {
        $data['appId']  = $this->appId;
        $data['appKey'] = $this->appKey;

        $client = new Client(['base_uri' => 'https://api.aihecong.com']);

        $response = $client->post($uri, ['json' => $data]);

        $result = json_decode((string) $response->getBody(), true);

        if (Arr::get($result, '_err')) {
            throw new ValidateException(Arr::get($result, 'errorCode'));
        }

        return $result;
    }
}
