<?php

namespace app\lib\provider;

use app\model\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Throwable;
use yunwuxin\auth\credentials\TokenCredentials;
use yunwuxin\auth\interfaces\Provider;

class Console implements Provider
{
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof TokenCredentials) {
            $token = $credentials->getToken();
            try {
                $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                $id      = $decoded['id'];

                $user = User::findOrFail($id);
                if ($user->remember_token == $decoded['remember_token']) {
                    return $user;
                }
            } catch (Throwable) {

            }
        }
    }

    static public function createToken(User $user)
    {
        return JWT::encode([
            'exp'            => time() + 60 * 60 * 24 * 3,
            'iat'            => time(),
            'id'             => $user->id,
            'remember_token' => $user->remember_token,
        ], config('app.token'), 'HS256');
    }
}
