<?php

namespace app\lib\provider;

use app\lib\oauth\HasAccessToken;
use yunwuxin\auth\credentials\RequestCredentials;
use yunwuxin\auth\interfaces\Provider;

class Api implements Provider
{
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof RequestCredentials) {

            $request = $credentials->getRequest();

            if (in_array(HasAccessToken::class, class_uses_recursive($request))) {
                return $request->getAccessToken()?->user;
            }
        }
    }
}
