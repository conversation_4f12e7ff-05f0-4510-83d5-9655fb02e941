<?php

namespace app\lib\provider;

use app\lib\Date;
use app\model\Application;
use app\model\ApplicationToken;
use app\model\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Throwable;
use yunwuxin\auth\credentials\RequestCredentials;
use yunwuxin\auth\interfaces\Provider;
use yunwuxin\oauth\server\ResourceServer;

class Notification implements Provider
{
    public function __construct(protected ResourceServer $server)
    {
    }

    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof RequestCredentials) {
            $request = $credentials->getRequest();

            try {
                $token   = (string) $credentials->getToken();
                $decoded = (array) JWT::decode($token, new Key(config('app.token') . '@notification', 'HS256'));

                $user        = $decoded['user'];
                $application = $decoded['application'];

                $request->setHeader('Application', $application);

                return User::find($user);
            } catch (Throwable) {

            }

            //检查oauth
            try {
                $req = $this->server->validateAuthenticatedRequest($request);

                $accessTokenId = $req->getAttribute('oauth_access_token_id');

                $accessToken = ApplicationToken::where('id', $accessTokenId)
                    ->where('expire_time', '>=', Date::now())
                    ->findOrFail();

                $request->setHeader('Application', $accessToken->application->id);

                return $accessToken->user;
            } catch (Throwable) {

            }
        }
    }

    static public function createToken(User|int $user, ?Application $application = null)
    {
        if ($user instanceof User) {
            $user = $user->id;
        }
        return JWT::encode([
            'exp'         => time() + 60 * 60 * 24,
            'iat'         => time(),
            'user'        => $user,
            'application' => $application?->id ?? 0,
        ], config('app.token') . "@notification", 'HS256');
    }
}
