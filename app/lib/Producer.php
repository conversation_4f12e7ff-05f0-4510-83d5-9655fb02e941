<?php

namespace app\lib;

use app\job\KafkaRetryJob;
use longlang\phpkafka\Producer\ProduceMessage;
use longlang\phpkafka\Producer\ProducerConfig;
use longlang\phpkafka\Util\KafkaUtil;
use think\swoole\pool\Proxy;

/**
 * @mixin \longlang\phpkafka\Producer\Producer
 */
class Producer
{
    /** @var \longlang\phpkafka\Producer\Producer */
    protected $handler;

    public function __construct()
    {
        $connector = function () {

            return new class {
                protected $handler;

                protected function getHandler()
                {
                    if (!$this->handler) {
                        $kafkaHost = env('KAFKA_HOST', 'topthink-kafka');

                        $config = new ProducerConfig();
                        $config->setBootstrapServer("{$kafkaHost}:9092");
                        $config->setAcks(-1);
                        $this->handler = new \longlang\phpkafka\Producer\Producer($config);
                    }

                    return $this->handler;
                }

                public function __call($method, $arguments)
                {
                    return $this->getHandler()->{$method}(...$arguments);
                }
            };
        };

        if (KafkaUtil::inSwooleCoroutine()) {
            $this->handler = new class ($connector, ['max_active' => 10, 'max_wait_time' => 3]) extends Proxy {

            };
        } else {
            $this->handler = $connector();
        }
    }

    /**
     * @param ProduceMessage[] $messages
     */
    public function sendBatch($messages)
    {
        //分批
        $messages = array_chunk($messages, 200);
        foreach ($messages as $chunk) {
            $this->__call('sendBatch', [$chunk]);
        }
    }

    /**
     * 直接调用Kafka Producer，不经过异常处理和队列重试
     * 仅在队列重试任务中使用，避免循环重试
     *
     * @param string $method 方法名
     * @param array $arguments 参数
     * @return mixed
     */
    public function directCall($method, $arguments)
    {
        return $this->handler->{$method}(...$arguments);
    }

    public function __call($method, $arguments)
    {
        try {
            $this->directCall($method, $arguments);
        } catch (\Throwable $e) {
            \think\facade\Log::error("[kafka] method: {$method}, args: " . json_encode($arguments));
            \think\facade\Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());

            // 记录到队列中稍后重试
            queue(KafkaRetryJob::class, [$method, $arguments], 0, 'retry');
        }
    }
}
