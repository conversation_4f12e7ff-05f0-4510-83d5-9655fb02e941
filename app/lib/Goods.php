<?php

namespace app\lib;

use app\enum\Product;
use app\model\Order;
use app\model\User;
use function yunwuxin\pay\generate_no;

abstract class Goods
{
    const NAME = '未知商品'; //用于自营服务统计显示

    use SerializesModel;

    /** @var User */
    protected $user;

    protected $amount;

    /** @var bool 是否可以撤销 */
    protected $canRevoke = false;

    /** 产品名称 */
    protected ?Product $product = null;

    public function canRevoke(Order $order)
    {
        return $this->canRevoke;
    }

    /**
     * @return mixed
     */
    public function getAmount()
    {
        return $this->amount;
    }

    public function setAmount($amount)
    {
        $this->amount = (int) $amount;
        return $this;
    }

    public function revoke(Order $order)
    {

    }

    abstract public function invoke(Order $order);

    /**
     * @return User
     */
    public function getUser()
    {
        if ($this->user) {
            return $this->user;
        } else {
            return new User(['id' => 0, 'name' => '游客', 'username' => null]);
        }
    }

    /**
     * @param User $user
     * @return \app\lib\Goods
     */
    public function setUser($user)
    {
        $this->user = $user;
        return $this;
    }

    abstract public function getSubject();

    public function getProduct()
    {
        return $this->product;
    }

    public function getMaxDiscount()
    {
        return $this->amount;
    }

    /**
     * @return Order
     */
    public function purchase()
    {
        return Order::create([
            'order_no'   => generate_no(),
            'user_id'    => $this->getUser()->id,
            'subject'    => $this->getSubject(),
            'amount'     => $this->getAmount(),
            'goods_type' => get_class($this),
            'goods'      => $this,
        ]);
    }
}
