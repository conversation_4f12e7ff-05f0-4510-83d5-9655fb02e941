<?php

namespace app\lib;

use app\notification\MobileCode;
use TencentCloud\Captcha\V20190722\CaptchaClient;
use TencentCloud\Captcha\V20190722\Models\DescribeCaptchaResultRequest;
use TencentCloud\Common\Credential;
use TencentCloud\Common\Exception\TencentCloudSDKException;
use think\Config;
use yunwuxin\facade\Notification;

trait WithLoginCaptcha
{
    public function captcha(Config $config, Otp $otp)
    {
        $this->validate([
            'mobile|手机号' => 'require|mobile',
            'captcha'       => ['require', function ($data) use ($config) {
                try {
                    // 实例化一个证书对象，入参需要传入腾讯云账户secretId，secretKey
                    $cred = new Credential($config->get('services.tencent.access_key'), $config->get('services.tencent.secret_key'));

                    // # 实例化要请求产品(以cvm为例)的client对象
                    $client = new CaptchaClient($cred, 'ap-shanghai');

                    // 实例化一个请求对象
                    $req = new DescribeCaptchaResultRequest();

                    $req->setCaptchaAppId($config->get('services.tencent.captcha.app_id'));
                    $req->setAppSecretKey($config->get('services.tencent.captcha.secret_key'));
                    $req->setCaptchaType(9);
                    $req->setTicket($data['ticket']);
                    $req->setRandstr($data['randstr']);
                    $req->setUserIp(request()->ip());
                    // 通过client对象调用想要访问的接口，需要传入请求对象
                    $resp = $client->DescribeCaptchaResult($req);

                    return $resp->getCaptchaCode() === 1;
                } catch (TencentCloudSDKException) {

                }
                return false;
            }],
        ], [
            'captcha' => '请完成人机验证',
        ]);

        $mobile = $this->request->param('mobile');

        $code = $otp->create($mobile, 'login-code');

        Notification::send(null, new MobileCode($mobile, $code));
    }
}
