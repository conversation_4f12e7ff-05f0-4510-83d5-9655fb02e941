<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Goods;
use app\model\Order;
use think\exception\ValidateException;
use think\helper\Arr;

class SmsPackage extends Goods
{
    public const NAME = '短信套餐';

    public const PACKAGES = [
        1 => [
            'name'     => '通知短信',
            'packages' => [
                [
                    'nums'  => 1000,
                    'price' => 45,
                ],
                [
                    'nums'  => 10000,
                    'price' => 400,
                ],
                [
                    'nums'  => 100000,
                    'price' => 3800,
                ],
                [
                    'nums'  => 500000,
                    'price' => 18000,
                ],
                [
                    'nums'  => 1000000,
                    'price' => 35000,
                ],
            ],
        ],
        2 => [
            'name'     => '营销短信',
            'packages' => [
                [
                    'nums'  => 1000,
                    'price' => 50,
                ],
                [
                    'nums'  => 10000,
                    'price' => 450,
                ],
                [
                    'nums'  => 100000,
                    'price' => 4200,
                ],
                [
                    'nums'  => 1000000,
                    'price' => 40000,
                ],
            ],
        ],
    ];

    protected ?Product $product = Product::Sms;

    public string $name;

    public function __construct(protected $nums, protected $type = 1)
    {
        $pkg     = self::PACKAGES[$type];
        $package = Arr::first($pkg['packages'], fn($package) => $package['nums'] == $nums);
        if (!$package)
            throw new ValidateException('短信套餐不存在');
        $this->amount = $package['price'] * 100;
        $this->name   = $pkg['name'];
    }

    public function canRevoke(Order $order)
    {
        $package = $order->payable;

        //未使用的套餐可以退款
        return $package->status == 1 && $package->used_nums == 0;
    }

    public function revoke(Order $order)
    {
        $order->payable->delete();
    }

    public function invoke(Order $order)
    {
        return \app\model\SmsPackage::create([
            'user_id'    => $this->user->id,
            'name'       => "套餐包：{$this->nums}次",
            'type'       => $this->type,
            'total_nums' => $this->nums,
            'price'      => $this->amount / 100,
        ]);
    }

    public function getSubject()
    {
        return "[短信服务] {$this->name}套餐包-{$this->nums}次";
    }

    public function getMaxDiscount()
    {
        $pkg = self::PACKAGES[$this->type];

        //获取单价最便宜的套餐
        $package = Arr::first(Arr::sort($pkg['packages'], function ($a, $b) {
            return $b['price'] / $b['nums'] > $a['price'] / $a['nums'] ? -1 : 1;
        }));

        return $this->amount - ($package['price'] / $package['nums'] * $this->nums) * 100;
    }
}
