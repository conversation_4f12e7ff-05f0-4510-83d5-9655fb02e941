<?php

namespace app\lib\goods;

use app\lib\Goods;
use app\model\AssistantHecong as HecongModel;
use app\model\Order;

class AssistantHecong extends Goods
{
    const NAME = '合从客服';

    public function __construct(protected HecongModel $model, protected $years = 1, protected $seat = 1)
    {
        if ($this->model->plan === 'trial' || $this->model->isExpired()) {
            $this->amount = 299 * $this->years * $this->seat * 100;
        } else {
            $this->amount = 299 * $this->years * $this->model->seat * 100;
        }
    }

    public function invoke(Order $order)
    {
        if ($this->model->plan === 'trial' || $this->model->isExpired()) {
            $this->model->open($this->years, $this->seat);
        } else {
            $this->model->renew($this->years);
        }
    }

    public function getSubject()
    {
        return "[运营助理] 合从客服标准版{$this->seat}席位-{$this->years}年";
    }
}
