<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Date;
use app\lib\Goods;
use app\model\ApiVip;
use app\model\Order;
use think\exception\ValidateException;

class ApiVipPlan extends Goods
{
    public const NAME = 'API会员';

    const SENIOR = 'senior';
    const GOLD   = 'gold';

    const TITLE = [
        self::SENIOR => '高级会员',
        self::GOLD   => '黄金会员',
    ];

    public const PRICES = [
        self::SENIOR => 499,
        self::GOLD   => 2599,
    ];

    protected ?Product $product = Product::Api;

    public function __construct(protected string $name, protected $years = 1)
    {
        $this->amount = self::PRICES[$this->name] * $this->years * 100;
    }

    public function canRevoke(Order $order)
    {
        $vip = ApiVip::where('user_id', $this->user->id)->find();

        return !empty($vip) && $vip->plan == $this->name;
    }

    public function revoke(Order $order)
    {
        $vip = ApiVip::where('user_id', $this->user->id)->find();

        if (empty($vip) || $vip->plan != $this->name) {
            throw new ValidateException('会员信息不匹配');
        }

        //扣除天数
        $vip->save(['expire_time' => $vip->expire_time->subYears($this->years)]);
    }

    public function invoke(Order $order)
    {
        $vip = ApiVip::where('user_id', $this->user->id)->find();

        if (empty($vip)) {
            //新购
            ApiVip::create([
                'user_id'     => $this->user->id,
                'plan'        => $this->name,
                'expire_time' => Date::now()->addYears($this->years),
            ]);
        } else {
            //续费
            if ($vip->expire_time->lt(Date::now())) {
                $expireTime = Date::now()->addYears($this->years);
            } else {
                $expireTime = $vip->expire_time->addYears($this->years);
            }

            $vip->save([
                'plan'        => $this->name,
                'expire_time' => $expireTime,
            ]);
        }
    }

    public function getSubject()
    {
        $title = self::TITLE[$this->name];

        return "[API服务] {$title}-{$this->years}年";
    }
}
