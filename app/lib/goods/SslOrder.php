<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Date;
use app\lib\Goods;
use app\model\Order;
use app\model\SslBrand;
use Carbon\CarbonInterval;
use think\helper\Str;

class SslOrder extends Goods
{
    const NAME = 'SSL证书';

    protected ?Product $product = Product::Ssl;

    protected $canRevoke = true;

    public function __construct(protected SslBrand $brand, protected $type, protected $one, protected $wildcard)
    {
        $info  = $this->brand[$this->type];
        $price = $info['price'];

        $price['one']      = explode(',', $price['one']);
        $price['wildcard'] = explode(',', $price['wildcard']);

        $this->amount = ($price['one'][1] * $this->one + $price['wildcard'][1] * $this->wildcard) * 100;
    }

    public function canRevoke(Order $order)
    {
        return $order->payable->status > -1
            && $order->payable->create_time->gt(Date::now()->sub(CarbonInterval::month()));
    }

    public function revoke(Order $order)
    {
        $order->payable->revoke();
    }

    public function invoke(Order $order)
    {
        $info = $this->brand[$this->type];
        $flex = !Str::contains($info['product'], ',');

        if ($this->one + $this->wildcard > 1 || $flex) {
            $productId = $info['product'];
        } else {
            $ids       = explode(',', $info['product']);
            $productId = $ids[$this->one > 0 ? 0 : 1];
        }

        $price = $info['price'];

        $price['one']      = explode(',', $price['one']);
        $price['wildcard'] = explode(',', $price['wildcard']);

        return \app\model\SslOrder::create([
            'user_id'      => $this->user->id,
            'product_id'   => $productId,
            'brand'        => $this->brand->name,
            'auth_type'    => $this->brand->auth_type ?: 'dns',
            'type'         => $this->type,
            'one'          => $this->one,
            'wildcard'     => $this->wildcard,
            'price'        => $price['one'][1] * $this->one + $price['wildcard'][1] * $this->wildcard,
            'agency_price' => $price['one'][2] * $this->one + $price['wildcard'][2] * $this->wildcard,
        ]);
    }

    public function getSubject()
    {
        $types = [
            'dv' => '域名型(DV)',
            'ov' => '企业型(OV)',
        ];
        return "[SSL证书] {$this->brand->name}-{$types[$this->type]}";
    }
}
