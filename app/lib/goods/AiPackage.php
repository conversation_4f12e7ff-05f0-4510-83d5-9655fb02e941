<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Goods;
use app\model\Ai;
use app\model\Order;
use think\helper\Arr;

class AiPackage extends Goods
{
    public const NAME = 'AI Token';

    protected ?Product $product = Product::Ai;

    protected $canRevoke = true;

    public function __construct(protected string $name)
    {
        $package      = $this->getPackage();
        $this->amount = $package['amount'] * 100;
    }

    protected function getPackage()
    {
        return Ai::PACKAGES[$this->name];
    }

    public function revoke(Order $order)
    {
        $package = $this->getPackage();
        $token   = $package['token'];

        $ai = Ai::where('user_id', $this->user->id)->findOrFail();

        $ai->token -= $token;
        $ai->save();
    }

    public function invoke(Order $order)
    {
        $package = $this->getPackage();
        $token   = $package['token'];

        $ai = Ai::where('user_id', $this->user->id)->findOrFail();

        $ai->token += $token;
        $ai->save();
    }

    public function getSubject()
    {
        $package = $this->getPackage();
        return "[ThinkAI] {$package['name']}";
    }

    public function getMaxDiscount()
    {
        //获取单价最便宜的套餐
        $package = Arr::first(Arr::sort(Ai::PACKAGES, function ($a, $b) {
            return $b['amount'] / $b['token'] > $a['amount'] / $a['token'] ? -1 : 1;
        }));

        return $this->amount - ($package['amount'] / $package['token'] * $this->getPackage()['token']) * 100;
    }
}
