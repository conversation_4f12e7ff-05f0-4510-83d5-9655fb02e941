<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Date;
use app\lib\Goods;
use app\model\AssistantSite as SiteModel;
use app\model\Order;

class AssistantSite extends Goods
{
    const NAME = '网站助理';

    protected ?Product $product = Product::Assistant;

    protected $canRevoke = true;

    public function __construct(protected SiteModel $model, protected $years = 1)
    {
        $this->amount = 199 * $this->years * 100;
    }

    public function revoke(Order $order)
    {
        $this->model->save([
            'expire_time' => $this->model->expire_time->subYears($this->years),
        ]);
    }

    public function invoke(Order $order)
    {
        $start = $this->model->isStandard() ? $this->model->expire_time : Date::now();

        $this->model->save([
            'plan'        => 'standard',
            'expire_time' => $start->addYears($this->years),
        ]);
    }

    public function getSubject()
    {
        return "[运营助理] 网站助理标准版-{$this->years}年";
    }
}
