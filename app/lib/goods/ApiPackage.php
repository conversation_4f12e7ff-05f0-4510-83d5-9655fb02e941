<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Goods;
use app\model\Api;
use app\model\ApiUserPackage;
use app\model\Order;
use think\db\Raw;

class ApiPackage extends Goods
{
    public const NAME = 'API套餐';

    protected ?Product $product = Product::Api;

    public function __construct(protected Api $api, protected \app\model\ApiPackage $package)
    {
        $this->amount = $package->price * 100;
    }

    public function canRevoke(Order $order)
    {
        $userPackage = $order->payable;

        //未使用的套餐可以退款
        return $userPackage->status == 1 && $userPackage->used_nums == 0;
    }

    public function revoke(Order $order)
    {
        $order->payable->delete();
    }

    public function invoke(Order $order)
    {
        return ApiUserPackage::create([
            'api_id'       => $this->api->id,
            'user_id'      => $this->user->id,
            'name'         => "套餐包：{$this->package->nums}次",
            'total_nums'   => $this->package->nums,
            'price'        => $this->package->price,
            'agency_price' => $this->package->agency_price,
        ]);
    }

    public function getSubject()
    {
        return "[API服务] {$this->api->title}-{$this->package->nums}次";
    }

    public function getMaxDiscount()
    {
        //获取单价最便宜的套餐
        /** @var \app\model\ApiPackage $package */
        $package = $this->api->packages(false)->order(new Raw('price/nums asc'))->find();

        return ($this->package->price - ($package->price / $package->nums) * $this->package->nums) * 100;
    }
}
