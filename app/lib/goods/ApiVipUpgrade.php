<?php

namespace app\lib\goods;

use app\enum\Product;
use app\lib\Goods;
use app\model\ApiVip;
use app\model\Order;

class ApiVipUpgrade extends Goods
{
    public const NAME = 'API会员';

    protected ?Product $product = Product::Api;

    public function __construct(protected int $days)
    {
        $this->amount = floor($days / 365 * (ApiVipPlan::PRICES[ApiVipPlan::GOLD] - ApiVipPlan::PRICES[ApiVipPlan::SENIOR])) * 100;
    }

    public function invoke(Order $order)
    {
        $vip = ApiVip::where('user_id', $this->user->id)->findOrFail();

        $vip->save([
            'plan' => ApiVipPlan::GOLD,
        ]);
    }

    public function getSubject()
    {
        return "[API服务] 升级黄金会员";
    }
}
