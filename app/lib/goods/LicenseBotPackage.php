<?php

namespace app\lib\goods;

use app\enum\LicensePlan;
use app\lib\Goods;
use app\model\CommissionLog;
use app\model\Order;
use think\exception\ValidateException;

class LicenseBotPackage extends Goods
{
    public const NAME = '应用授权';

    public function __construct(protected \app\model\LicenseBot $license, protected int $nums, $amount = null)
    {
        if ($amount > 0) {
            if ($amount < LicensePlan::Fee['token'] * $this->nums) {
                throw new ValidateException('售价不能低于代理价');
            }
            $this->amount = floor($amount * 100);
        } else {
            $this->amount = LicensePlan::Fee['token'] * $this->nums * 100;
        }
    }

    public function invoke(Order $order)
    {
        $this->license->inc('token', $this->nums * 1000 * 1000)->save();

        //分佣
        $commission = $this->amount - LicensePlan::Fee['token'] * $this->nums * 100;
        if ($commission > 0) {
            $this->license->partner->updateCommission(CommissionLog::TYPE_INC, $commission, $this->getSubject(), $order);
        }
    }

    public function getSubject()
    {
        return "[应用授权] ThinkBot充值 {$this->nums}M Token";
    }
}
