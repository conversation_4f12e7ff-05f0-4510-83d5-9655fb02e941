<?php

namespace app\lib\submail;

use think\Config;
use think\helper\Arr;
use think\tracing\Tracer;

class Sms
{
    use WithHttpClient;

    protected $appId;
    protected $appKey;

    public function __construct(protected Tracer $tracer, Config $config)
    {
        $this->appId  = $config->get('services.submail.app_id');
        $this->appKey = $config->get('services.submail.app_key');
    }

    public function send($to, $content)
    {
        return $this->request('post', '/sms/send.json', [
            'to'      => $to,
            'content' => $content,
        ]);
    }

    public function xSend($to, $template, $vars = null)
    {
        return $this->request('post', '/sms/xsend.json', [
            'to'      => $to,
            'project' => $template,
            'vars'    => $vars ?: null,
        ]);
    }

    public function batchSend($to, $content)
    {
        $result = $this->request('post', '/sms/batchsend.json', [
            'to'      => $to,
            'content' => $content,
        ]);

        return Arr::get($result, 'responses');
    }

    public function batchXSend($to, $template, $vars = null)
    {
        $result = $this->request('post', '/sms/batchxsend.json', [
            'to'      => $to,
            'project' => $template,
            'vars'    => $vars ?: null,
        ]);

        return Arr::get($result, 'responses');
    }

    public function multiSend($content, $multi)
    {
        //替换变量格式
        $content = preg_replace('/\$\{(.+?)\}/', '@var(\1)', $content);

        return $this->request('post', '/sms/multisend.json', [
            'content' => $content,
            'multi'   => $multi,
        ]);
    }

    public function multiXSend($template, $multi)
    {
        return $this->request('post', '/sms/multixsend.json', [
            'project' => $template,
            'multi'   => $multi,
        ]);
    }

    public function addSign($sign, $company, $attachments, $source = 0)
    {
        $options = Options::makeWithParams([
            'sms_signature'        => $sign,
            'company'              => $company['name'] ?? '',
            'company_lisence_code' => $company['identity'] ?? '',
            'legal_name'           => $company['corporation'] ?? '',
            'attachments'          => $attachments,
            'source_type'          => $source,
        ])->useMultipart(['attachments' => 'image.png']);

        return $this->request('post', '/sms/appextend.json', $options);
    }

    public function getSign($sign)
    {
        $result = $this->request('get', '/sms/appextend.json', [
            'sms_signature' => $sign,
        ]);

        return Arr::first(Arr::get($result, 'smsSignature', []));
    }

    public function addTemplate($title, $sign, $content)
    {
        //替换变量格式
        $content = preg_replace('/\$\{(.+?)\}/', '@var(\1)', $content);

        $result = $this->request('post', '/sms/template.json', [
            'sms_title'     => $title,
            'sms_signature' => "【{$sign}】",
            'sms_content'   => $content,
        ]);
        return Arr::get($result, 'template_id');
    }

    public function updateTemplate($id, $title, $sign, $content)
    {
        //替换变量格式
        $content = preg_replace('/\$\{(.+?)\}/', '@var(\1)', $content);

        $this->request('put', '/sms/template.json', [
            'template_id'   => $id,
            'sms_title'     => $title,
            'sms_signature' => "【{$sign}】",
            'sms_content'   => $content,
        ]);
    }

    public function getTemplate($id)
    {
        $result = $this->request('get', '/sms/template.json', [
            'template_id' => $id,
        ]);
        return Arr::get($result, 'template');
    }

    public function getLog($id)
    {
        $result = $this->request('post', '/sms/log.json', [
            'send_id' => $id,
        ]);
        return Arr::get($result, 'data.0');
    }
}
