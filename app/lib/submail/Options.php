<?php

namespace app\lib\submail;

class Options
{

    protected $query = [];

    protected $params = [];

    protected $multipart = false;

    protected $method = 'GET';

    public static function makeWithQuery(array $query)
    {
        return (new self())->setQuery($query);
    }

    public static function makeWithParams(array $params)
    {
        return (new self())->setParams($params);
    }

    public function useMultipart($multipart = true)
    {
        $this->multipart = $multipart;
        return $this;
    }

    public function setMethod($method)
    {
        $this->method = strtoupper($method);
        return $this;
    }

    public function getMethod()
    {
        return $this->method;
    }

    public function setQuery($name, $value = null)
    {
        if (is_array($name)) {
            $this->query = array_merge($this->query, $name);
        } else {
            $this->query[$name] = $value;
        }
        return $this;
    }

    public function getQuery($name = null)
    {
        return is_null($name) ? $this->query : $this->query[$name];
    }

    public function setParams($name, $value = null)
    {
        if (is_array($name)) {
            $this->params = array_merge($this->params, $name);
        } else {
            $this->params[$name] = $value;
        }
        return $this;
    }

    public function getParams($name = null)
    {
        return is_null($name) ? $this->params : $this->params[$name];
    }

    public function toArray()
    {
        if ($this->multipart) {
            $multipart = [];

            foreach ($this->params as $name => $value) {
                foreach ((array) $value as $item) {
                    $part = [
                        'name'     => $name,
                        'contents' => $item,
                    ];

                    if (array_key_exists($name, $this->multipart)) {
                        $part['filename'] = $this->multipart[$name];
                    }

                    $multipart[] = $part;
                }
            }

            return [
                'query'     => $this->query,
                'multipart' => $multipart,
            ];
        }

        return [
            'query'       => $this->query,
            'form_params' => $this->params,
        ];
    }

}
