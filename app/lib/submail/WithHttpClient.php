<?php

namespace app\lib\submail;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use think\helper\Arr;
use const OpenTracing\Tags\ERROR;

trait WithHttpClient
{
    protected $baseUri = 'https://api-v4.mysubmail.com';

    protected function request($method, $uri, $options = [])
    {
        if (!$options instanceof Options) {
            if (strtoupper($method) == 'GET') {
                $options = Options::makeWithQuery($options);
            } else {
                $options = Options::makeWithParams($options);
            }
        }

        $options->setMethod($method);

        $options = $this->transformOptions($options);

        $scope = $this->tracer->startActiveSpan('submail', [
            'tags' => [
                'request.method' => $method,
                'request.uri'    => $uri,
            ],
        ]);

        $span = $scope->getSpan();

        try {
            $response = $this->getHttpClient()->request($method, $uri, $options->toArray());

            $span->setTag('response.status_code', $response->getStatusCode());
            $stream = $response->getBody();
            $span->setTag('response.body', $stream->getContents());
            $stream->rewind();
        } catch (ClientException $e) {
            $span->setTag(ERROR, $e);

            if (!$e->hasResponse()) {
                throw $e;
            }

            $response = $e->getResponse();
        } finally {
            $scope->close();
        }

        $result = json_decode($response->getBody()->getContents(), true);

        return $this->transformResult($result);
    }

    protected function transformOptions(Options $options)
    {
        if ($options->getMethod() == 'GET') {
            $options->setQuery('appid', $this->appId);
            $options->setQuery('signature', $this->appKey);
        } else {
            $options->setParams('appid', $this->appId);
            $options->setParams('signature', $this->appKey);
        }

        return $options;
    }

    protected function transformResult($result)
    {
        if (isset($result['status']) && $result['status'] != 'success') {
            throw new Exception(Arr::get($result, 'msg'), Arr::get($result, 'code', 0));
        }

        return $result;
    }

    protected function getHttpClient()
    {
        return new Client([
            'base_uri' => $this->baseUri,
            'verify'   => false,
        ]);
    }
}
