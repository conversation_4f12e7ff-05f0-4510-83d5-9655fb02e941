<?php

namespace app\lib;

use Exception;
use <PERSON><PERSON><PERSON>\Agent\Agent;
use <PERSON><PERSON><PERSON>\Db\Reader;

class Detect
{

    protected static $reader;

    public static function getLocation($ip)
    {
        if (empty(self::$reader)) {
            $databaseFile = root_path('geo') . 'GeoLite2-City.mmdb';

            self::$reader = new Reader($databaseFile);
        }

        try {
            $result = self::$reader->get($ip);

            return [
                'country'       => $result['country']['names']['zh-CN'] ?? '',
                'country_code'  => $result['country']['iso_code'] ?? '',
                'province'      => $result['subdivisions'][0]['names']['zh-CN'] ?? '',
                'province_code' => $result['subdivisions'][0]['iso_code'] ?? '',
                'city'          => $result['city']['names']['zh-CN'] ?? '',
            ];
        } catch (Exception) {

        }
        return null;
    }

    public static function getClientInfo($userAgent)
    {
        $agent = new Agent();
        $agent->setUserAgent($userAgent);

        $browser = $agent->browser();
        $os      = $agent->platform();

        $device = match (true) {
            $agent->isRobot() => 'robot',
            $agent->isTablet() => 'tablet',
            $agent->isPhone() => 'phone',
            $agent->isDesktop() => 'desktop',
            default => 'unknown',
        };

        return [
            'browser'         => $browser,
            'browser_version' => $agent->version($browser),
            'os'              => $os,
            'os_version'      => $agent->version($os),
            'device'          => $device,
        ];
    }
}
