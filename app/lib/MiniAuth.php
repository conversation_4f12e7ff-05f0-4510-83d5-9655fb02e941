<?php

namespace app\lib;

use app\lib\wechat\mini\Application;
use app\model\SocialToken;
use app\model\User;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Ramsey\Uuid\Uuid;
use think\Cache;
use think\Env;
use think\exception\ValidateException;
use think\helper\Arr;

class MiniAuth
{
    const TOKEN_KEY = 'mini-token-%s';

    public function __construct(protected Application $application, protected Cache $cache, protected Env $env)
    {
    }

    protected function getKey($token)
    {
        return sprintf(self::TOKEN_KEY, $token);
    }

    public function create($page = 'login', $data = false)
    {
        $token = md5((string) Uuid::uuid4());

        $params = [
            'scene'       => $token,
            'page'        => "pages/{$page}/index",
            'check_path'  => false,
            'env_version' => 'release',
        ];

        if ($this->env->get('APP_TEST', false)) {
            $params['env_version'] = 'develop';
        }

        $response = $this->application->getClient()->post('/wxa/getwxacodeunlimit', [
            'json' => $params,
        ]);

        if (is_array($response)) {
            throw new ValidateException(Arr::get($response, 'errmsg'));
        }

        $this->setData($token, $data);

        return [
            'token' => $token,
            'image' => 'data:image/png;base64,' . base64_encode($response),
        ];
    }

    public function verify($token)
    {
        if ($token == 'test' && $this->env->get('APP_TEST', false)) {
            return;
        }

        if (!$this->cache->has($this->getKey($token))) {
            throw new ValidateException('获取登录信息失败，请刷新二维码后重新扫码');
        }
    }

    public function getData($token)
    {
        $this->verify($token);

        return $this->cache->get($this->getKey($token));
    }

    public function deleteData($token)
    {
        $this->cache->delete($this->getKey($token));
    }

    public function setData($token, $data)
    {
        $this->cache->set($this->getKey($token), $data, 300);
    }

    protected function getSocialUser($code)
    {
        $session = $this->application->codeToSession($code);

        $socialUser = \yunwuxin\social\User::make([
            ...$session,
            'nickname' => '微信用户',
        ], [
            'id' => 'unionid',
        ]);

        $socialUser->setChannel('wechat');

        return $socialUser;
    }

    public function check($token, $code)
    {
        $this->verify($token);

        $socialUser = $this->getSocialUser($code);

        $token = SocialToken::getBySocialUser($socialUser);

        $result = [
            'id' => $socialUser->getId(),
        ];

        if (!empty($token)) {
            $result['user'] = JWT::encode([
                'exp'  => time() + 300,
                'iat'  => time(),
                'user' => $token->user_id,
            ], config('app.token'), 'HS256');
        }

        return $result;
    }

    public function authorize($token, $code)
    {
        if ($code instanceof \yunwuxin\social\User) {
            $socialUser = $code;
        } else {
            $socialUser = $this->getSocialUser($code);
        }
        $this->setData($token, $socialUser);
    }

    public function login($token, $user)
    {
        try {
            $parsed = (array) JWT::decode($user, new Key(config('app.token'), 'HS256'));

            $this->setData($token, $parsed['user']);
        } catch (Exception) {
            throw new ValidateException('登录失败，请刷新二维码后重新扫码');
        }
    }

    public function register($token, $id, $code)
    {
        $response = $this->application->getClient()->post('/wxa/business/getuserphonenumber', [
            'json' => [
                'code' => $code,
            ],
        ]);

        if (Arr::get($response, 'errcode') == 0) {
            $mobile = Arr::get($response, 'phone_info.phoneNumber');

            $user = User::retrieveByMobile($mobile, request()->ip());

            $user->socialTokens()->save([
                'channel' => 'wechat',
                'openid'  => $id,
            ]);

            $this->setData($token, $user->id);
        }
    }

    public function user($token)
    {
        $value = $this->getData($token);

        if ($value) {
            switch (true) {
                case is_numeric($value):
                    $user = User::find($value);
                    break;
                case $value instanceof \yunwuxin\social\User:
                    $user = [
                        'id'       => $value->getId(),
                        'nickname' => $value->getNickname(),
                        'avatar'   => $value->getAvatar(),
                        'email'    => $value->getEmail(),
                    ];
                    break;
            }

            if (!empty($user)) {
                $this->deleteData($token);
                return $user;
            }
        }
    }
}
