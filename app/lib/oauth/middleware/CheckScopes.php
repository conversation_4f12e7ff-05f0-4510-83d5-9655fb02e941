<?php

namespace app\lib\oauth\middleware;

use Closure;
use yunwuxin\auth\exception\AuthorizationException;

class CheckScopes
{
    /**
     * @param \app\Request $request
     * @param Closure $next
     * @param mixed ...$scopes
     * @return mixed
     */
    public function handle($request, Closure $next, ...$scopes)
    {
        $token = $request->getAccessToken();
        if ($token) {
            foreach ($scopes as $scope) {
                if (!$token->can($scope)) {
                    throw new AuthorizationException;
                }
            }
            return $next($request);
        }
        throw new AuthorizationException();
    }
}
