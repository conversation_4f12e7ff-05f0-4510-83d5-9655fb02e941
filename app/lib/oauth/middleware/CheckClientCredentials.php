<?php

namespace app\lib\oauth\middleware;

use app\lib\Date;
use app\lib\oauth\HasAccessToken;
use app\model\ApplicationToken;
use Closure;
use Exception;
use yunwuxin\auth\exception\AuthenticationException;
use yunwuxin\oauth\server\ResourceServer;

class CheckClientCredentials
{
    public function __construct(protected ResourceServer $server)
    {
    }

    /**
     * @param \app\Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $req = $this->server->validateAuthenticatedRequest($request);

            $accessTokenId = $req->getAttribute('oauth_access_token_id');

            $accessToken = ApplicationToken::where('id', $accessTokenId)
                ->where('expire_time', '>=', Date::now())
                ->findOrFail();

            if (in_array(HasAccessToken::class, class_uses_recursive($request))) {
                $request->setAccessToken($accessToken);
            }

            return $next($request);
        } catch (Exception) {
            throw new AuthenticationException();
        }
    }
}
