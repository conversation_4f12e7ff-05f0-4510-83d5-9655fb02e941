<?php

namespace app\lib\oauth;

use app\lib\oauth\scopes\UserEmail;
use app\model\User;
use yunwuxin\oauth\server\entities\Client;
use yunwuxin\oauth\server\interfaces\ScopeInterface;

class Scope extends \yunwuxin\oauth\server\entities\Scope implements ScopeInterface
{
    static $scopes = [
        'user:email' => UserEmail::class,
    ];

    protected $title;

    public function getTitle()
    {
        return $this->title;
    }

    public function resolved(User $user)
    {
        return true;
    }

    public function resolveUrl()
    {

    }

    public static function get($id): ?\yunwuxin\oauth\server\entities\Scope
    {
        if (isset(self::$scopes[$id])) {
            return new self::$scopes[$id]($id);
        }

        return new static($id);
    }

    public static function finalize(array $scopes, $grantType, Client $client, $userIdentifier = null): array
    {
        return $scopes;
    }
}
