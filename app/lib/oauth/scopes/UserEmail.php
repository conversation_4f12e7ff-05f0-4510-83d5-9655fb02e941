<?php

namespace app\lib\oauth\scopes;

use app\model\User;
use yunwuxin\oauth\server\entities\Scope;

class UserEmail extends Scope
{
    protected $title = '用户邮箱地址';

    public function getTitle()
    {
        return $this->title;
    }

    public function resolved(User $user)
    {
        return $user->email != null;
    }

    public function resolveUrl()
    {
        return console_url('/user/info');
    }
}
