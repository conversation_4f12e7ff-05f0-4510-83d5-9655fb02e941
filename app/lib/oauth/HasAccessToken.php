<?php

namespace app\lib\oauth;

use app\model\ApplicationToken;

trait HasAccessToken
{
    /** @var ApplicationToken|null */
    protected $accessToken;

    /**
     * @param ApplicationToken $token
     */
    public function setAccessToken($token): void
    {
        $this->accessToken = $token;
    }

    /**
     * @return ApplicationToken|null
     */
    public function getAccessToken()
    {
        return $this->accessToken;
    }
}
