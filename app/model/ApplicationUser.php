<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\ApplicationUser
 *
 * @property \app\lib\Date $create_time
 * @property int $application_id
 * @property int $id
 * @property int $user_id
 * @property-read \app\model\Application $application
 * @property-read mixed $application_name
 */
class ApplicationUser extends Model
{
    protected $autoWriteTimestamp = false;

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    protected function getApplicationNameAttr()
    {
        return $this->application->getAttr('name');
    }
}
