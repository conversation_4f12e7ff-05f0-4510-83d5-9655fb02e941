<?php

namespace app\model;

use app\lib\Date;
use app\lib\Hashids;
use think\db\Raw;
use think\facade\Db;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\AssistantSite
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property array $appearance
 * @property array $broadcast
 * @property array $customs
 * @property array $features
 * @property array $robot
 * @property int $id
 * @property int $status
 * @property string $delete_time
 * @property string $name
 * @property string $plan
 * @property string $scripts
 * @property string $url
 * @property-read \app\model\AssistantSiteEvent[] $events
 * @property-read \app\model\AssistantSiteFeedback[] $feedbacks
 * @property-read \app\model\AssistantSiteSession[] $sessions
 * @property-read \app\model\User[] $members
 * @property-read mixed $code
 * @property-read mixed $hash_id
 * @property-read mixed $is_standard
 * @property-read mixed $stats
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class AssistantSite extends Model
{
    use SoftDelete;

    protected $append = ['hash_id', 'code', 'is_standard'];

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function members()
    {
        return $this->belongsToMany(User::class, AssistantSiteMember::class, 'user_id', 'site_id');
    }

    public function sessions()
    {
        return $this->hasMany(AssistantSiteSession::class, 'site_id', 'id');
    }

    public function events()
    {
        return $this->hasMany(AssistantSiteEvent::class, 'site_id', 'id');
    }

    public function feedbacks()
    {
        return $this->hasMany(AssistantSiteFeedback::class, 'site_id', 'id');
    }

    protected function getStatsAttr()
    {
        $getStats = function (Date $start, Date $end) {

            $subSql = $this->events()
                ->field(
                    new Raw(<<<EOT
session_id, 
count(*) c
EOT
                    )
                )
                ->where('event_type', AssistantSiteEvent::PAGEVIEW)
                ->whereBetween('create_time', [$start, $end])
                ->group('session_id')
                ->buildSql();

            $result = Db::connect('clickhouse')
                ->table($subSql . ' t')
                ->field(
                    new Raw(<<<EOT
sum(t.c) as "pageviews",
count(distinct t.session_id) as "uniques"
EOT
                    )
                )
                ->select();

            return $result[0];
        };

        $yesterday = $getStats(Date::yesterday(), Date::today());
        $today     = $getStats(Date::today(), Date::tomorrow());

        return [
            'yesterday' => $yesterday,
            'today'     => $today,
        ];
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getCodeAttr()
    {
        $src = main_url('/assistant/js');
        return '<script async defer data-id="' . $this->hash_id . '" src="' . $src . '"></script>';
    }

    protected function getAppearanceAttr($value)
    {
        $default = [
            'window' => [
                'tone'         => '#3c60ff',
                'sideMargin'   => 80,
                'bottomMargin' => 48,
            ],
            'button' => [
                'hidden'       => false,
                'color'        => '#333333',
                'background'   => '#FFFFFF',
                'size'         => 'normal',
                'sideMargin'   => 24,
                'bottomMargin' => 48,
            ],
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getFeaturesAttr($value)
    {
        $default = [
            'gotop' => [
                'enable' => false,
            ],
            'doc'   => [
                'enable' => false,
            ],
            'chat'  => [
                'enable' => false,
            ],
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getCustomsAttr($value)
    {
        $default = [];
        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getBroadcastAttr($value)
    {
        $default = [
            'enable' => false,
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getRobotAttr($value)
    {
        $default = [
            'enable' => false,
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getIsStandardAttr()
    {
        return $this->isStandard();
    }

    public function isStandard()
    {
        return $this->expire_time && $this->expire_time->gt(Date::now());
    }

    protected function getStatusAttr($status)
    {
        if ($status == 1) {
            $date   = Date::now()->subHours(24);
            $events = $this->events()->where('create_time', '>=', $date)->count('distinct session_id');
            if ($events == 0) {
                $status = 2;
            }
        }
        return $status;
    }
}
