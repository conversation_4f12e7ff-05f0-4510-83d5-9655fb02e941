<?php

namespace app\model;

use app\enum\Product;
use app\lib\Date;
use app\lib\Hashids;
use Exception;
use think\model\Pivot;

/**
 * Class app\model\CpsUser
 *
 * @property \app\lib\Date $create_time
 * @property int $partner_id
 * @property int $user_id
 * @property-read \app\model\Partner $partner
 */
class PartnerUser extends Pivot
{
    protected $type = [
        'create_time' => Date::class,
    ];

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public static function association($key, User $user)
    {
        try {
            $partner = Partner::findOrFail(Hashids::decode($key));
            if ($partner->user_id != $user->id) {
                $partner->customers()->attach($user);
            }
        } catch (Exception) {

        }
    }

    public function getRebate(Charge $charge)
    {
        $product = null;
        if ($order = $charge->ord) {
            $product = $order->goods->getProduct();
        } elseif ($application = $charge->application) {
            $product = Product::tryFrom($application->product);
        }

        $rebate = match ($product) {
            Product::Assistant, Product::Wiki, Product::Bot, Product::Q => 15,
            Product::Api, Product::Ssl => 10,
            Product::Sms, Product::Ai, Product::Chat => 5,
            default => 0
        };

        return floor($charge->net_amount * $rebate / 100);
    }
}
