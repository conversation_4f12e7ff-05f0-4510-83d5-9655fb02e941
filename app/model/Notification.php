<?php

namespace app\model;

use app\lib\Date;
use League\CommonMark\ConverterInterface;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Environment\EnvironmentInterface;
use League\CommonMark\Extension\CommonMark\Node\Inline\Link;
use League\CommonMark\Extension\DefaultAttributes\DefaultAttributesExtension;
use League\CommonMark\Extension\InlinesOnly\InlinesOnlyExtension;
use League\CommonMark\MarkdownConverter;
use League\CommonMark\Node\Block\Document;
use League\CommonMark\Node\StringContainerInterface;
use League\CommonMark\Output\RenderedContent;
use League\CommonMark\Output\RenderedContentInterface;
use League\CommonMark\Parser\MarkdownParser;
use League\CommonMark\Parser\MarkdownParserInterface;
use League\CommonMark\Renderer\DocumentRendererInterface;
use think\facade\Log;
use think\Model;

/**
 * Class app\model\Notification
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $application_id
 * @property int $from_id
 * @property int $id
 * @property int $to_id
 * @property string $channel
 * @property string $content
 * @property string $read_time
 * @property string $url
 * @property-read \app\model\User $from
 * @property-read mixed $avatar
 * @property-read mixed $text
 */
class Notification extends Model
{
    protected $append = ['avatar'];

    public function from()
    {
        return $this->belongsTo(User::class, 'from_id');
    }

    protected function getAvatarAttr()
    {
        if ($this->from_id) {
            return $this->from->avatar;
        }
        return (string) main_url(asset('images/avatar.svg'));
    }

    protected function getContentAttr($value)
    {
        $config = [
            'default_attributes' => [
                Link::class => [
                    'target' => '_blank',
                ],
            ],
        ];

        $environment = new Environment($config);

        // Add the extension
        $environment->addExtension(new DefaultAttributesExtension());
        $environment->addExtension(new InlinesOnlyExtension());

        // Instantiate the converter engine and start converting some Markdown!
        $converter = new MarkdownConverter($environment);
        return (string) $converter->convert($value);
    }

    protected function getTextAttr()
    {
        // Create a new, empty environment
        $environment = new Environment();

        // Add this extension
        $environment->addExtension(new InlinesOnlyExtension());

        // Instantiate the converter engine and start converting some Markdown!
        $converter = new class($environment) implements ConverterInterface {
            /** @psalm-readonly */
            private MarkdownParserInterface $parser;

            /** @psalm-readonly */
            private DocumentRendererInterface $renderer;

            public function __construct(EnvironmentInterface $environment)
            {
                $this->parser   = new MarkdownParser($environment);
                $this->renderer = new class($environment) implements DocumentRendererInterface {
                    private EnvironmentInterface $environment;

                    public function __construct(EnvironmentInterface $environment)
                    {
                        $this->environment = $environment;
                    }

                    public function renderDocument(Document $document): RenderedContentInterface
                    {
                        $text   = '';
                        $walker = $document->walker();
                        while ($event = $walker->next()) {
                            $node = $event->getNode();

                            if ($event->isEntering()) {
                                if ($node instanceof StringContainerInterface) {
                                    $text .= $node->getLiteral();
                                }
                            }
                        }

                        return new RenderedContent($document, $text);
                    }
                };
            }

            public function convert(string $input): RenderedContentInterface
            {
                return $this->renderer->renderDocument($this->parser->parse($input));
            }
        };
        return (string) $converter->convert($this->getData('content'));
    }

    public static function getWeaccount()
    {
        $weaccount = WxaWeaccount::where('app_id', env('NOTIFY_APPID'))->where('status', 1)->find();

        $application = app(\app\lib\wechat\wxa\Application::class);

        return $application->getAccountAppWithRefreshToken($weaccount->app_id, $weaccount->refresh_token);
    }

    public static function notify($data, ?Application $application = null)
    {
        $notification = Notification::create([
            'to_id'          => $data['to_id'],
            'content'        => $data['content'],
            'from_id'        => $data['from_id'] ?? 0,
            'channel'        => $data['channel'] ?? null,
            'url'            => $data['url'] ?? null,
            'application_id' => $application?->id ?? 0,
        ]);

        $app     = $data['app'] ?? $application?->getAttr('name');
        $message = $data['message'] ?? $notification->text;

        //推送微信通知
        try {
            $account = UserWeaccount::where('user_id', $data['to_id'])->find();
            if (!empty($account) && !empty($account->openid)) {
                $weaccount = Notification::getWeaccount();

                $weaccount->getClient()->post('/cgi-bin/message/template/send', ['json' => [
                    'touser'        => $account->openid,
                    'template_id'   => env('NOTIFY_TEMPLATE'),
                    'client_msg_id' => $notification->id,
                    'data'          => [
                        'keyword1' => [
                            'value' => "【" . ($app ?? '顶想云') . "】{$message}",
                        ],
                        'keyword2' => [
                            'value' => (string) Date::now(),
                        ],
                    ],
                ]]);
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        //发送短信通知
        if (!empty($data['sms'])) {
            try {
                if (is_string($data['sms'])) {
                    $message = $data['sms'];
                }
                /** @var \app\model\User $user */
                $user = User::where('id', $data['to_id'])->find();
                if ($user) {
                    $content = "【顶想云】{$message}";
                    if (!empty($app)) {
                        $content .= " ---{$app}";
                    }
                    \app\facade\Sms::send($user->mobile, $content);
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
