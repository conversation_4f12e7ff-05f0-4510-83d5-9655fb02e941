<?php

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\AssistantSiteFeedback
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $site_id
 * @property int $status
 * @property string $delete_time
 * @property string $distribution
 * @property string $name
 * @property string $question
 * @property-read \app\model\AssistantSiteFeedbackEntry[] $entries
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class AssistantSiteFeedback extends Model
{
    use SoftDelete;

    public function entries()
    {
        return $this->hasMany(AssistantSiteFeedbackEntry::class, 'feedback_id');
    }

    protected function getQuestionAttr($value)
    {
        $default = [
            'title'    => '您如何评价当前的体验？',
            'reason'   => [
                'enable'     => true,
                'text'       => '我们会珍惜你留下的每一个反馈和建议',
                'screenshot' => false,
            ],
            'complete' => '感谢您的评价！',
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }

    protected function getDistributionAttr($value)
    {
        $default = [
            'timing'   => [
                'handle' => 'visit',
            ],
            'strategy' => [
                'enable' => true,
                'rate'   => 'mild',
            ],
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }
}
