<?php

namespace app\model\ai;

use app\lib\Hashids;
use Exception;
use think\helper\Arr;

/**
 * Class app\model\ai\Plugin
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $api
 * @property array $auth
 * @property array $config
 * @property int $id
 * @property int $sort
 * @property int $status
 * @property int $user_id
 * @property string $description
 * @property string $icon
 * @property string $schema
 * @property string $title
 * @property string $type
 * @property-read mixed $name
 * @property-read mixed $tools
 */
class Plugin extends Model
{
    protected $type = [
        'config' => 'json',
        'auth'   => 'json',
        'api'    => 'json',
    ];

    protected function getToolsAttr()
    {
        return $this->getTools();
    }

    protected function getNameAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getTypeAttr($type)
    {
        return match ($type) {
            1, '1' => 'OpenApi',
            2, '2' => 'ThinkA<PERSON>',
            default => $type,
        };
    }

    public static function getByName($name)
    {
        $id = Hashids::decode($name);
        return self::where('status', 1)->findOrFail($id);
    }

    public function getTools()
    {
        try {
            $type     = $this->getAttr('type');
            $provider = new ("app\lib\llm\plugin\\{$type}")($this->config);

            return $provider->getTools();
        } catch (\Throwable) {
            return [];
        }
    }

    /**
     * @param $name
     * @return \app\lib\llm\Tool
     */
    public function getTool($name)
    {
        $tools = $this->getTools();

        $tool = Arr::first($tools, function ($tool) use ($name) {
            return $tool->getName() == $name;
        });

        if ($tool) {
            return $tool;
        }
        throw new Exception("{$name} tool not found");
    }
}
