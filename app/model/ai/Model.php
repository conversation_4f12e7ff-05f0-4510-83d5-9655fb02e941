<?php

namespace app\model\ai;

/**
 * Class app\model\ai\Model
 *
 * @property array $params
 * @property int $id
 * @property int $status
 * @property string $channel
 * @property string $checkpoint
 * @property string $code
 * @property string $description
 * @property string $factor
 * @property string $label
 * @property string $sort
 * @property string $type
 * @property string $version
 */
class Model extends \think\Model
{
    const STATUS_HIDE      = 2;
    const STATUS_RECOMMEND = 4;

    protected $type = [
        'params' => 'json',
    ];

    public function getFactor($method)
    {
        $factor = $this->getAttr('factor');
        switch ($this->getAttr('type')) {
            case 'image':
            case 'text':
                return $factor[$method] ?? null;
        }
        return $factor;
    }

    protected function getFactorAttr($factor)
    {
        switch ($this->getAttr('type')) {
            case 'image':
                if (is_numeric($factor)) {
                    return ['generations' => $factor];
                }
                return json_decode($factor, true);
            case 'text':
                if (is_numeric($factor)) {
                    return ['embedding' => $factor];
                }
                return json_decode($factor, true);
        }

        return $factor;
    }

    protected function setFactorAttr($factor)
    {
        switch ($this->getAttr('type')) {
            case 'image':
            case 'text':
                return json_encode($factor);
        }

        return $factor;
    }

    protected function getCheckpointAttr()
    {
        if (!empty($this->version)) {
            return $this->version;
        }
        return $this->code;
    }

}
