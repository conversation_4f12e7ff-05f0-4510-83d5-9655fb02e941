<?php

namespace app\model\ai;

use app\lib\StaticData;
use app\model\Application;
use app\model\LicenseChat;
use app\model\User;
use think\Model;
use think\swoole\coroutine\Context;

/**
 * Class app\model\ai\Log
 *
 * @property-read int $total
 * @property-read int $user_id
 * @property-read int $user_type
 * @property-read string $model
 */
class Log extends Model
{
    use StaticData;

    const TYPE_USER         = 0;
    const TYPE_APPLICATION  = 1;
    const TYPE_LICENSE_CHAT = 2;
    const TYPE_LICENSE_BOT  = 3;
    const TYPE_LICENSE_DOC  = 4;

    protected $connection = 'clickhouse';

    public function getUsername()
    {
        return Context::rememberData(sprintf('ai_log_username_%u_%u', $this->user_type, $this->user_id), function () {
            switch ($this->user_type) {
                case self::TYPE_USER:
                    $user = User::find($this->user_id);
                    return $user?->getAttr('name') ?? '未知用户';
                case self::TYPE_APPLICATION:
                    $application = Application::find($this->user_id);
                    return $application?->getAttr('name') ?? '未知应用';
                case self::TYPE_LICENSE_CHAT:
                    $license = LicenseChat::find($this->user_id);
                    return $license?->getAttr('name') ?? '未知应用';
                default:
                    return '未知';
            }
        });
    }

    public function getModelName()
    {
        $type = $this->getAttr('type');
        return Context::rememberData(sprintf('ai_log_model_name_%s_%s', $type, $this->model), function () use ($type) {
            switch ($type) {
                case 'chat':
                case 'image':
                case 'text':
                case 'video':
                case 'audio':
                    $model = \app\model\ai\Model::where('code', $this->model)->where('type', $type)->find();
                    return $model?->getAttr('label') ?? '未知模型';
                default:
                    return '未知模型';
            }
        });
    }
}
