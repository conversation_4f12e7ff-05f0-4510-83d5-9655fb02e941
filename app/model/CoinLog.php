<?php

namespace app\model;

use think\Exception;
use think\Model;

/**
 * Class app\model\CoinLog
 *
 * @property int $application_id
 * @property int $id
 * @property int $number
 * @property int $source_id
 * @property int $type
 * @property int $user_id
 * @property string $create_time
 * @property string $info
 * @property string $source_type
 * @property-read \app\model\User $user
 * @property-read mixed $source
 */
class CoinLog extends Model
{
    const TYPE_INC = 1;
    const TYPE_DEC = 2;

    public static function onBeforeDelete(self $log)
    {
        if ($log->getAttr('type') === self::TYPE_INC) {
            if ($log->user->coin < $log->number) {
                throw new Exception('用户云币不足！');
            }
            $log->user->dec('coin', $log->number)->save();
        } else {
            $log->user->inc('coin', $log->number)->save();
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function source()
    {
        return $this->morphTo();
    }
}
