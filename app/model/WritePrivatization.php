<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\WritePrivatization
 *
 * @property \Carbon\Carbon $expire_time
 * @property int $id
 * @property int $user_id
 * @property string $client_id
 * @property string $client_secret
 * @property string $create_time
 * @property string $name
 * @property string $remark
 * @property string $type
 * @property string $update_time
 * @property string $url
 */
class WritePrivatization extends Model
{
    protected $type = [
        'expire_time' => Date::class,
    ];
}
