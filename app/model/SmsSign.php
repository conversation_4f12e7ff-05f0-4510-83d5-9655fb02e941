<?php

namespace app\model;

use app\lib\Hashids;
use app\lib\submail\Exception;
use app\notification\Message;
use think\exception\ValidateException;
use think\facade\Cache;
use think\facade\Filesystem;
use think\helper\Arr;
use think\helper\Str;
use think\Model;
use think\model\concern\SoftDelete;
use yunwuxin\facade\Notification;

/**
 * Class app\model\SmsSign
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $company
 * @property int $id
 * @property int $status
 * @property int $user_id
 * @property string $delete_time
 * @property string $error
 * @property string $name
 * @property string $proof
 * @property string $remark
 * @property string $source
 * @property-read \app\model\SmsTemplate[] $templates
 * @property-read \app\model\User $user
 * @property-read mixed $hash_id
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class SmsSign extends Model
{
    use SoftDelete;

    const CACHE_KEY = 'sms_sign_%s';

    protected $append = ['hash_id'];

    protected $type = [
        'company' => 'json',
    ];

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(self::CACHE_KEY, $model->hash_id));
    }

    public static function getById(User $user, $id): self
    {
        $decoded = is_numeric($id) ? $id : Hashids::decode($id, must: false);

        if ($decoded) {
            $sign = SmsSign::where('id', $decoded)
                ->cacheAlways(sprintf(self::CACHE_KEY, $decoded), 7 * 24 * 3600)
                ->find();
        }

        if (empty($sign) || $sign->status != 1 || $sign->user_id != $user->id) {
            throw new ValidateException("签名[{$id}]不存在或已被冻结");
        }

        return $sign;
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function templates()
    {
        return $this->hasMany(SmsTemplate::class, 'sign_id');
    }

    public function submit()
    {
        //提交报备
        $attachments = [];

        $company = $this->company;
        if (empty($company)) {
            $company = [
                'name'        => $this->user->enterprise->getAttr('name'),
                'identity'    => $this->user->enterprise->getAttr('identity'),
                'corporation' => $this->user->enterprise->getAttr('corporation'),
            ];

            $attachments[] = $this->getImageContent($this->user->enterprise->getAttr('img'));
        } elseif (!empty($company['img'])) {
            $attachments[] = $this->getImageContent($company['img']);
        }

        if (!empty($this->proof)) {
            $attachments[] = $this->getImageContent($this->proof);
        }

        $name = $this->getAttr('name');

        try {
            $source = match ($this->getAttr('source')) {
                '企事业单位名' => 0,
                '已注册商标名' => 1,
                '已备案APP' => 2,
                default => throw new Exception('未知签名来源'),
            };
            \app\facade\Sms::addSign($name, $company, $attachments, $source);

            $this->save([
                'status' => 3,
            ]);
        } catch (Exception $e) {
            if ($e->getCode() == 127) {
                //签名已存在
                $data = \app\facade\Sms::getSign($name);

                $status = Arr::get($data, 'status');
                switch ($status) {
                    case 0:
                        $this->save([
                            'status' => 3,
                        ]);
                        break;
                    case 1:
                        $this->pass();
                        break;
                    default:
                        throw $e;
                }
            } else {
                throw $e;
            }
        }
    }

    protected function getImageContent($path)
    {
        if (!Str::startsWith($path, 'http')) {
            $disk = Filesystem::disk('uploads');
            $path = $disk->path($path);
        }
        return file_get_contents($path);
    }

    public function pass()
    {
        $this->save(['status' => 1]);

        Notification::send($this->user_id, new Message("您提交的短信签名“{$this->getAttr('name')}”已审核通过。"));

        Sms::callWebhook($this->user_id, [
            'action' => 'sign',
            'sign'   => $this->toData(),
        ]);
    }

    public function reject($reason)
    {
        $this->save([
            'status' => -1,
            'error'  => $reason,
        ]);

        Notification::send($this->user_id, new Message("您提交的短信签名“{$this->getAttr('name')}”审核未通过。"));

        Sms::callWebhook($this->user_id, [
            'action' => 'sign',
            'sign'   => $this->toData(),
        ]);
    }

    public function toData()
    {
        return [
            'id'      => $this->hash_id,
            'name'    => $this->name,
            'source'  => $this->source,
            'company' => $this->company,
            'proof'   => $this->proof,
            'status'  => $this->status,
            'error'   => $this->error,
        ];
    }
}
