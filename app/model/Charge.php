<?php

namespace app\model;

use app\job\ChargeCompleteJob;
use app\job\ChargeRevokeJob;
use app\lib\Date;
use app\lib\Hashids;
use Carbon\Carbon;
use Exception;
use think\db\Query;
use think\exception\ValidateException;
use think\Model;
use yunwuxin\pay\entity\PurchaseResponse;
use yunwuxin\pay\entity\PurchaseResult;
use yunwuxin\pay\interfaces\Payable;
use yunwuxin\pay\Payment;
use yunwuxin\pay\traits\PayableModel;
use function yunwuxin\pay\generate_no;

/**
 * Class app\model\Charge
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $pay_time
 * @property \app\lib\Date $revoke_time
 * @property \app\lib\Date $update_time
 * @property array $raw
 * @property bool $is_paid
 * @property int $amount
 * @property int $application_id
 * @property int $coin
 * @property int $discount
 * @property int $id
 * @property int $invoice_id 发票
 * @property int $money
 * @property int $order_id
 * @property int $promo_id
 * @property int $status
 * @property int $user_id
 * @property string $channel
 * @property string $client_id
 * @property string $extra
 * @property string $notify_url
 * @property string $order_no
 * @property string $out_trade_no
 * @property string $return_url
 * @property string $revoke_url
 * @property string $subject
 * @property string $trade_no
 * @property-read \app\model\Application $application
 * @property-read \app\model\CoinLog $coin_log
 * @property-read \app\model\CommissionLog $commission_log
 * @property-read \app\model\MoneyLog $money_log
 * @property-read \app\model\Order $ord
 * @property-read \app\model\Promo $promo
 * @property-read \app\model\Refund $refund
 * @property-read \app\model\User $user
 * @property-read mixed $application_name
 * @property-read mixed $can_revoke
 * @property-read mixed $full_subject
 * @property-read mixed $hash_id
 * @property-read mixed $net_amount
 * @property-read mixed $pay_url
 * @method static \think\db\Query|self invoiceable()
 */
class Charge extends Model implements Payable
{
    use PayableModel;

    protected $hidden = ['notify_url', 'return_url', 'revoke_url', 'extra'];

    protected $append = ['pay_url', 'full_subject', 'net_amount'];

    protected $type = [
        'raw'         => 'json',
        'pay_time'    => Date::class,
        'revoke_time' => Date::class,
    ];

    public function application()
    {
        return $this->belongsTo(Application::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function ord()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function coinLog()
    {
        return $this->morphOne(CoinLog::class, 'source');
    }

    public function moneyLog()
    {
        return $this->morphOne(MoneyLog::class, 'source');
    }

    public function commissionLog()
    {
        return $this->morphOne(CommissionLog::class, 'source');
    }

    public function promo()
    {
        return $this->belongsTo(Promo::class);
    }

    /**
     * 可开票的
     * @param Query $query
     */
    public function scopeInvoiceable(Query $query)
    {
        $query
            ->where('invoice_id', 0)
            ->where('amount', '>', 0)
            ->where('is_paid', 1)
            ->where('status', 1)
            ->whereNull('revoke_time');
    }

    protected function getApplicationNameAttr()
    {
        if ($this->application) {
            return $this->application->getAttr('name');
        }
        return '顶想云';
    }

    protected function getFullSubjectAttr()
    {
        return "[{$this->application_name}] {$this->subject}";
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    protected function getPayUrlAttr()
    {
        return (string) main_url('/cashier?id=' . $this->hash_id);
    }

    protected function getNetAmountAttr()
    {
        return $this->amount - $this->discount;
    }

    public function getReturnUrl()
    {
        return $this->pay_url;
    }

    public function getAmount()
    {
        return $this->net_amount - $this->coin - $this->money;
    }

    public function isComplete()
    {
        return $this->is_paid;
    }

    public function pay($channel, $gateway = null)
    {
        $amount = $this->getAmount();
        if ($amount < 0) {
            throw new ValidateException('非法请求');
        } elseif ($amount > 0) {
            $this->save([
                'channel' => $channel,
            ]);

            if ($channel === 'transfer') {
                return new PurchaseResponse(
                    [
                        'trade_no' => $this->trade_no,
                        'company'  => '上海顶想信息科技有限公司',
                        'bank'     => '上海银行徐汇支行',
                        'account'  => '***********',
                        'amount'   => $this->getAmount(),
                    ],
                    'transfer'
                );
            }

            return $this->invoke(function (Payment $payment) use ($channel, $gateway) {
                return $payment->channel($channel)->gateway($gateway)->purchase($this);
            });
        } else {
            //无需支付
            $this->doComplete();
            return new PurchaseResponse(null, 'none');
        }
    }

    protected function doComplete($data = [])
    {
        $this->save([
            'status'   => 2,
            'is_paid'  => 1,
            'pay_time' => Carbon::now(),
            ...$data,
        ]);

        //返利等任务
        queue(new ChargeCompleteJob($this));
    }

    /**
     * 查看订单信息
     * @param string $channel
     */
    public function check($channel = null)
    {
        $force = true;
        if (!$channel) {
            $channel = $this->channel;
            $force   = false;
        }
        if (empty($channel) || $channel === 'transfer') {
            return;
        }
        if (!$this->isComplete()) {
            try {
                try {
                    $result = $this->queryPayResult($channel);
                } catch (Exception $e) {
                    throw new ValidateException($e->getMessage());
                }

                if (!$result->isPaid()) {
                    throw new ValidateException('尚未支付成功');
                }

                $this->onComplete($result);
            } catch (ValidateException $e) {
                if ($force) {
                    throw $e;
                }
            }
        }
    }

    /**
     * 确认企业支付
     */
    public function confirm()
    {
        if (!$this->isComplete()) {
            $result = new PurchaseResult('transfer', '', $this->getAmount(), true, Carbon::now(), []);

            $this->onComplete($result);
        }
    }

    public function getRebate()
    {

    }

    public function onComplete(PurchaseResult $result)
    {
        if ($result->isPaid()) {
            $this->doComplete([
                'channel'      => $result->getChannel(),
                'out_trade_no' => $result->getTradeNo(),
                'raw'          => $result->getRaw(),
            ]);
        }
    }

    public function refund()
    {
        return $this->hasOne(Refund::class);
    }

    protected function getCanRevokeAttr()
    {
        try {
            $this->ensureCanRevoke();
            if ($this->status == 0) {
                //处理失败的订单 可撤销
                return true;
            }
            if ($this->ord) {
                return $this->ord->canRevoke();
            }

            return !empty($this->revoke_url);
        } catch (Exception) {
            return false;
        }
    }

    /**
     * 确认可撤销
     * @throws Exception
     */
    protected function ensureCanRevoke()
    {
        if (!$this->is_paid) {
            throw new Exception('订单尚未支付，无需撤销');
        }

        if ($this->status == 2) {
            throw new Exception('订单尚未处理完成，不能撤销');
        }

        if ($this->revoke_time) {
            throw new Exception('该订单已撤销');
        }
    }

    /**
     * 撤销
     * @throws Exception
     */
    public function revoke()
    {
        $this->transaction(function () {

            $this->ensureCanRevoke();

            //发起退款
            /** @var Refund $refund */
            $refund = $this->refund()->save([
                'refund_no' => generate_no(),
                'status'    => 2,
            ]);

            //执行退款
            if ($this->channel !== 'transfer' && $this->getAmount() > 0) {
                //对公转账或无需支付的的不退款
                try {
                    $refund->refund();
                } catch (Exception $e) {
                    throw new ValidateException($e->getMessage());
                }
            }

            //执行回调
            queue(new ChargeRevokeJob($refund));

            $this->save([
                'revoke_time' => Date::now(),
            ]);
        });
    }
}
