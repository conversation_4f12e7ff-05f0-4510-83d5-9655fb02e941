<?php

namespace app\model;

use think\Model;
use think\swoole\coroutine\Context;

/**
 * Class app\model\ApiLog
 *
 * @property \app\lib\Date $create_time
 * @property bool $success
 * @property int $api_id
 * @property int $id
 * @property int $nums
 * @property int $user_id
 * @property string $path
 * @property-read \app\model\Api $api
 */
class ApiLog extends Model
{
    protected $connection = 'clickhouse';

    public function api()
    {
        return $this->belongsTo(Api::class);
    }

    public function getUsername()
    {
        return Context::rememberData(sprintf('api_log_username_%u', $this->user_id), function () {
            $user = User::find($this->user_id);
            return $user?->getAttr('name') ?? '未知用户';
        });
    }

    public function getApiName()
    {
        return Context::rememberData(sprintf('api_log_api_name_%u', $this->api_id), function () {
            $api = Api::find($this->api_id);
            return $api?->getAttr('title') ?? '未知接口';
        });
    }

}
