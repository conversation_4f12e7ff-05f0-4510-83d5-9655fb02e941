<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\SslCertificate
 *
 * @property \app\lib\Date $expire_time
 * @property int $id
 * @property int $status
 * @property int $user_id
 * @property string $brand
 * @property string $certificate
 * @property string $create_time
 * @property string $domain
 * @property string $key
 * @property string $name
 * @property string $type
 * @property string $update_time
 * @property-read \app\model\User $user
 */
class SslCertificate extends Model
{

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getInfo()
    {
        return \Spatie\SslCertificate\SslCertificate::createFromString($this->certificate);
    }
}
