<?php

namespace app\model;

use app\facade\Sms;
use app\lib\Hashids;
use think\exception\ValidateException;
use think\facade\Cache;
use think\Model;
use think\model\concern\SoftDelete;

/**
 * Class app\model\SmsTemplate
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $sign_id
 * @property int $status
 * @property int $type 类型：1-通知，2-营销
 * @property int $user_id
 * @property string $content
 * @property string $delete_time
 * @property string $error
 * @property string $name
 * @property string $out_id
 * @property string $remark
 * @property-read \app\model\SmsSign $sign
 * @property-read \app\model\User $user
 * @property-read mixed $hash_id
 * @method static \think\db\Query onlyTrashed()
 * @method static \think\db\Query withTrashed()
 */
class SmsTemplate extends Model
{
    use SoftDelete;

    const CACHE_KEY = 'sms_template_%s';

    protected $append = ['hash_id'];

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(self::CACHE_KEY, $model->hash_id));
    }

    public static function getById(User $user, $id): self
    {
        $decoded = is_numeric($id) ? $id : Hashids::decode($id, must: false);

        if ($decoded) {
            $template = SmsTemplate::where('id', $decoded)
                ->cacheAlways(sprintf(self::CACHE_KEY, $decoded), 7 * 24 * 3600)
                ->find();
        }

        if (empty($template) || $template->status != 1 || !in_array($template->user_id, [0, $user->id])) {
            throw new ValidateException("模板[{$id}]不存在或已被冻结");
        }

        return $template;
    }

    public static function isMarketing($content)
    {
        return !!preg_match('/(?=.*退订)(?=.*[Tt])|(?=.*拒收)(?=.*[Rr])/', $content);
    }

    protected function getTypeAttr($type)
    {
        return ($type != 2) ? 1 : $type;
    }

    protected function getStatusAttr($status)
    {
        if ($status == 0) {
            try {
                $template = Sms::getTemplate($this->out_id);
                $data     = [];
                if ($template['template_status'] == 2) {
                    $data['status'] = $status = 1;
                }
                if ($template['template_status'] == 3) {
                    $data['status'] = $status = -1;
                    $data['error'] = $template['template_reject_reson'];
                }

                if (!empty($data)) {
                    $this->save($data);
                }
            } catch (\Exception) {

            }
        }

        return $status;
    }

    public function sign()
    {
        return $this->belongsTo(SmsSign::class, 'sign_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    protected function getHashIdAttr()
    {
        return Hashids::encode($this->id);
    }

    public function toData()
    {
        return [
            'id'      => $this->hash_id,
            'sign_id' => $this->sign->hash_id,
            'name'    => $this->name,
            'type'    => $this->getAttr('type'),
            'content' => $this->content,
            'status'  => $this->status,
            'error'   => $this->error,
        ];
    }
}
