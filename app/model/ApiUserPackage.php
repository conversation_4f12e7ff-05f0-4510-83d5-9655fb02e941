<?php

namespace app\model;

use app\lib\Date;
use think\facade\Cache;
use think\Model;

/**
 * Class app\model\ApiUserPackage
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $agency_price
 * @property int $api_id
 * @property int $id
 * @property int $price
 * @property int $status
 * @property int $total_nums
 * @property int $used_nums
 * @property int $user_id
 * @property string $name
 * @property-read \app\model\Api $api
 * @property-read \app\model\User $user
 * @property-read mixed $nums
 */
class ApiUserPackage extends Model
{
    const USED_NUMS_KEY = 'api_package_used_nums_%u';

    protected $append = ['nums', 'expire_time'];

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(ApiUser::PACKAGES_KEY, $model->api_id, $model->user_id));
    }

    public static function onAfterDelete(self $model): void
    {
        Cache::delete(sprintf(ApiUser::PACKAGES_KEY, $model->api_id, $model->user_id));
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function api()
    {
        return $this->belongsTo(Api::class);
    }

    public function isExpired()
    {
        return $this->expire_time->isPast();
    }

    public function isAvailable()
    {
        return !$this->isExpired() && $this->status > 0 && $this->nums > 0;
    }

    protected function getExpireTimeAttr()
    {
        return $this->create_time->addYears(2);
    }

    protected function getNumsAttr()
    {
        return max($this->total_nums - $this->used_nums, 0);
    }

    protected function getUsedNumsAttr($nums)
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);

        $cached = Cache::get($key);

        if ($cached == null) {
            Cache::set($key, $nums);
            return $nums;
        }
        return $cached;
    }

    public function saveUsedNums()
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);
        if (Cache::has($key)) {
            $usedNums = Cache::get($key);
            $this->save([
                'used_nums' => $usedNums,
            ]);
        }
    }

    public function incUsedNums()
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);
        Cache::inc($key);
    }

    public function decUsedNums()
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);
        Cache::dec($key);
    }

    public function syncUsedNums()
    {
        $archiveNums  = ApiUserPackageUsage::where('package_id', $this->id)->sum('nums');
        $archiveMonth = ApiUserPackageUsage::where('package_id', $this->id)->max('month', false);

        $logQuery = ApiLog::where('api_id', $this->api_id)
            ->where('success', 1);

        if ($this->status == 2) {
            $logQuery->where(function ($query) {
                $query->where('package_id', $this->id);
                $query->whereOr(function ($query) {
                    $query->where('package_id', 0);
                    $query->where('user_id', $this->user_id);
                });
            });
        } else {
            $logQuery->where('package_id', $this->id);
        }

        if ($archiveMonth) {
            $logQuery->where('create_time', '>', Date::createFromFormat('!Ym', $archiveMonth)->endOfMonth());
        }

        $logNums = $logQuery->count();

        $toNums   = ApiUserPackageAmend::where('to_id', $this->id)->sum('nums');
        $fromNums = ApiUserPackageAmend::where('from_id', $this->id)->sum('nums');

        $usedNums = $archiveNums + $logNums + $toNums - $fromNums;

        if ($usedNums != $this->used_nums) {
            Cache::set(sprintf(self::USED_NUMS_KEY, $this->id), $usedNums);
            $this->save(['used_nums' => $usedNums]);
        }
    }

    /**
     * 日志归档
     * @param $month
     * @return int
     */
    public function archive($month = null)
    {
        if (!$month) {
            $month = Date::now()->subMonth()->format('Ym');
        }

        $startTime = Date::createFromFormat('!Ym', $month)->startOfMonth();
        $endTime   = Date::createFromFormat('!Ym', $month)->endOfMonth();

        $numsQuery = ApiLog::where('api_id', $this->api_id)
            ->whereBetween('create_time', [$startTime, $endTime])
            ->where('success', 1);

        if ($this->status == 2) {
            $numsQuery->where(function ($query) {
                $query->where('package_id', $this->id);
                $query->whereOr(function ($query) {
                    $query->where('package_id', 0);
                    $query->where('user_id', $this->user_id);
                });
            });
        } else {
            $numsQuery->where('package_id', $this->id);
        }

        $nums = $numsQuery->count();

        if ($nums > 0) {
            ApiUserPackageUsage::create([
                'package_id' => $this->id,
                'month'      => $month,
                'nums'       => $nums,
            ], replace: true);
        }

        return $nums;
    }

    protected function setPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getPriceAttr($price)
    {
        return $price / 100;
    }

    protected function setAgencyPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getAgencyPriceAttr($price)
    {
        return $price / 100;
    }
}
