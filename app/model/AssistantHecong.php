<?php

namespace app\model;

use app\lib\Date;
use app\lib\Hecong;
use think\Model;

/**
 * Class app\model\AssistantHecong
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $ent_id
 * @property int $id
 * @property int $seat
 * @property int $user_id
 * @property string $plan
 * @property-read \app\model\User $user
 */
class AssistantHecong extends Model
{
    protected $type = [
        'expire_time' => Date::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isExpired()
    {
        return $this->expire_time->lt(Date::now());
    }

    public function open($life, $places)
    {
        $this->invoke(function (Hecong $hecong) use ($life, $places) {
            $hecong->open($this->ent_id, $life, $places);

            $now   = Date::now();
            $start = $this->expire_time->lt($now) ? $now : $this->expire_time;

            $this->save([
                'plan'        => 'standard',
                'seat'        => $places,
                'expire_time' => $start->addYears($life),
            ]);
        });
    }

    public function renew($life)
    {
        $this->invoke(function (Hecong $hecong) use ($life) {
            $hecong->renew($this->ent_id, $life);
            $now   = Date::now();
            $start = $this->expire_time->lt($now) ? $now : $this->expire_time;

            $this->save([
                'expire_time' => $start->addYears($life),
            ]);
        });
    }
}
