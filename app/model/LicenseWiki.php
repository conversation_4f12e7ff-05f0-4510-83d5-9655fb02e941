<?php

namespace app\model;

use app\enum\LicensePlan;
use app\lib\Date;
use think\Model;

/**
 * Class app\model\LicenseDoc
 *
 * @property \app\enum\LicensePlan $plan
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $partner_id
 * @property int $token
 * @property int $user_id
 * @property string $name
 * @property string $url
 * @property-read \app\model\AccessToken $access_token
 * @property-read \app\model\Partner $partner
 * @property-read \app\model\User $user
 * @property-read mixed $code
 */
class LicenseWiki extends Model
{
    protected $type = [
        'expire_time' => Date::class,
        'plan'        => LicensePlan::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function accessToken()
    {
        return $this->morphOne(AccessToken::class, 'accessible');
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->lt(Date::now());
    }

    protected function getCodeAttr()
    {
        return $this->access_token?->token;
    }
}
