<?php

namespace app\model;

use app\enum\LicensePlan;
use app\lib\Date;
use think\Model;

/**
 * Class app\model\Partner
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $commission
 * @property int $id
 * @property int $license
 * @property int $user_id
 * @property-read \app\model\CommissionLog[] $commission_logs
 * @property-read \app\model\LicenseBot[] $bots
 * @property-read \app\model\LicenseChat[] $chats
 * @property-read \app\model\LicenseAi[] $ais
 * @property-read \app\model\LicenseWiki[] $wikis
 * @property-read \app\model\PartnerSettlement[] $settlements
 * @property-read \app\model\Promo[] $promos
 * @property-read \app\model\User $user
 * @property-read \app\model\User[] $customers
 * @property-read mixed $fee
 * @property-read mixed $settle_commission
 */
class Partner extends Model
{
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function customers()
    {
        return $this->belongsToMany(User::class, PartnerUser::class, 'user_id', 'partner_id');
    }

    public function promos()
    {
        return $this->hasMany(Promo::class);
    }

    public function settlements()
    {
        return $this->hasMany(PartnerSettlement::class);
    }

    public function wikis()
    {
        return $this->hasMany(LicenseWiki::class);
    }

    public function bots()
    {
        return $this->hasMany(LicenseBot::class);
    }

    public function chats()
    {
        return $this->hasMany(LicenseChat::class);
    }

    public function ais()
    {
        return $this->hasMany(LicenseAi::class);
    }

    public function commissionLogs()
    {
        return $this->hasMany(CommissionLog::class);
    }

    protected function getFeeAttr()
    {
        if ($this->license > 0) {
            return [
                'token' => LicensePlan::Fee['token'],
                'wiki'  => floor(LicensePlan::Fee['wiki'] * $this->license / 100),
                'bot'   => floor(LicensePlan::Fee['bot'] * $this->license / 100),
                'chat'  => floor(LicensePlan::Fee['chat'] * $this->license / 100),
                'ai'    => floor(LicensePlan::Fee['ai'] * $this->license / 100),
            ];
        }

        return null;
    }

    protected function getSettleCommissionAttr()
    {
        return $this->commission - $this->commissionLogs()
                ->where('create_time', '>', Date::now()->subWeek())
                ->where('type', CommissionLog::TYPE_INC)
                ->sum('number');
    }

    /**
     * 更新佣金
     * @param integer $type
     * @param integer $number
     * @param string $info
     * @param null|Model $source
     * @return MoneyLog
     */
    public function updateCommission($type, $number, $info, $source = null)
    {
        return $this->transaction(function () use ($source, $type, $info, $number) {

            if ($type == CommissionLog::TYPE_DEC) {
                $this->dec('commission', $number);
            } else {
                $this->inc('commission', $number);
            }

            $this->save();

            $data = [
                'type'   => $type,
                'number' => $number,
                'info'   => $info,
            ];

            if ($source) {
                $pk = $source->getPk();

                $data['source_type'] = get_class($source);
                $data['source_id']   = $source->{$pk};
            }

            return $this->commissionLogs()->save($data);
        });
    }
}
