<?php

namespace app\model;

use think\exception\ValidateException;
use think\Model;

/**
 * Class app\model\Api
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $category_id
 * @property int $id
 * @property int $identify_type
 * @property int $price_type
 * @property int $status
 * @property int $trial_nums
 * @property string $content
 * @property string $description
 * @property string $logo
 * @property string $name
 * @property string $recommend_time
 * @property string $title
 * @property-read \app\model\ApiCategory $category
 * @property-read \app\model\ApiPackage[] $packages
 * @property-read \app\model\ApiUser $pivot
 * @property-read \app\model\User[] $users
 * @property-read mixed $apply_url
 * @property-read mixed $url
 */
class Api extends Model
{

    protected $append = ['url'];

    public function category()
    {
        return $this->belongsTo(ApiCategory::class, 'category_id');
    }

    public function packages($order = true)
    {
        $relation = $this->hasMany(ApiPackage::class, 'api_id');
        if ($order) {
            $relation->order('nums asc');
        }
        return $relation;
    }

    public function users()
    {
        return $this->belongsToMany(User::class, ApiUser::class);
    }

    protected function getUrlAttr()
    {
        return (string) main_url("/api/{$this->id}");
    }

    protected function getApplyUrlAttr()
    {
        return (string) console_url("/api/apply/{$this->id}");
    }

    public function isFree()
    {
        return $this->price_type == 0;
    }

    public function isVip()
    {
        return $this->price_type == 1;
    }

    public function isPay()
    {
        return $this->price_type == 2;
    }

    public function ensureAvailableForUser(User $user)
    {
        if ($this->identify_type == 1 && !$user->isCertified()) {
            throw new ValidateException('该接口仅限实名认证用户使用');
        }

        if ($this->identify_type == 2 && !$user->isEnterprise()) {
            throw new ValidateException('该接口仅限企业认证用户使用');
        }

        if ($this->price_type == 1 && !ApiVip::isVip($user)) {
            throw new ValidateException('该接口仅限高级会员以上用户使用');
        }
    }

}
