<?php

namespace app\model;

use Exception;
use think\api\Client;
use think\facade\Log;
use think\helper\Arr;
use think\Model;

/**
 * Class Certification
 *
 * @property bool $passed
 * @property int $id
 * @property int $user_id
 * @property string $certify_id
 * @property string $certify_url
 * @property string $create_time
 * @property string $failed_reason
 * @property string $identity
 * @property string $name
 * @property string $update_time
 */
class Certification extends Model
{

    public function check()
    {
        if ($this->passed == 0) {
            try {
                $this->invoke(function (Client $client) {
                    if ($this->certify_id) {
                        $result = $client->faceQuery()
                            ->withOrderNumber($this->certify_id)
                            ->request();

                        if (Arr::get($result, 'data.status') == 1) {
                            $this->save([
                                'passed' => 1,
                            ]);
                        }
                    }
                });
            } catch (Exception) {

            }
        }

        return $this->passed;
    }

    protected function getCertifyUrlAttr($value)
    {
        if ($this->passed == 0) {
            return $this->invoke(function (Client $client) use ($value) {
                //TODO 使用二要素预检

                try {
                    if (empty($this->certify_id) && empty($this->failed_reason)) {
                        $result = $client->faceDetect()
                            ->withName($this->getAttr('name'))
                            ->withIdcard($this->identity)
                            ->withCallbackUrl('schema://topthink')
                            ->withNotifyUrl('http://localhost')
                            ->withFaceauthMode('ZHIMACREDIT')
                            ->request();

                        if (Arr::get($result, 'status') == 0) {
                            $this->save([
                                'certify_id'  => Arr::get($result, 'data.orderNumber'),
                                'certify_url' => Arr::get($result, 'data.originalUrl'),
                            ]);

                            return Arr::get($result, 'data.originalUrl');
                        } else {
                            $this->save([
                                'failed_reason' => Arr::get($result, 'message'),
                            ]);
                            return null;
                        }
                    }

                    return $value;
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                    return null;
                }
            });
        }
        return null;
    }

}
