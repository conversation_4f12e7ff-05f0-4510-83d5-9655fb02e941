<?php

namespace app\model;

use app\notification\Message;
use think\facade\Log;
use think\Model;
use yunwuxin\facade\Notification;

/**
 * Class app\model\Ai
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $token
 * @property int $user_id
 * @property int $warn_token
 * @property-read \app\model\User $user
 * @property-read mixed $packages
 */
class Ai extends Model
{
    const PACKAGES = [
        '3M'    => [
            'name'   => '3M Token',
            'token'  => 3000000,
            'amount' => 80,
            'origin' => 100,
        ],
        '10M'   => [
            'name'   => '10M Token',
            'token'  => 10000000,
            'amount' => 220,
            'origin' => 330,
        ],
        '100M'  => [
            'name'   => '100M Token',
            'token'  => 100000000,
            'amount' => 2100,
            'origin' => 3200,
        ],
        '1000M' => [
            'name'   => '1000M Token',
            'token'  => 1000000000,
            'amount' => 21000,
            'origin' => 32000,
        ],
    ];

    protected $append = ['packages'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    protected function getPackagesAttr()
    {
        return self::PACKAGES;
    }

    public function checkWarn($usage)
    {
        try {
            if ($this->warn_token > 0) {
                $warn       = $this->warn_token * 1000;
                $needNotify = $warn < $this->token && $warn >= $this->token - $usage;
                if ($needNotify) {
                    Notification::send(
                        $this->user_id,
                        new Message("您ThinkAI的Token余额不足{$this->warn_token}K，为了避免影响业务，请及时续费！", sms: true)
                    );
                }
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }
}
