<?php

namespace app\model;

use think\Model;
use think\model\Collection;

/**
 * Class app\model\Invoice
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property int $amount 发票金额
 * @property int $id
 * @property int $status
 * @property int $type
 * @property int $user_id
 * @property string $remark
 * @property string $taxpayer_number 纳税人识别号
 * @property string $title 公司名称
 * @property-read \app\model\Charge[]|\think\model\Collection $charges
 * @property-read \app\model\User $user
 * @property-read mixed $status_text
 */
class Invoice extends Model
{
    protected $append = ['status_text'];

    protected function getStatusTextAttr()
    {
        switch ($this->status) {
            case 1:
                return '已开票';
            case -1:
                return '已驳回';
            default:
                return '等待开票';
        }
    }

    public function charges()
    {
        return $this->hasMany(Charge::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
