<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\ApiPackage
 *
 * @property int $agency_price
 * @property int $api_id
 * @property int $id
 * @property int $nums
 * @property int $original_price
 * @property int $price
 * @property-read \app\model\Api $api
 * @property-read mixed $buy_url
 */
class ApiPackage extends Model
{
    protected $autoWriteTimestamp = false;

    public function api()
    {
        return $this->belongsTo(Api::class);
    }

    protected function getBuyUrlAttr()
    {
        return console_url("/api/{$this->api_id}/package/buy/{$this->id}");
    }

    protected function setPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getPriceAttr($price)
    {
        return $price / 100;
    }

    protected function setOriginalPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getOriginalPriceAttr($price)
    {
        return $price / 100;
    }

    protected function setAgencyPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getAgencyPriceAttr($price)
    {
        return $price / 100;
    }
}
