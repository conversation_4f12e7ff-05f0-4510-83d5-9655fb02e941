<?php

namespace app\model;

use think\Model;
use yunwuxin\pay\interfaces\Refundable;
use yunwuxin\pay\traits\RefundableModel;

/**
 * Class app\model\Refund
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $charge_id
 * @property int $id
 * @property string $extra
 * @property string $raw
 * @property string $refund_no
 * @property string $refund_time
 * @property string $status
 * @property-read \app\model\Charge $charge
 * @property-read mixed $status_text
 */
class Refund extends Model implements Refundable
{
    use RefundableModel;

    public function charge()
    {
        return $this->belongsTo(Charge::class);
    }

    public function getAmount()
    {
        return $this->charge->getAmount();
    }

    public function getChannel()
    {
        return $this->charge->channel;
    }

}
