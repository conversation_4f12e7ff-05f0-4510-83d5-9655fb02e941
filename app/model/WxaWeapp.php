<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\WxaWeapp
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $ext
 * @property int $application_id
 * @property int $id
 * @property int $status
 * @property int $template_id
 * @property int $user_id
 * @property string $app_id
 * @property string $avatar
 * @property string $domain
 * @property string $identity
 * @property string $nickname
 * @property string $refresh_token
 * @property string $signature
 * @property string $version
 * @property-read \app\model\WxaTemplate $template
 */
class WxaWeapp extends Model
{
    protected $type = [
        'ext' => 'json',
    ];

    public function template()
    {
        return $this->hasOne(WxaTemplate::class, 'id', 'template_id');
    }
}
