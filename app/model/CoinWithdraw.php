<?php

namespace app\model;

use app\lib\Hashids;
use app\lib\Soho;
use app\lib\StaticData;
use Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\Model;

/**
 * Class app\model\CoinWithdraw
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount
 * @property int $id
 * @property int $status
 * @property int $user_id
 * @property string $account
 * @property string $name
 * @property string $remark
 * @property-read \app\model\User $user
 * @property-read mixed $order_id
 * @property-read mixed $status_color
 * @property-read mixed $status_text
 */
class CoinWithdraw extends Model
{
    use StaticData;

    protected $append = ['status_text', 'status_color'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function confirm()
    {
        if ($this->status != 0) {
            throw new ValidateException('只能操作正在处理中的提现');
        }

        $this->invoke(function (Soho $soho) {

            $soho->createSettlement([
                'amount'   => $this->amount,
                'order_id' => $this->order_id,
                'name'     => $this->getAttr('name'),
                'account'  => $this->account,
                'identity' => $this->user->certification->identity,
                'mobile'   => $this->user->mobile,
            ]);

            $this->save(['status' => 2]);
        });
    }

    /**
     * 取消提现
     * @param $remark
     */
    public function cancel($remark)
    {
        if (!in_array($this->getData('status'), [0, 2])) {
            throw new ValidateException('只能取消正在处理中的提现');
        }
        Db::transaction(function () use ($remark) {
            $this->user->updateCoin(CoinLog::TYPE_INC, $this->amount, "取消提现#{$this->id}");
            $this->save(['status' => -1, 'remark' => $remark]);
        });
    }

    protected function getOrderIdAttr()
    {
        return Hashids::encode($this->id, config('app.app_host'));
    }

    protected function getStatusAttr($status)
    {
        return $this->getStaticData(function () use ($status) {
            if ($status == 2) {
                $this->invoke(function (Soho $soho) {
                    try {
                        $result = $soho->querySettlement($this->order_id);

                        if ($result['status'] == 50) {
                            $this->save(['status' => 1]);
                        } elseif (in_array($result['status'], [1, 60, 70, 80])) {
                            $this->cancel($result['errorMsg']);
                        }
                    } catch (Exception) {

                    }
                });
            }
            return $this->getData('status');
        });
    }

    protected function getStatusTextAttr()
    {
        return [
                   1  => '已完成',
                   0  => '待审核',
                   2  => '结算中',
                   -1 => '已取消',
               ][$this->status];
    }

    protected function getStatusColorAttr()
    {
        return [
                   1  => 'success',
                   0  => 'info',
                   2  => 'primary',
                   -1 => 'danger',
               ][$this->status];
    }
}
