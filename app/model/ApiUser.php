<?php

namespace app\model;

use app\lib\Date;
use app\lib\StaticData;
use think\facade\Cache;
use think\model\Pivot;

/**
 * Class app\model\ApiUser
 *
 * @property \app\lib\Date $create_time
 * @property int $api_id
 * @property int $user_id
 * @property int $warn_nums
 * @property-read \app\model\User $user
 * @property-read mixed $nums
 * @property-read mixed $rate
 * @property-read mixed $total_nums
 * @property-read mixed $used_nums
 */
class ApiUser extends Pivot
{
    use StaticData;

    const PACKAGES_KEY = 'api_packages_%u_%u';

    protected $append = ['rate', 'nums'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function logs()
    {
        return ApiLog::where('api_id', $this->api_id)
            ->where('user_id', $this->user_id)
            ->order('create_time desc');
    }

    public function getUser()
    {
        return $this->parent instanceof User ? $this->parent : $this->user;
    }

    protected function getRateAttr()
    {
        $vip = ApiVip::getByUser($this->getUser());

        if ($vip) {
            if ($vip->isGold()) {
                return [1000, 60, '1000次/分钟'];
            }
            if ($vip->isSenior()) {
                return [100, 60, '100次/分钟'];
            }
        }
        return [50, 60 * 60 * 24, '50次/天'];
    }

    protected function getNumsAttr()
    {
        return $this->total_nums - $this->used_nums;
    }

    protected function getTotalNumsAttr()
    {
        return $this->getPackages()->reduce(function ($nums, ApiUserPackage $package) {
            return $package->total_nums + $nums;
        }, 0);
    }

    protected function getUsedNumsAttr()
    {
        return $this->getPackages()->reduce(function ($nums, ApiUserPackage $package) {
            return $package->used_nums + $nums;
        }, 0);
    }

    public function getPackages()
    {
        return $this->getStaticData(function () {
            return ApiUserPackage::where('user_id', $this->user_id)
                ->where('api_id', $this->api_id)
                ->where('status', '>', 0)
                ->where('create_time', '>', Date::parse('-2 years'))
                ->order('create_time asc')
                ->cacheAlways(sprintf(self::PACKAGES_KEY, $this->api_id, $this->user_id), 7 * 24 * 3600)
                ->select();
        });
    }

    public function getPackage($id): ?ApiUserPackage
    {
        return $this->getPackages()->first(function (ApiUserPackage $package) use ($id) {
            return $package->id == $id;
        });
    }

    public function getAvailablePackage(): ?ApiUserPackage
    {
        $packages = $this->getPackages();

        return $packages->first(function (ApiUserPackage $package) {
            return $package->isAvailable();
        });
    }

}
