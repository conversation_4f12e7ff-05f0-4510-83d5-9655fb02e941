<?php

namespace app\model;

use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\facade\Cache;
use think\Model;

/**
 * Class app\model\AccessToken
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $last_time
 * @property int $accessible_id
 * @property int $id
 * @property string $accessible_type
 * @property string $name
 * @property string $token
 * @property string[] $scopes
 * @property-read \app\model\AssistantWeaccount|\app\model\User|\app\model\LicenseWiki|\app\model\LicenseBot|\app\model\LicenseChat $accessible
 */
class AccessToken extends Model
{
    const TOKEN_KEY = 'access_token_%s';

    protected $type = [
        'create_time' => Date::class,
        'expire_time' => Date::class,
        'last_time'   => Date::class,
    ];

    protected $hidden = ['accessible_id', 'accessible_type', 'token'];

    protected $autoWriteTimestamp = false;

    public static function onAfterDelete(self $model): void
    {
        Cache::delete(sprintf(self::TOKEN_KEY, $model->token));
    }

    public function accessible()
    {
        return $this->morphTo();
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->lt(Date::now());
    }

    protected function setScopesAttr($scopes)
    {
        return join(',', $scopes);
    }

    protected function getScopesAttr($scopes)
    {
        if (empty($scopes)) {
            return [];
        }
        return explode(',', $scopes);
    }

    public function match($type = null, $scope = null)
    {
        if ($type && !in_array($this->accessible_type, (array) $type)) {
            return false;
        }

        if ($scope && !$this->hasScope($scope)) {
            return false;
        }

        return true;
    }

    public function hasScope($scope)
    {
        return in_array($scope, $this->scopes);
    }

    /**
     * @param string $token
     * @param string $type
     * @param $scope
     */
    public static function getByToken($token, $type = null, $scope = null)
    {
        if (!Uuid::isValid($token)) {
            return false;
        }

        /** @var AccessToken $accessToken */
        $accessToken = Cache::remember(sprintf(self::TOKEN_KEY, $token), function () use ($token) {
            $accessToken = AccessToken::where('token', $token)->with('accessible')->find();

            if ($accessToken) {
                $accessToken->save([
                    'last_time' => Date::now(),
                ]);
            }
            return $accessToken;
        }, 60 * 60);

        if (empty($accessToken) || $accessToken->isExpired()) {
            return null;
        }

        if (!$accessToken->match($type, $scope)) {
            return null;
        }

        return $accessToken;
    }
}
