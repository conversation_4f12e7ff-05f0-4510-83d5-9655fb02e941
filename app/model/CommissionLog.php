<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\CommissionLog
 *
 * @property \app\lib\Date $create_time
 * @property int $id
 * @property int $number
 * @property int $partner_id
 * @property int $source_id
 * @property int $type
 * @property string $info
 * @property string $source_type
 * @property-read \app\model\Partner $partner
 * @property-read mixed $source
 */
class CommissionLog extends Model
{
    const TYPE_INC = 1;
    const TYPE_DEC = 2;

    public static function onBeforeDelete(self $log)
    {
        if ($log->getAttr('type') === self::TYPE_INC) {
            $log->partner->dec('commission', $log->number)->save();
        } else {
            $log->partner->inc('commission', $log->number)->save();
        }
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function source()
    {
        return $this->morphTo();
    }
}
