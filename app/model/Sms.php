<?php

namespace app\model;

use app\facade\Producer;
use app\job\SmsHookJob;
use app\lib\Date;
use app\lib\StaticData;
use app\notification\ApiWarn;
use longlang\phpkafka\Producer\ProduceMessage;
use think\exception\ValidateException;
use think\facade\Cache;
use think\Model;
use yunwuxin\throttle\facade\RateLimiter;

/**
 * Class app\model\Sms
 *
 * @property int $day_limit
 * @property int $rate_limit 全局频率限制
 * @property int $hour_limit
 * @property int $id
 * @property int $minute_limit
 * @property int $user_id
 * @property int $warn_nums
 * @property int $batch_send 是否开启批量发送
 * @property string $webhook
 * @property-read \app\model\User $user
 */
class Sms extends Model
{
    use StaticData;

    const RATE_LIMITER_KEY = 'sms_rate_limiter_%u_%s_%u_%u';
    const PACKAGES_KEY     = 'api_packages_%u';
    const CACHE_KEY        = 'sms_%u';

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(self::CACHE_KEY, $model->user_id));
    }

    public static function getByUser(User|int $user): self
    {
        if ($user instanceof User) {
            $user = $user->id;
        }
        $sms = self::where('user_id', $user)
            ->cacheAlways(sprintf(self::CACHE_KEY, $user), 7 * 24 * 3600)
            ->find();

        if (empty($sms)) {
            throw new ValidateException('尚未开通短信服务');
        }

        return $sms;
    }

    public static function callWebhook(User|int $user, $payload)
    {
        $sms = self::getByUser($user);
        if ($sms->webhook) {
            queue(SmsHookJob::class, [$sms->webhook, $payload], 0, 'retry');
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getNums($type = 1)
    {
        return $this->getPackages()->reduce(function ($nums, SmsPackage $package) use ($type) {
            if ($package->getAttr('type') == $type) {
                return $nums + $package->nums;
            }
            return $nums;
        }, 0);
    }

    protected function getRateLimitAttr($value)
    {
        return $value > 0 ? $value : 1000;
    }

    protected function getCurrentRateLimitAttr()
    {
        $key = sprintf(self::RATE_LIMITER_KEY, $this->user->id, 'global', 0, 0);
        return RateLimiter::attempts($key);
    }

    public function getPackages()
    {
        return $this->getStaticData(function () {
            return SmsPackage::where('user_id', $this->user_id)
                ->where('status', '>', 0)
                ->where('create_time', '>', Date::parse('-2 years'))
                ->order('create_time asc')
                ->cacheAlways(sprintf(self::PACKAGES_KEY, $this->user_id), 7 * 24 * 3600)
                ->select();
        });
    }

    public function getAvailablePackage(int $type = 1, int $nums = 1, $force = false): ?SmsPackage
    {
        $packages = $this->getPackages();

        $package = $packages->first(function (SmsPackage $package) use ($nums, $type) {
            return $package->isAvailable() && $package->getAttr('type') == $type && $package->nums >= $nums;
        });

        if ($force && !$package) {
            throw new ValidateException('无可用套餐');
        }

        return $package;
    }

    public function getPackage($id): ?SmsPackage
    {
        return $this->getPackages()->first(function (SmsPackage $package) use ($id) {
            return $package->id == $id;
        });
    }

    protected function checkWarnNums($type, $nums)
    {
        if ($this->warn_nums > 0) {
            $totalNums = $this->getNums($type);
            if ($this->warn_nums < $totalNums + $nums && $this->warn_nums >= $totalNums) {
                $this->user->notify(new ApiWarn($type == 1 ? '通知短信' : '营销短信', $this->warn_nums));
            }
        }
    }

    protected function getContent(SmsSign $sign, SmsTemplate $template, $params = null)
    {
        if (!empty($params)) {
            if (is_string($params)) {
                $params = json_decode($params, true);

                if (!is_array($params)) {
                    throw new ValidateException('模板变量参数不合规，请检查');
                }
            }
            $content = str_replace(array_map(function ($value) {
                return sprintf('${%s}', $value);
            }, array_keys($params)), array_values($params), $template->content);
        } else {
            $content = $template->content;
        }

        //添加签名
        return "【{$sign->getAttr('name')}】{$content}";
    }

    /**
     * @param SmsSign|string $sign
     * @param SmsTemplate|string $template
     */
    protected function checkSignAndTemplate($sign, $template)
    {
        if (!$sign instanceof SmsSign) {
            $sign = SmsSign::getById($this->user, $sign);
        }
        if (!$template instanceof SmsTemplate) {
            $template = SmsTemplate::getById($this->user, $template);
        }

        if ($template->sign_id > 0 && $template->sign_id != $sign->id) {
            throw new ValidateException("签名[{$sign->hash_id}]与模板[{$template->hash_id}]不匹配");
        }

        return [$sign, $template];
    }

    /**
     * @param SmsSign|string $sign
     * @param SmsTemplate|string $template
     * @param string $phone
     * @param string|null $params
     */
    public function send($sign, $template, string $phone, ?string $params = null)
    {
        [$sign, $template] = $this->checkSignAndTemplate($sign, $template);

        $package = $this->getAvailablePackage($template->getAttr('type'), 1, true);

        //全局频率限制
        $globalLimiterKey = sprintf(self::RATE_LIMITER_KEY, $this->user->id, 'global', 0, 0);

        if (RateLimiter::tooManyAttempts($globalLimiterKey, $this->rate_limit)) {
            throw new ValidateException('流控超限，频繁发送超限');
        }

        $limiterKey = sprintf(self::RATE_LIMITER_KEY, $this->user->id, $phone, $sign->id, $template->id);

        if (
            RateLimiter::tooManyAttempts($limiterKey . '@minute', $this->minute_limit) ||
            RateLimiter::tooManyAttempts($limiterKey . '@hour', $this->hour_limit) ||
            RateLimiter::tooManyAttempts($limiterKey . '@day', $this->day_limit)
        ) {
            //超出频率限制了
            throw new ValidateException('单个号码日、月发送上限，流控超限，频繁发送超限');
        }

        $content = $this->getContent($sign, $template, $params);

        if ($template->sign_id > 0) {
            $result = \app\facade\Sms::xSend($phone, $template->out_id, $params);
        } else {
            //公共模板
            $result = \app\facade\Sms::send($phone, $content);
        }

        $id   = $result['send_id'];
        $nums = $result['fee'];

        $package->incUsedNums($nums);

        Producer::send('sms_log', json_encode([
            'send_id'     => $id,
            'user_id'     => $this->user->id,
            'sign_id'     => $sign->id,
            'template_id' => $template->id,
            'package_id'  => $package->id,
            'phone'       => $phone,
            'content'     => $content,
            'nums'        => $nums,
            'send_time'   => Date::now(),
            'create_time' => Date::now(),
            'status'      => 0,
        ]));

        //hit
        RateLimiter::hit($globalLimiterKey, 3600);

        RateLimiter::hit($limiterKey . '@minute');
        RateLimiter::hit($limiterKey . '@hour', 3600);
        RateLimiter::hit($limiterKey . '@day', 3600 * 24);

        //预警
        $this->checkWarnNums($template->getAttr('type'), $nums);

        return [
            'id'   => $id,
            'nums' => $nums,
        ];
    }

    /**
     * @param SmsSign|string $sign
     * @param SmsTemplate|string $template
     * @param string $phone
     * @param string|null $params
     */
    public function batchSend($sign, $template, string $phone, ?string $params = null, $taskId = 0)
    {
        if (!$this->batch_send) {
            throw new ValidateException('批量发送功能尚未开通，请联系客服');
        }

        [$sign, $template] = $this->checkSignAndTemplate($sign, $template);

        //检查phone是否为一对多格式
        $multi = json_decode($phone, true);
        if (!empty($multi) && is_array($multi)) {
            $package = $this->getAvailablePackage($template->getAttr('type'), count($multi), true);
            $content = array_map(function ($item) use ($sign, $template) {
                return $this->getContent($sign, $template, $item['vars'] ?? null);
            }, $multi);

            if ($template->sign_id > 0) {
                $result = \app\facade\Sms::multiXSend($template->out_id, $phone);
            } else {
                //公共模板
                $result = \app\facade\Sms::multiSend($this->getContent($sign, $template), $phone);
            }
        } else {
            $package = $this->getAvailablePackage($template->getAttr('type'), count(explode(',', $phone)), true);
            $content = $this->getContent($sign, $template, $params);

            if ($template->sign_id > 0) {
                $result = \app\facade\Sms::batchXSend($phone, $template->out_id, $params);
            } else {
                //公共模板
                $result = \app\facade\Sms::batchSend($phone, $content);
            }
        }

        $nums = array_reduce($result, function ($nums, $item) {
            if ($item['status'] === 'success') {
                return $nums + $item['fee'];
            }
            return $nums;
        }, 0);

        $package->incUsedNums($nums);

        $i = 0;
        Producer::sendBatch(array_flat_map(function ($item) use ($template, $sign, $package, $content, $taskId, &$i) {
            $index = $i++;
            if ($item['status'] === 'success') {
                return [
                    new ProduceMessage('sms_log', json_encode([
                        'send_id'     => $item['send_id'],
                        'user_id'     => $this->user->id,
                        'sign_id'     => $sign->id,
                        'template_id' => $template->id,
                        'task_id'     => $taskId,
                        'package_id'  => $package->id,
                        'phone'       => $item['to'],
                        'content'     => is_array($content) ? $content[$index] : $content,
                        'nums'        => $item['fee'],
                        'send_time'   => Date::now(),
                        'create_time' => Date::now(),
                        'status'      => 0,
                    ])),
                ];
            }
            return [];
        }, $result));

        //预警
        $this->checkWarnNums($template->getAttr('type'), $nums);

        return array_flat_map(function ($item) {
            if ($item['status'] === 'success') {
                return [
                    [
                        'id'    => $item['send_id'],
                        'phone' => $item['to'],
                        'nums'  => $item['fee'],
                    ],
                ];
            }
            return [];
        }, $result);
    }

}
