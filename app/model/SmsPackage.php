<?php

namespace app\model;

use app\lib\Date;
use think\facade\Cache;
use think\Model;

/**
 * Class app\model\SmsPackage
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $price
 * @property int $status
 * @property int $total_nums
 * @property int $type 类型：1-通知，2-营销
 * @property int $used_nums
 * @property int $user_id
 * @property string $name
 * @property-read \app\model\User $user
 * @property-read mixed $expire_time
 * @property-read mixed $nums
 */
class SmsPackage extends Model
{
    const USED_NUMS_KEY = 'sms_package_used_nums_%u';

    protected $append = ['nums', 'expire_time'];

    protected $type = [
        'expire_time' => Date::class,
    ];

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(Sms::PACKAGES_KEY, $model->user_id));
    }

    public static function onAfterDelete(self $model): void
    {
        Cache::delete(sprintf(Sms::PACKAGES_KEY, $model->user_id));
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isExpired()
    {
        return $this->expire_time->isPast();
    }

    public function isAvailable()
    {
        return !$this->isExpired() && $this->status > 0 && $this->nums > 0;
    }

    protected function getTypeAttr($type)
    {
        return ($type != 2) ? 1 : $type;
    }

    protected function getExpireTimeAttr($value)
    {
        // 如果数据库中存储了expire_time，则使用该值
        if (!empty($value)) {
            return new Date($value);
        }
        // 否则使用默认的计算方式（创建时间+2年）
        return $this->create_time->addYears(2);
    }

    protected function getNumsAttr()
    {
        return max($this->total_nums - $this->used_nums, 0);
    }

    protected function getUsedNumsAttr($nums)
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);

        $cached = Cache::get($key);

        if ($cached == null) {
            Cache::set($key, $nums);
            return $nums;
        }
        return $cached;
    }

    public function incUsedNums($nums = 1)
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);
        Cache::inc($key, $nums);
    }

    public function decUsedNums($nums = 1)
    {
        $key = sprintf(self::USED_NUMS_KEY, $this->id);
        Cache::dec($key, $nums);
    }

    public function syncUsedNums()
    {
        $archiveNums  = SmsPackageUsage::where('package_id', $this->id)->sum('nums');
        $archiveMonth = SmsPackageUsage::where('package_id', $this->id)->max('month', false);

        $logQuery = SmsLog::where('package_id', $this->id)->where('status', 1);

        if ($archiveMonth) {
            $logQuery->where('send_time', '>', Date::createFromFormat('!Ym', $archiveMonth)->endOfMonth());
        }

        $logNums = $logQuery->sum('nums');

        $toNums   = SmsPackageAmend::where('to_id', $this->id)->sum('nums');
        $fromNums = SmsPackageAmend::where('from_id', $this->id)->sum('nums');

        $usedNums = $archiveNums + $logNums + $toNums - $fromNums;

        if ($usedNums != $this->used_nums) {
            Cache::set(sprintf(self::USED_NUMS_KEY, $this->id), $usedNums);
            $this->save(['used_nums' => $usedNums]);
        }
    }

    public function amendUsedNums()
    {
        if ($this->used_nums > $this->total_nums) {
            $sms         = Sms::getByUser($this->user_id);
            $type        = $this->getAttr('type');
            $balanceNums = $this->used_nums - $this->total_nums;

            $balanceNums = min($balanceNums, $sms->getNums($type) - $sms->warn_nums - 50);

            if ($balanceNums > 0) {
                /** @var SmsPackage $to */
                $to = SmsPackage::where('user_id', $this->user_id)
                    ->where('id', '<>', $this->id)
                    ->where('type', $type)
                    ->order('create_time asc')
                    ->select()
                    ->first(function (SmsPackage $package) {
                        return $package->isAvailable();
                    });

                if ($to) {
                    $balanceNums = min($balanceNums, $to->nums);

                    if ($balanceNums > 0) {
                        //调整次数
                        $amend = SmsPackageAmend::where('from_id', $this->id)
                            ->where('to_id', $to->id)
                            ->find();

                        if (empty($amend)) {
                            $amend = new SmsPackageAmend([
                                'from_id' => $this->id,
                                'to_id'   => $to->id,
                                'nums'    => 0,
                            ]);
                        }

                        $amend->nums += $balanceNums;
                        $amend->save();

                        $this->decUsedNums($balanceNums);
                        $to->incUsedNums($balanceNums);
                    }
                }
            }
        }
    }

    /**
     * 日志归档
     * @param $month
     * @return int
     */
    public function archive($month = null)
    {
        if (!$month) {
            $month = Date::now()->subMonth()->format('Ym');
        }

        $startTime = Date::createFromFormat('!Ym', $month)->startOfMonth();
        $endTime   = Date::createFromFormat('!Ym', $month)->endOfMonth();

        $nums = SmsLog::where('package_id', $this->id)
            ->whereBetween('send_time', [$startTime, $endTime])
            ->where('status', 1)
            ->sum('nums');

        if ($nums > 0) {
            SmsPackageUsage::create([
                'package_id' => $this->id,
                'month'      => $month,
                'nums'       => $nums,
            ], replace: true);
        }

        return $nums;
    }

    protected function setPriceAttr($price)
    {
        return $price * 100;
    }

    protected function getPriceAttr($price)
    {
        return $price / 100;
    }

}
