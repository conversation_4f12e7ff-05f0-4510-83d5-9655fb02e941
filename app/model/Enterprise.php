<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\Enterprise
 *
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property int $id
 * @property int $status
 * @property int $user_id
 * @property string $corporation
 * @property string $identity
 * @property string $img
 * @property string $name
 * @property-read \app\model\User $user
 */
class Enterprise extends Model
{
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
