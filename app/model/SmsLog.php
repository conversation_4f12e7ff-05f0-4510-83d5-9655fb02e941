<?php

namespace app\model;

use app\lib\Date;
use app\lib\Producer;
use think\Model;
use think\swoole\coroutine\Context;

/**
 * Class app\model\SmsLog
 *
 * @property \app\lib\Date $send_time
 * @property int $nums
 * @property int $package_id
 * @property int $sign_id
 * @property int $status
 * @property int $task_id
 * @property int $template_id
 * @property int $user_id
 * @property string $content
 * @property string $message
 * @property string $phone
 * @property string $send_id
 * @method static \think\db\Query final()
 * @method static \think\db\Query recent()
 */
class SmsLog extends Model
{
    protected $connection = 'clickhouse';

    // 定义全局的查询范围
    protected $globalScope = ['final'];

    public function scopeFinal($query)
    {
        $query->final();
    }

    public function scopeRecent($query)
    {
        $query->where('send_time', '>', Date::now()->subDays(3));
    }

    public function syncStatus()
    {
        if ($this->send_id) {
            $this->invoke(function (\app\lib\submail\Sms $sms) {
                $log = $sms->getLog($this->send_id);
                if (empty($log) || $log['status'] == 'dropped') {
                    $status = -1;
                } elseif ($log['status'] == 'delivered') {
                    $status = 1;
                }

                if (isset($status)) {
                    $this->updateStatus($status, $status == -1 ? ($log['dropped_reason'] ?? '未知原因') : null);
                }
            });
        }
    }

    public function updateStatus($status, $message = null)
    {
        if ($this->status == 0) {
            $this->invoke(function (Producer $producer) use ($message, $status) {
                //推送状态
                $producer->send('sms_log', json_encode([
                    'send_id'     => $this->send_id,
                    'task_id'     => $this->task_id,
                    'sign_id'     => $this->sign_id,
                    'template_id' => $this->template_id,
                    'package_id'  => $this->package_id,
                    'user_id'     => $this->user_id,
                    'phone'       => $this->phone,
                    'content'     => $this->content,
                    'nums'        => $this->nums,
                    'send_time'   => $this->send_time,
                    'create_time' => Date::now(),
                    'status'      => $status,
                    'message'     => $message,
                ]));

                //归还次数
                if ($status == -1) {
                    Sms::getByUser($this->user_id)->getPackage($this->package_id)?->decUsedNums($this->nums);
                }
            });

            $this->status = $status;
        }
    }

    public function toData()
    {
        return [
            'id'        => $this->send_id,
            'phone'     => $this->phone,
            'content'   => $this->content,
            'send_time' => $this->send_time,
            'nums'      => $this->nums,
            'status'    => $this->status,
            'message'   => $this->message,
        ];
    }

    public function getUsername()
    {
        return Context::rememberData(sprintf('sms_log_username_%u', $this->user_id), function () {
            $user = User::find($this->user_id);
            return $user?->getAttr('name') ?? '未知用户';
        });
    }

    /**
     * @return \app\model\SmsSign|null
     */
    public function getSign()
    {
        return Context::rememberData(sprintf('sms_log_sign_%u', $this->sign_id), function () {
            return SmsSign::find($this->sign_id);
        });
    }

    /**
     * @return \app\model\SmsTemplate|null
     */
    public function getTemplate()
    {
        return Context::rememberData(sprintf('sms_log_template_%u', $this->template_id), function () {
            return SmsTemplate::find($this->template_id);
        });
    }
}
