<?php

namespace app\model;

use app\job\SmsBatchSendJob;
use app\lib\Date;
use think\Model;

/**
 * Class app\model\SmsTask
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $send_time 定时发送时间
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $status 任务状态
 * @property int $template_id 短信模板ID
 * @property int $total 号码总数
 * @property int $user_id
 * @property string $data 号码数据
 * @property string $name 任务名称
 * @property string $type 任务类型
 * @property-read \app\model\SmsTemplate $template
 */
class SmsTask extends Model
{

    protected $type = [
        'send_time' => Date::class,
    ];

    public function template()
    {
        return $this->belongsTo(SmsTemplate::class, 'template_id');
    }

    public function run()
    {
        switch ($this->status) {
            case 0:
                if (empty($this->send_time) || $this->send_time->isPast()) {
                    $this->save([
                        'status' => 2,
                    ]);
                    queue(SmsBatchSendJob::class, $this->id);
                }
                break;
            case 2:
                //长时间处于发送中的任务标记为失败
                if ($this->create_time->diffInMinutes() > 10) {
                    $this->save([
                        'status' => -2,
                    ]);
                }
                break;
        }
    }
}
