<?php

namespace app\model;

use think\exception\ValidateException;
use think\facade\Db;
use think\Model;

/**
 * Class app\model\PartnerSettlement
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount
 * @property int $id
 * @property int $partner_id
 * @property int $status
 * @property int $type
 * @property string $invoice
 * @property string $remark
 * @property-read \app\model\CommissionLog $commission_log
 * @property-read \app\model\Partner $partner
 * @property-read mixed $status_color
 * @property-read mixed $status_text
 */
class PartnerSettlement extends Model
{
    protected $append = ['status_text', 'status_color'];

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function commissionLog()
    {
        return $this->morphOne(CommissionLog::class, 'source');
    }

    public function confirm()
    {
        if ($this->status != 0) {
            throw new ValidateException('只能操作正在处理中的结算');
        }

        Db::transaction(function () {
            if ($this->getData('type') == 1) {
                //扣除6.5%税费
                $this->partner->user->updateCoin(CoinLog::TYPE_INC, floor($this->amount * 93.5 / 100), "佣金结算#{$this->id}", $this);
            } else {
                $this->partner->user->updateMoney(MoneyLog::TYPE_INC, $this->amount, "佣金结算#{$this->id}", $this);
            }
            $this->save(['status' => 1]);
        });
    }

    /**
     * 取消结算
     * @param $remark
     */
    public function cancel($remark)
    {
        if (!in_array($this->getData('status'), [0, 2])) {
            throw new ValidateException('只能操作正在处理中的结算');
        }
        Db::transaction(function () use ($remark) {
            $this->commission_log->delete();
            $this->save(['status' => -1, 'remark' => $remark]);
        });
    }

    protected function getStatusTextAttr()
    {
        return match ($this->status) {
            1 => '已完成',
            0 => '待审核',
            -1 => '已取消',
        };
    }

    protected function getStatusColorAttr()
    {
        return match ($this->status) {
            1 => 'success',
            0 => 'info',
            -1 => 'danger',
        };
    }
}
