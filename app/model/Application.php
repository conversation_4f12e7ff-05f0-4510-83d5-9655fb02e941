<?php

namespace app\model;

use think\Model;
use yunwuxin\oauth\server\entities\Client;
use yunwuxin\oauth\server\interfaces\ClientInterface;

/**
 * Class app\model\Application
 *
 * @property array $scopes
 * @property bool $is_confidential
 * @property int $id
 * @property string $client_id
 * @property string $client_secret
 * @property string $create_time
 * @property string $logout_uri
 * @property string $name
 * @property string $product
 * @property string $redirect_uri
 * @property string $update_time
 */
class Application extends Model implements ClientInterface
{

    protected function setScopesAttr($scopes)
    {
        return join(',', $scopes);
    }

    protected function getScopesAttr($scopes)
    {
        if (empty($scopes)) {
            return [];
        }
        return explode(',', $scopes);
    }

    /**
     * @param $clientId
     * @return static|null
     */
    public static function getByClientId($clientId)
    {
        return self::where('client_id', $clientId)->findOrFail();
    }

    public static function get($id): ?Client
    {
        $app = static::where('client_id', $id)->find();
        if ($app) {
            return new Client(
                $app->client_id,
                $app->getAttr('name'),
                $app->redirect_uri ? explode("\n", $app->redirect_uri) : [],
                $app->is_confidential
            );
        }
        return null;
    }

    public static function validate($identifier, $secret, $grantType): bool
    {
        return !!static::where('client_id', $identifier)->where('client_secret', $secret)->find();
    }
}
