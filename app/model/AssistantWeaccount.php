<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\AssistantWeaccount
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $user_id
 * @property int $weaccount_id
 * @property string $plan
 * @property-read \app\model\WxaWeaccount $weaccount
 * @property-read mixed $access_tokens
 */
class AssistantWeaccount extends Model
{
    protected $type = [
        'expire_time' => Date::class,
    ];

    public function weaccount()
    {
        return $this->belongsTo(WxaWeaccount::class, 'weaccount_id');
    }

    public function accessTokens()
    {
        return $this->morphMany(AccessToken::class, 'accessible');
    }
}
