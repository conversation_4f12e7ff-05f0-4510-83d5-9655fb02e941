<?php

namespace app\model;

use app\lib\Date;
use League\OAuth2\Server\Entities\ScopeEntityInterface;
use think\Model;
use yunwuxin\oauth\server\entities\AccessToken;
use yunwuxin\oauth\server\interfaces\AccessTokenInterface;

/**
 * Class app\model\AccessToken
 *
 * @property \League\OAuth2\Server\Entities\ScopeEntityInterface[] $scopes
 * @property \app\lib\Date $expire_time
 * @property int $user_id
 * @property string $client_id
 * @property string $create_time
 * @property string $id
 * @property string $update_time
 * @property-read \app\model\Application $application
 * @property-read \app\model\User $user
 */
class ApplicationToken extends Model implements AccessTokenInterface
{

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function application()
    {
        return $this->belongsTo(Application::class, 'client_id', 'client_id');
    }

    protected function setScopesAttr($scopes)
    {
        return join(',', $scopes);
    }

    protected function getScopesAttr($scopes)
    {
        if (empty($scopes)) {
            return [];
        }
        return explode(',', $scopes);
    }

    public function can($scope)
    {
        return in_array($scope, $this->scopes + $this->application->scopes);
    }

    public static function persist(AccessToken $accessToken)
    {
        $scopes         = array_map(function (ScopeEntityInterface $scope) {
            return $scope->getIdentifier();
        }, $accessToken->getScopes());
        $expiryDateTime = $accessToken->getExpiryDateTime()->format('Y-m-d H:i:s.u');
        $userId         = $accessToken->getUserIdentifier() ?? 0;

        $token = static::where('client_id', $accessToken->getClient()->getIdentifier())
            ->where('user_id', $userId)
            ->find();

        if ($token) {
            if ($token->expire_time->lt(Date::now())) {
                $token->delete();
            } else {
                $token->save([
                    'scopes'      => $scopes,
                    'expire_time' => $expiryDateTime,
                ]);

                $accessToken->setIdentifier($token->id);
                return;
            }
        }

        static::create([
            'id'          => $accessToken->getIdentifier(),
            'client_id'   => $accessToken->getClient()->getIdentifier(),
            'user_id'     => $userId,
            'scopes'      => $scopes,
            'expire_time' => $expiryDateTime,
        ]);
    }

    public static function revoke($id)
    {
        static::destroy($id);
    }

    public static function isRevoked($id): bool
    {
        return false;
    }
}
