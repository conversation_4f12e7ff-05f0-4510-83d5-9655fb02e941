<?php

namespace app\model;

use app\lib\Date;
use think\Model;

/**
 * Class app\model\UserWeaccount
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $ticket_time
 * @property \app\lib\Date $update_time
 * @property int $user_id
 * @property string $openid
 * @property string $ticket
 */
class UserWeaccount extends Model
{

    protected $pk = false;

    protected $type = [
        'ticket_time' => Date::class,
    ];

    public function isTicketExpired()
    {
        return $this->ticket_time->lte(Date::now());
    }

}
