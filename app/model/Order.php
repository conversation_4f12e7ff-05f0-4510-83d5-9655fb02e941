<?php

namespace app\model;

use think\Model;
use Throwable;
use function yunwuxin\pay\generate_no;

/**
 * Class app\model\Order
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property \app\lib\Goods $goods
 * @property int $amount
 * @property int $id
 * @property int $payable_id
 * @property int $status
 * @property int $user_id
 * @property string $goods_type
 * @property string $order_no
 * @property string $payable_type
 * @property string $subject
 * @property-read \app\model\Charge $charge
 * @property-read \app\model\SslOrder|\app\model\ApiUserPackage|\app\model\SmsPackage $payable
 */
class Order extends Model
{
    protected $type = [
        'goods' => 'serialize',
    ];

    public function charge()
    {
        return $this->hasOne(Charge::class);
    }

    public function payable()
    {
        return $this->morphTo();
    }

    public function paid()
    {
        $this->transaction(function () {
            $model = $this->goods->invoke($this);

            if ($model instanceof Model) {
                $this->payable()->associate($model);
            }

            $this->save(['status' => 1]);
        });
    }

    /**
     * @return void
     * @internal
     */
    public function revoke()
    {
        $this->transaction(function () {
            if ($this->status == 1) {
                $this->goods->revoke($this);
            }
            $this->save(['status' => -1]);
        });
    }

    public function canRevoke()
    {
        try {
            return $this->goods->canRevoke($this);
        } catch (Throwable) {
            return false;
        }
    }

    public function pay()
    {
        $data = [
            'order_id' => $this->id,
            'order_no' => $this->order_no,
            'subject'  => $this->subject,
            'amount'   => $this->amount,
            'user_id'  => $this->user_id,
        ];

        $charge = Charge::where('order_id', $data['order_id'])->find();

        if (empty($charge)) {
            $data['trade_no'] = generate_no();
            $charge           = Charge::create($data);
        }

        return $charge->visible(['order_no', 'trade_no'])->append(['pay_url']);
    }

}
