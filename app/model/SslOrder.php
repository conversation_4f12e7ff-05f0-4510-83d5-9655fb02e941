<?php

namespace app\model;

use app\lib\Certbase;
use app\lib\Date;
use Exception;
use think\Model;

/**
 * Class app\model\SslOrder
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $contact
 * @property array $org
 * @property int $agency_price
 * @property int $cert_id
 * @property int $id
 * @property int $one
 * @property int $price
 * @property int $product_id
 * @property int $status
 * @property int $user_id
 * @property int $wildcard
 * @property string $auth_type
 * @property string $brand
 * @property string $domains
 * @property string $out_order_no
 * @property string $type
 * @property-read \app\model\SslCertificate $certificate
 * @property-read \app\model\User $user
 */
class SslOrder extends Model
{

    protected $type = [
        'contact' => 'json',
        'org'     => 'json',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function certificate()
    {
        return $this->hasOne(SslCertificate::class, 'id', 'cert_id');
    }

    protected function getStatusAttr($status)
    {
        return $this->invoke(function (Certbase $certbase) use ($status) {
            if ($status == 2) {
                try {
                    $result = $certbase->status($this->out_order_no);
                    if ($result['certStatus'] == '3004') {
                        return $this->transaction(function () use ($certbase) {
                            $this->save(['status' => 3]);

                            //保存证书
                            $certInfo = $certbase->download($this->out_order_no);

                            $this->certificate->save([
                                'expire_time' => Date::createFromTimestamp($certInfo['notAfter'] / 1000),
                                'domain'      => $certInfo['commonName'],
                                'certificate' => "{$certInfo['certContent']}\n{$certInfo['midCertContent']}",
                                'status'      => 1,
                            ]);

                            return 3;
                        });
                    }
                } catch (Exception) {

                }
            }
            return $status;
        });
    }

    public function revoke()
    {
        $this->transaction(function () {
            $this->invoke(function (Certbase $certbase) {
                if ($this->status != -1) {
                    $this->save(['status' => -1]);

                    if ($this->out_order_no) {
                        $certbase->cancel($this->out_order_no);
                    }

                    if ($this->certificate) {
                        $this->certificate->save(['status' => 0]);
                    }
                }
            });
        });
    }

}
