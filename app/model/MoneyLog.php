<?php

namespace app\model;

use Exception;
use think\Model;

/**
 * Class app\model\MoneyLog
 *
 * @property \app\lib\Date $create_time
 * @property int $application_id
 * @property int $id
 * @property int $number
 * @property int $source_id
 * @property int $type
 * @property int $user_id
 * @property string $info
 * @property string $source_type
 * @property-read \app\model\User $user
 * @property-read mixed $source
 */
class MoneyLog extends Model
{
    const TYPE_INC = 1;
    const TYPE_DEC = 2;

    public static function onBeforeDelete(self $log)
    {
        if ($log->getAttr('type') === self::TYPE_INC) {
            if ($log->user->money < $log->number) {
                throw new Exception('用户余额不足！');
            }
            $log->user->dec('money', $log->number)->save();
        } else {
            $log->user->inc('money', $log->number)->save();
        }
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function source()
    {
        return $this->morphTo();
    }
}
