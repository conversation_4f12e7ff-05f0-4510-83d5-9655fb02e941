<?php

namespace app\model;

use app\lib\Date;
use app\lib\goods\ApiVipPlan;
use think\facade\Cache;
use think\Model;

/**
 * Class app\model\ApiVip
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $user_id
 * @property string $plan
 * @property-read \app\model\User $user
 * @property-read mixed $plan_name
 */
class ApiVip extends Model
{
    const USER_KEY = 'api_vip_%u';

    protected $append = ['plan_name'];

    protected $type = [
        'expire_time' => Date::class,
    ];

    public static function onAfterWrite(self $model): void
    {
        Cache::delete(sprintf(self::USER_KEY, $model->user_id));
    }

    protected function getPlanNameAttr()
    {
        return ApiVipPlan::TITLE[$this->plan];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function isExpired()
    {
        return $this->expire_time->lt(Date::now());
    }

    public function isSenior()
    {
        return $this->plan == ApiVipPlan::SENIOR;
    }

    public function isGold()
    {
        return $this->plan == ApiVipPlan::GOLD;
    }

    public static function isVip(User $user)
    {
        return !!self::getByUser($user);
    }

    /**
     * @param \app\model\User $user
     * @return self|null
     */
    public static function getByUser(User $user)
    {
        $vip = self::where('user_id', $user->id)
            ->cacheAlways(sprintf(self::USER_KEY, $user->id), 7 * 24 * 3600)
            ->find();

        if ($vip && $vip->isExpired()) {
            return null;
        }

        return $vip;
    }

}
