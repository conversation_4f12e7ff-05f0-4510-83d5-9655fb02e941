<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\AssistantSiteEvent
 *
 * @property \app\lib\Date $create_time
 * @property int $event_type
 * @property int $id
 * @property int $site_id
 * @property string $event_data
 * @property string $event_name
 * @property string $page_title
 * @property string $referrer
 * @property string $referrer_domain
 * @property string $referrer_path
 * @property string $session_id
 * @property string $url
 * @property string $url_domain
 * @property string $url_path
 */
class AssistantSiteEvent extends Model
{

    protected $connection = 'clickhouse';

    const PAGEVIEW = 1;
    const CUSTOM   = 2;
}
