<?php

namespace app\model;

use app\event\Register;
use app\lib\Date;
use app\lib\StaticData;
use think\db\Raw;
use think\Filesystem;
use think\helper\Str;
use think\Model;
use think\model\relation\HasMany;
use yunwuxin\auth\interfaces\Authorizable;
use yunwuxin\auth\Role;
use yunwuxin\auth\traits\AuthorizableUser;
use yunwuxin\notification\Notifiable;

/**
 * Class User
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $last_time
 * @property \app\lib\Date $update_time
 * @property int $coin
 * @property int $features
 * @property int $id
 * @property int $money
 * @property string $avatar
 * @property string $delete_time
 * @property string $description 个人简介
 * @property string $email
 * @property string $last_ip
 * @property string $mobile 手机
 * @property string $name 昵称
 * @property string $reg_ip
 * @property string $remember_token
 * @property-read \app\model\Ai $ai
 * @property-read \app\model\Api[] $apis
 * @property-read \app\model\ApplicationToken[] $tokens
 * @property-read \app\model\Application[] $applications
 * @property-read \app\model\AssistantSite[] $assistant_sites
 * @property-read \app\model\Certification $certification
 * @property-read \app\model\CoinLog[] $coin_logs
 * @property-read \app\model\CoinWithdraw[] $coin_withdraws
 * @property-read \app\model\Enterprise $enterprise
 * @property-read \app\model\MoneyLog[] $money_logs
 * @property-read \app\model\MoneyWithdraw[] $money_withdraws
 * @property-read \app\model\PartnerUser $inviter
 * @property-read \app\model\SocialToken[] $social_tokens
 * @property-read \app\model\UserTag[] $tags
 * @property-read mixed $access_tokens
 * @property-read mixed $finance_url
 * @property-read mixed $is_admin
 * @property-read mixed $is_certified
 * @property-read mixed $is_enterprise
 * @property-read mixed $roles
 * @property-read mixed $setting_url
 * @property-read mixed $ssl_url
 * @package app\model
 */
class User extends Model implements Authorizable
{

    const ASSISTANT_FEATURE = 1;

    use AuthorizableUser, Notifiable, StaticData;

    protected $visible = ['id', 'avatar', 'email', 'mobile', 'name', 'features', 'create_time'];
    protected $append  = ['is_certified', 'is_enterprise', 'roles'];

    protected $type = [
        'last_time' => Date::class,
    ];

    public function certification()
    {
        return $this->hasOne(Certification::class);
    }

    public function socialTokens()
    {
        return $this->hasMany(SocialToken::class);
    }

    public function accessTokens()
    {
        return $this->morphMany(AccessToken::class, 'accessible');
    }

    public function applications()
    {
        return $this->belongsToMany(Application::class, 'application_user');
    }

    public function enterprise()
    {
        return $this->hasOne(Enterprise::class);
    }

    public function apis()
    {
        return $this->belongsToMany(Api::class, ApiUser::class);
    }

    public function ai()
    {
        return $this->hasOne(Ai::class);
    }

    public function assistantSites()
    {
        return $this->belongsToMany(AssistantSite::class, AssistantSiteMember::class, 'site_id', 'user_id');
    }

    public function tags()
    {
        return $this->belongsToMany(UserTag::class, 'user_tag_map', 'tag_id');
    }

    public function inviter()
    {
        return $this->hasOne(PartnerUser::class);
    }

    protected function getIsCertifiedAttr()
    {
        return $this->isCertified();
    }

    protected function getIsEnterpriseAttr()
    {
        return $this->isEnterprise();
    }

    protected function getAvatarAttr($avatar)
    {
        if (!empty($avatar)) {
            if (Str::startsWith($avatar, 'http')) {
                return $avatar;
            }
            $url = $this->invoke(function (Filesystem $filesystem) use ($avatar) {
                return $filesystem->disk('uploads')->url($avatar);
            });
        } else {
            $url = "/avatar/" . md5($this->mobile);
        }

        if (!Str::startsWith($url, 'http')) {
            return (string) main_url($url);
        }
        return $url;
    }

    /**
     * 是否为沉默用户
     */
    public function isInactive()
    {
        if (empty($this->inviter) || $this->inviter->create_time->lt(Date::now()->subDays(90))) {
            $charge = Charge::where('user_id', $this->id)
                ->where('is_paid', 1)
                ->where('create_time', '>', Date::now()->subYear())
                ->find();

            return empty($charge);
        }
        return false;
    }

    /**
     * 是否实名认证过
     * @return bool
     */
    public function isCertified()
    {
        return $this->certification && $this->certification->passed;
    }

    /**
     * 是否通过企业认证
     * @return bool
     */
    public function isEnterprise()
    {
        return $this->enterprise && $this->enterprise->status == 1;
    }

    /**
     * @param $mobile
     * @return self
     */
    public static function retrieveByMobile($mobile, $ip = null)
    {
        $user = self::where('mobile', $mobile)->find();
        if (empty($user)) {
            //自动注册
            $user = self::create([
                'mobile' => $mobile,
                'name'   => '用户' . substr($mobile, -4),
                'reg_ip' => $ip,
            ]);
            //
            event(new Register($user));
        }

        return $user;
    }

    protected function getRolesAttr()
    {
        return array_map(function (Role $role) {
            return $role->getName();
        }, $this->getRoles());
    }

    protected function getIsAdminAttr()
    {
        return in_array($this->mobile, explode(',', config('app.admin', '')));
    }

    protected function getSettingUrlAttr()
    {
        return (string) console_url('/user');
    }

    protected function getSslUrlAttr()
    {
        return (string) console_url('/ssl');
    }

    protected function getFinanceUrlAttr()
    {
        return (string) console_url('/finance');
    }

    public function getRoles()
    {
        $roles = [];
        if ($this->is_admin) {
            $roles[] = new Role('admin');
        }
        return $roles;
    }

    public function resetToken()
    {
        $this->save([
            'remember_token' => Str::random(60),
        ]);
    }

    /**
     * 云币记录
     * @return HasMany
     */
    public function coinLogs()
    {
        return $this->hasMany(CoinLog::class);
    }

    /**
     * 更新云币
     * @param integer $type
     * @param integer $number
     * @param string $info
     * @param null|Application $application
     * @param null|Model $source
     * @return CoinLog
     */
    public function updateCoin($type, $number, $info, $application = null, $source = null)
    {
        return $this->transaction(function () use ($source, $type, $application, $info, $number) {

            if ($type == CoinLog::TYPE_DEC) {
                $this->dec('coin', $number);
            } else {
                $this->inc('coin', $number);
            }

            $this->save();

            $data = [
                'type'   => $type,
                'number' => $number,
                'info'   => $info,
            ];

            if ($application) {
                $data['application_id'] = $application->id;
            }

            if ($source) {
                $pk = $source->getPk();

                $data['source_type'] = get_class($source);
                $data['source_id']   = $source->{$pk};
            }

            return $this->coinLogs()->save($data);
        });
    }

    public function syncCoin()
    {
        $coin = $this->coinLogs()->sum(new Raw('if(type=1, number, -number)'));

        $this->save([
            'coin' => $coin,
        ]);
    }

    public function coinWithdraws()
    {
        return $this->hasMany(CoinWithdraw::class);
    }

    public function moneyLogs()
    {
        return $this->hasMany(MoneyLog::class);
    }

    /**
     * 更新余额
     * @param integer $type
     * @param integer $number
     * @param string $info
     * @param null|Application $application
     * @param null|Model $source
     * @return MoneyLog
     */
    public function updateMoney($type, $number, $info, $application = null, $source = null)
    {
        return $this->transaction(function () use ($source, $type, $application, $info, $number) {

            if ($type == MoneyLog::TYPE_DEC) {
                $this->dec('money', $number);
            } else {
                $this->inc('money', $number);
            }

            $this->save();

            $data = [
                'type'   => $type,
                'number' => $number,
                'info'   => $info,
            ];

            if ($application) {
                $data['application_id'] = $application->id;
            }

            if ($source) {
                $pk = $source->getPk();

                $data['source_type'] = get_class($source);
                $data['source_id']   = $source->{$pk};
            }

            return $this->moneyLogs()->save($data);
        });
    }

    public function syncMoney()
    {
        $money = $this->moneyLogs()->sum(new Raw('if(type=1, number, -number)'));

        $this->save([
            'money' => $money,
        ]);
    }

    public function moneyWithdraws()
    {
        return $this->hasMany(MoneyWithdraw::class);
    }

    /**
     * @return mixed
     */
    public static function getAdmins()
    {
        return User::whereIn('mobile', config('app.admin', ''))->cache(24 * 3600)->select();
    }
}
