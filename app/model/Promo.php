<?php

namespace app\model;

use app\lib\Date;
use think\exception\ValidateException;
use think\Model;

/**
 * Class app\model\Promo
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property array $product
 * @property int $discount
 * @property int $id
 * @property int $partner_id
 * @property int $rebate
 * @property string $code
 * @property string $title
 * @property-read \app\model\Charge[] $charges
 * @property-read \app\model\Partner $partner
 */
class Promo extends Model
{

    protected $type = [
        'expire_time' => Date::class,
    ];

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function charges()
    {
        return $this->hasMany(Charge::class)->where('is_paid', 1);
    }

    protected function setProductAttr($value)
    {
        return implode(',', $value);
    }

    protected function getProductAttr($value)
    {
        return explode(',', $value);
    }

    public function isExpired()
    {
        return $this->expire_time && $this->expire_time->isBefore(Date::now());
    }

    public function getRebate(Charge $charge)
    {
        return floor($charge->net_amount * $this->rebate / 100);
    }

    public function getDiscount(Charge $charge)
    {
        if ($this->isExpired()) {
            throw new ValidateException('该优惠码已过期');
        }
        $discount = false;
        if ($order = $charge->ord) {
            $product = $order->goods->getProduct()?->value;
            if (in_array($product, $this->product)) {
                $max      = $order->goods->getMaxDiscount();
                $discount = min($charge->amount * $this->discount / 100, $max);
            }
        } elseif ($application = $charge->application) {
            if (in_array($application->product, $this->product)) {
                $discount = $charge->amount * $this->discount / 100;
            }
        }

        if ($discount === false) {
            throw new ValidateException('该优惠码不适用于此订单');
        }

        return floor($discount);
    }
}
