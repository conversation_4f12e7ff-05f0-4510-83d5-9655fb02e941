<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\WxaWeaccount
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $robot
 * @property int $id
 * @property int $status
 * @property string $app_id
 * @property string $avatar
 * @property string $nickname
 * @property string $refresh_token
 * @property string $signature
 */
class WxaWeaccount extends Model
{
    protected $hidden = ['refresh_token'];

    protected function getRobotAttr($value)
    {
        $default = [
            'enable' => false,
        ];

        if ($value) {
            return array_merge_deep($default, json_decode($value, true));
        }
        return $default;
    }
}
