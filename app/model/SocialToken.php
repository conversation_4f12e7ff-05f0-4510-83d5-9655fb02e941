<?php

namespace app\model;

use think\Model;
use yunwuxin\social\User as SocialUser;

/**
 * Class app\model\SocialToken
 *
 * @property int $id
 * @property int $user_id
 * @property string $channel
 * @property string $openid
 * @property-read \app\model\User $user
 */
class SocialToken extends Model
{
    protected $autoWriteTimestamp = false;

    /**
     * @param SocialUser $socialUser
     * @return SocialToken|null
     */
    public static function getBySocialUser(SocialUser $socialUser)
    {
        $id      = $socialUser->getId();
        $channel = $socialUser->getChannel();

        return self::where('channel', $channel)->where('openid', $id)->find();
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
