<?php

namespace app\model;

use think\Model;

/**
 * Class app\model\WxaTemplate
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $template_id
 * @property string $desc
 * @property string $name
 * @property string $version
 * @property-read \app\model\WxaWeapp[] $weapps
 */
class WxaTemplate extends Model
{
    public function weapps()
    {
        return $this->hasMany(WxaWeapp::class, 'template_id', 'id')->where('status', 1);
    }
}
