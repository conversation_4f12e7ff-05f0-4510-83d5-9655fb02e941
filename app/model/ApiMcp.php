<?php

namespace app\model;

use app\lib\Hashids;
use think\Model;

class ApiMcp extends Model
{
    protected $append = ['endpoint'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    protected function setApisAttr($value)
    {
        return implode(',', $value);
    }

    protected function getApisAttr($value)
    {
        $ids = explode(',', $value);

        return $this->user->apis()->whereIn('id', $ids)->select();
    }

    protected function getEndpointAttr()
    {
        $hash = Hashids::encode($this->id, 'mcp');
        return (string) api_url("/mcp/{$hash}/sse");
    }

    public static function getByHash($hash)
    {
        $id = Hashids::decode($hash, 'mcp');
        return self::findOrFail($id);
    }
}
