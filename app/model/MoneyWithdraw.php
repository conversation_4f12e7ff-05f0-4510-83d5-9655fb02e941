<?php

namespace app\model;

use app\lib\alipay\FundTransfer;
use app\lib\Hashids;
use app\lib\StaticData;
use think\exception\ValidateException;
use think\facade\Db;
use think\Model;
use yunwuxin\pay\Payment;

/**
 * Class app\model\MoneyWithdraw
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $amount
 * @property int $id
 * @property int $status
 * @property int $user_id
 * @property string $account
 * @property string $name
 * @property string $remark
 * @property-read \app\model\User $user
 * @property-read mixed $order_id
 * @property-read mixed $status_color
 * @property-read mixed $status_text
 */
class MoneyWithdraw extends Model
{
    use StaticData;

    protected $append = ['status_text', 'status_color'];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    protected function getOrderIdAttr()
    {
        return Hashids::encode($this->id, config('app.app_host'));
    }

    public function confirm()
    {
        if ($this->status != 0) {
            throw new ValidateException('只能操作正在处理中的提现');
        }

        $this->invoke(function (Payment $payment) {
            $channel = $payment->channel('alipay');

            $bizContent = [
                'out_biz_no'   => $this->order_id,
                'trans_amount' => $this->amount / 100,
                'product_code' => 'TRANS_ACCOUNT_NO_PWD',
                'biz_scene'    => 'DIRECT_TRANSFER',
                'order_title'  => "余额提现#{$this->id}",
                'payee_info'   => [
                    'identity'      => $this->getAttr('account'),
                    'identity_type' => 'ALIPAY_LOGON_ID',
                    'name'          => $this->getAttr('name'),
                ],
            ];

            $request = $channel->createRequest(FundTransfer::class, $bizContent);

            $channel->sendRequest($request);

            $this->save(['status' => 1]);
        });
    }

    /**
     * 取消提现
     * @param $remark
     */
    public function cancel($remark)
    {
        if (!in_array($this->getData('status'), [0, 2])) {
            throw new ValidateException('只能取消正在处理中的提现');
        }
        Db::transaction(function () use ($remark) {
            $this->user->updateMoney(MoneyLog::TYPE_INC, $this->amount, "取消提现#{$this->id}");
            $this->save(['status' => -1, 'remark' => $remark]);
        });
    }

    protected function getStatusTextAttr()
    {
        return match ($this->status) {
            1 => '已完成',
            0 => '待审核',
            2 => '结算中',
            -1 => '已取消',
        };
    }

    protected function getStatusColorAttr()
    {
        return match ($this->status) {
            1 => 'success',
            0 => 'info',
            2 => 'primary',
            -1 => 'danger',
        };
    }
}
