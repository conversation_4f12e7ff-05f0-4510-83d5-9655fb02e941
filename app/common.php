<?php
// 应用公共文件
use app\lib\Date;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\Extension\CommonMark\Node\Inline\Link;
use League\CommonMark\Extension\DefaultAttributes\DefaultAttributesExtension;
use League\CommonMark\Extension\GithubFlavoredMarkdownExtension;
use League\CommonMark\MarkdownConverter;
use think\facade\Db;

function asset($path)
{
    static $manifest = null;
    if (!isset($manifest)) {
        $manifestDirectory = root_path('asset/main/dist/asset');

        $manifestPath = $manifestDirectory . '/manifest.json';
        if (!is_file($manifestPath)) {
            throw new Exception('The manifest does not exist.');
        }

        $manifest = json_decode(file_get_contents($manifestPath), true);
    }

    if (!isset($manifest[$path])) {
        throw new Exception("Unable to locate file: {$path}.");
    }

    return $manifest[$path];
}

function main_url(string $url = '', array $vars = [], $suffix = true)
{
    $host   = config('app.app_host');
    $parsed = parse_url($host);

    return url($url, $vars, $suffix)->domain($parsed['host'])->https($parsed['scheme'] == 'https');
}

function console_url(string $url = '', array $vars = [], $suffix = true)
{
    $host   = config('app.console_host');
    $parsed = parse_url($host);

    return url($url, $vars, $suffix)->domain($parsed['host'])->https($parsed['scheme'] == 'https');
}

function api_url(string $url = '', array $vars = [], $suffix = true)
{
    $host   = config('app.api_host');
    $parsed = parse_url($host);
    return url($url, $vars, $suffix)->domain($parsed['host'])->https($parsed['scheme'] == 'https');
}

function setting($name = null, $value = null)
{
    if ($value !== null) {
        Db::table('setting')->where('name', $name)->update(['value' => $value]);
    } else {
        if ($name == null) {
            return array_map(function ($value) {
                if ('false' === $value) {
                    $value = false;
                } elseif ('true' === $value) {
                    $value = true;
                }

                return $value;
            }, Db::table('setting')->column('value', 'name'));
        } else {
            $value = Db::table('setting')->where('name', $name)->failException(true)->value('value');
            if ('false' === $value) {
                $value = false;
            } elseif ('true' === $value) {
                $value = true;
            }

            return $value;
        }
    }
}

function array_merge_deep(array ...$arrays): array
{
    $result = [];
    foreach ($arrays as $array) {
        foreach ($array as $key => $value) {
            if (is_integer($key)) {
                $result[] = $value;
            } elseif (isset($result[$key]) && is_array($result[$key]) && is_array($value)) {
                $result[$key] = array_merge_deep(
                    $result[$key],
                    $value,
                );
            } else {
                $result[$key] = $value;
            }
        }
    }
    return $result;
}

function array_flat_map(callable $fn, array $array): array
{
    return array_merge(...array_map($fn, $array));
}

function fill_data($dateList, $data, $dateKey = 'date', $valueKey = 'value', $alias = null)
{
    if ($data instanceof \think\Collection) {
        $data = $data->toArray();
    }
    $keys = array_column($data, $dateKey);
    array_multisort($keys, SORT_ASC, $data);

    $result  = [];
    $dataKey = 0;

    foreach ($dateList as $date) {
        if (!empty($data[$dataKey]) && Date::parse($data[$dataKey][$dateKey])->equalTo($date)) {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => (int) $data[$dataKey][$valueKey],
            ];
            $dataKey++;
        } else {
            $result[] = [
                $dateKey            => $date,
                $alias ?? $valueKey => 0,
            ];
        }
    }

    return $result;
}

function get_ck_date_query($field, $unit = 'hour')
{
    $formats = [
        'minute' => '%Y-%m-%d %H:%i:00',
        'hour'   => '%Y-%m-%d %H:00:00',
        'day'    => '%Y-%m-%d 00:00:00',
        'week'   => '%YW%V',
        'month'  => '%Y-%m-01',
        'year'   => '%Y-01-01',
    ];

    return "formatDateTime({$field}, '{$formats[$unit]}')";
}

function get_date_query($field, $unit = 'hour')
{
    $formats = [
        'minute' => '%Y-%m-%d %H:%i:00',
        'hour'   => '%Y-%m-%d %H:00:00',
        'day'    => '%Y-%m-%d 00:00:00',
        'week'   => '%xW%v',
        'month'  => '%Y-%m-01',
        'year'   => '%Y-01-01',
    ];

    return "date_format({$field}, '{$formats[$unit]}')";
}

/**
 * @param $period
 * @return array<Date, Date, string, array>
 */
function get_period($period = '24hours')
{
    switch ($period) {
        case '1year':
            $start  = Date::now()->subYears(1)->addMonth()->startOfMonth();
            $end    = Date::now()->endOfMonth();
            $unit   = 'month';
            $format = 'Y-m-01';
            break;
        case 'half-year':
            $start  = Date::now()->subMonths(6)->addMonth()->startOfMonth();
            $end    = Date::now()->endOfMonth();
            $unit   = 'month';
            $format = 'Y-m-01';
            break;
        case '6months':
            $start  = Date::now()->subMonths(6)->addWeek()->startOfWeek();
            $end    = Date::now()->endOfWeek();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '180days':
            $start  = Date::now()->subDays(180)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'week';
            $format = 'Y-m-d';
            break;
        case '90days':
            $start  = Date::now()->subDays(90)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case 'last-month':
            $start  = Date::now()->subMonth()->startOfMonth();
            $end    = Date::now()->subMonth()->endOfMonth();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '30days':
            $start  = Date::now()->subDays(30)->addDay()->startOfDay();
            $end    = Date::now()->endOfDay();
            $unit   = 'day';
            $format = 'Y-m-d';
            break;
        case '7days':
            $start  = Date::now()->subDays(7)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '3days':
            $start  = Date::now()->subDays(3)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case 'yesterday':
            $start  = Date::yesterday()->startOfDay();
            $end    = Date::yesterday()->endOfDay();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
            break;
        case '24hours':
        default:
            $start  = Date::now()->subHours(24)->addHour()->startOfHour();
            $end    = Date::now()->endOfHour();
            $unit   = 'hour';
            $format = 'Y-m-d H:00:00';
    }

    $list = [];
    $date = $start->copy();
    do {
        if ($date->gt($end)) {
            break;
        }
        $list[] = $date->format($format);
    } while ($date = $date->add($unit, 1));

    return [$start, $end, $unit, $list];
}

function markdown($text)
{
    $config = [
        'renderer'           => [
            'soft_break' => "<br />\n",
        ],
        'default_attributes' => [
            Link::class => [
                'target' => '_blank',
            ],
        ],
    ];

    $environment = new Environment($config);

    $environment->addExtension(new CommonMarkCoreExtension());
    $environment->addExtension(new GithubFlavoredMarkdownExtension());
    $environment->addExtension(new DefaultAttributesExtension());

    $converter = new MarkdownConverter($environment);

    return (string) $converter->convert($text);
}

function is_debug()
{
    return app()->isDebug();
}
