<?php

namespace app\notification;

use app\model\User;
use app\notification\channel\Inbox;
use think\queue\ShouldQueue;
use yunwuxin\Notification;

class Message extends Notification implements ShouldQueue
{

    public function __construct(
        protected string           $content,
        protected                  $from = null,
        protected ?string          $message = null,
        protected ?string          $url = null,
        protected string|bool|null $sms = null
    )
    {
    }

    public function channels($notifiable)
    {
        return [Inbox::class];
    }

    /**
     * @param \app\model\User|int $notifiable
     * @return array
     */
    public function toInbox($notifiable)
    {
        if ($notifiable instanceof User) {
            $notifiable = $notifiable->id;
        }

        return [
            'to_id'   => $notifiable,
            'content' => $this->content,
            'from_id' => $this->from,
            'url'     => $this->url,
            'sms'     => $this->sms,
        ];
    }
}
