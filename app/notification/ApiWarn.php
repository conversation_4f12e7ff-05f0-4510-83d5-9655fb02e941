<?php

namespace app\notification;

use app\model\User;
use app\notification\channel\Sms;
use think\queue\ShouldQueue;
use yunwuxin\Notification;

class ApiWarn extends Notification implements ShouldQueue
{
    public $api;
    public $nums;

    public function __construct($api, $nums)
    {
        $this->api  = $api;
        $this->nums = $nums;
    }

    public function channels($notifiable)
    {
        return [Sms::class];
    }

    /**
     * @param User $notifiable
     * @return array
     */
    public function toSms($notifiable)
    {
        return [
            'to'       => $notifiable->mobile,
            'template' => 'X7axk9ey',
            'param'    => [
                'api'  => $this->api,
                'nums' => $this->nums,
            ],
        ];
    }
}
