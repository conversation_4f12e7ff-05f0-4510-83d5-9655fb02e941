<?php

namespace app\notification\channel;

use RuntimeException;
use think\api\Client;
use yunwuxin\Notification;
use yunwuxin\notification\Channel;
use yunwuxin\notification\Notifiable;

class Sms extends Channel
{

    public function __construct(protected Client $client)
    {
    }

    /**
     * 发送通知
     * @param Notifiable $notifiable
     * @param Notification $notification
     */
    public function send($notifiable, Notification $notification)
    {
        $message = $this->getMessage($notifiable, $notification);

        $result = $this->client
            ->smsSend()
            ->withPhone($message['to'])
            ->withSignId('olejRRej')
            ->withTemplateId($message['template'])
            ->withParams(json_encode($message['param']))
            ->request();

        if ($result['code'] != 0) {
            throw new RuntimeException($result['message']);
        }
    }
}
