<?php

namespace app\notification;

use app\model\User;
use app\notification\channel\Sms;
use think\queue\ShouldQueue;
use yunwuxin\Notification;

class SslWarn extends Notification implements ShouldQueue
{

    public function __construct(public $date, public $domain)
    {

    }

    public function channels($notifiable)
    {
        return [Sms::class];
    }

    /**
     * @param User $notifiable
     * @return array
     */
    public function toSms($notifiable)
    {
        return [
            'to'       => $notifiable->mobile,
            'template' => '4oeERva0',
            'param'    => [
                'date' => $this->date,
                'site' => $this->domain,
            ],
        ];
    }
}
