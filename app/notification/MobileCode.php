<?php

namespace app\notification;

use app\notification\channel\Sms;
use think\queue\ShouldQueue;
use yunwuxin\Notification;
use yunwuxin\notification\Notifiable;

class MobileCode extends Notification implements ShouldQueue
{
    protected $mobile;
    protected $code;

    public function __construct($mobile, $code)
    {
        $this->mobile = $mobile;
        $this->code   = $code;
    }

    /**
     * 发送渠道
     * @param Notifiable $notifiable
     * @return array
     */
    public function channels($notifiable)
    {
        return [Sms::class];
    }

    public function toSms($notifiable)
    {
        return [
            'to'       => $this->mobile,
            'template' => 'olejRejN',
            'param'    => [
                'code' => $this->code,
            ],
        ];
    }

}
