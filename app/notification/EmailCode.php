<?php

namespace app\notification;

use app\model\User;
use think\queue\ShouldQueue;
use yunwuxin\Notification;
use yunwuxin\notification\message\Mail;

class EmailCode extends Notification implements ShouldQueue
{
    public function __construct(public $email, public $code)
    {
    }

    public function channels($notifiable)
    {
        return ['mail'];
    }

    /**
     * @param User $notifiable
     * @return Mail
     */
    public function toMail($notifiable)
    {
        return (new Mail())
            ->subject('邮箱验证码')
            ->line('您的邮箱验证码为：')
            ->action($this->code)
            ->to($this->email)
            ->line('此验证码10分钟内有效。为了保障您的账户安全，请勿向他人泄漏验证码信息。');
    }
}
