<?php
declare (strict_types = 1);

namespace app;

use app\model\User;
use Closure;
use ReflectionFunction;
use think\App;
use think\db\Query;
use think\Model;
use think\Request;
use think\Validate;
use think\View;
use yunwuxin\Auth;
use yunwuxin\auth\exception\AuthorizationException;
use yunwuxin\facade\Gate;

interface MiddlewareOption
{
    public function only($methods);

    public function except($methods);
}

/**
 * 控制器基础类
 * @method initialized()
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var \app\Request
     */
    protected $request;

    /**
     * 应用实例
     * @var \think\App
     */
    protected $app;

    /**
     * @var User
     */
    protected $user;

    /**
     * 控制器中间件
     * @var array
     */
    protected $middleware = [];

    /**
     * 构造方法
     * @access public
     * @param App $app 应用对象
     */
    public function __construct(App $app, Auth $auth, View $view)
    {
        $this->app     = $app;
        $this->request = $this->app->request;
        $this->user    = $auth->user();

        if ($this->request->isGet()) {
            $view->assign('request', $this->request);
            $view->assign('user', $this->user);
        }
    }

    protected function middleware($middleware, ...$params)
    {
        $options = [];

        if ($middleware instanceof Closure) {
            $ref = new ReflectionFunction($middleware);

            if ($ref->getNumberOfParameters() == 0) {
                $middleware = function (Request $request, $next) use ($middleware) {
                    $middleware();
                    return $next($request);
                };
            }
        }

        $this->middleware[] = [
            'middleware' => [$middleware, $params],
            'options'    => &$options,
        ];

        return new class($options) implements MiddlewareOption {
            protected $options;

            public function __construct(array &$options)
            {
                $this->options = &$options;
            }

            public function only($methods)
            {
                $this->options['only'] = is_array($methods) ? $methods : func_get_args();
                return $this;
            }

            public function except($methods)
            {
                $this->options['except'] = is_array($methods) ? $methods : func_get_args();

                return $this;
            }
        };
    }

    protected function authorized($ability, ...$args)
    {
        $result = Gate::raw($ability, ...$args);

        if ($result !== true) {
            throw new AuthorizationException($result);
        }
    }

    protected function initialize()
    {
        foreach (array_reverse(class_uses_recursive($this)) as $trait) {
            if (method_exists($this, $method = 'initialize' . class_basename($trait))) {
                $this->$method();
            }
        }

        if (method_exists($this, $method = 'initialized')) {
            $this->$method();
        }
    }

    /**
     * 验证数据
     * @access protected
     * @param string|array $validate 验证器名或者验证规则数组
     * @param array $message 提示信息
     */
    protected function validate($validate, array $message = [], array $params = null)
    {
        $v = new Validate();
        $v->rule(array_filter($validate));
        $v->message($message);
        $v->batch(true);

        if (is_null($params)) {
            $params = $this->request->all();
        }

        $v->failException(true)->check($params);

        $data = [];

        foreach ($validate as $key => $rule) {
            if (strpos($key, '|')) {
                // 字段|描述 用于指定属性名称
                [$key] = explode('|', $key);
            }
            if (array_key_exists($key, $params)) {
                $data[$key] = $params[$key];
            }
        }

        return $data;
    }

    /**
     * @param Query|Model $query
     * @param $field
     * @param $q
     * @return void
     */
    protected function searchField($query, $field, $q = 'q')
    {
        if ($this->request->has($q)) {
            $qs = $this->request->param($q);
            $query->whereLike($field, "%{$qs}%");
        }
    }

    /**
     * @param Query|Model|\think\model\Relation $query
     * @param $fields
     * @return void
     */
    protected function filterFields($query, $fields)
    {
        foreach ($fields as $key => $value) {
            if (is_string($key)) {
                if ($this->request->has($key)) {
                    if ($value === true) {
                        $query->where($key, $this->request->param($key));
                    } elseif (is_callable($value)) {
                        call_user_func($value, $query, $this->request->param($key));
                    }
                }
            } elseif ($this->request->has($value)) {
                $condition = $this->request->param($value);
                if (is_array($condition)) {
                    $query->whereIn($value, $condition);
                } else {
                    $query->whereLike($value, "%{$this->request->param($value)}%");
                }
            }
        }
    }

}
