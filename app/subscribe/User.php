<?php

namespace app\subscribe;

use app\event\Register;
use app\lib\Date;
use app\model\PartnerUser;
use think\Event;
use think\Request;
use think\Session;
use yunwuxin\auth\event\Login;

class User
{

    public function onLogin(Session $session, Request $request, Login $login)
    {
        /** @var \app\model\User $user */
        $user = $login->user;

        $user->save([
            'last_ip'   => $request->ip(),
            'last_time' => Date::now(),
        ]);

        //CPS用户关联
        if ($session->has('cps_key')) {
            if ($user->isInactive()) {
                PartnerUser::association($session->get('cps_key'), $user);
            }
            $session->delete('cps_key');
        }
    }

    public function onRegister(Session $session, Register $register)
    {
        //CPS用户关联
        if ($session->has('cps_key')) {
            PartnerUser::association($session->get('cps_key'), $register->user);
            $session->delete('cps_key');
        }
    }

    public function subscribe(Event $event)
    {
        $event->listen(Login::class, [$this, 'onLogin']);
        $event->listen(Register::class, [$this, 'onRegister']);
    }
}
