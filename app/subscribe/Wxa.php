<?php

namespace app\subscribe;

use app\job\InitWeappJob;
use app\lib\Date;
use app\lib\wechat\Message;
use app\lib\wechat\wxa\Application;
use app\model\UserWeaccount;
use app\model\WxaWeaccount;
use app\model\WxaWeapp;
use think\Event;
use think\helper\Str;

class Wxa
{

    public function onRegister(Application $application, Message $message)
    {
        //快速注册小程序
        $info = $message['info'];

        $identity = md5($info['name'] . $info['code'] . $info['legal_persona_wechat'] . $info['legal_persona_name']);

        $weapp = WxaWeapp::where('identity', $identity)->where('create_time', '>', Date::yesterday())
            ->where('status', 0)->order('id desc')->find();

        if ($weapp) {
            if ($message['status'] == 0) {

                $result = $application->getAuthorization($message['auth_code']);

                $weapp->save([
                    'app_id'        => $message['appid'],
                    'refresh_token' => $result->getRefreshToken(),
                    'status'        => 2,
                ]);

                queue(InitWeappJob::class, $weapp->id, queue: 'retry');
            } else {
                //注册失败
                $weapp->save([
                    'status' => -1,
                ]);
            }
        }
    }

    public function onUnauthorized(Message $message)
    {
        $weaccount = WxaWeaccount::where('app_id', $message->AuthorizerAppid)->find();

        if ($weaccount) {
            $weaccount->save([
                'status' => 0,
            ]);
        }
    }

    public function onUnsubscribe(Message $message)
    {
        if ($message->Appid == env('NOTIFY_APPID')) {
            //取消订阅通知
            UserWeaccount::update(['openid' => null], ['openid' => $message->FromUserName]);
        }
    }

    public function onSubscribe(Message $message)
    {
        if ($message->Appid == env('NOTIFY_APPID') && Str::startsWith($message->EventKey, 'qrscene_notification:')) {
            //订阅通知
            [, $userId] = explode(':', $message->EventKey, 2);
            if ($userId) {
                UserWeaccount::update(['openid' => $message->FromUserName], ['user_id' => $userId]);
                return '通知提醒订阅成功！';
            }
        }
    }

    public function onScan(Message $message)
    {
        if ($message->Appid == env('NOTIFY_APPID') && Str::startsWith($message->EventKey, 'notification:')) {
            //订阅通知
            [, $userId] = explode(':', $message->EventKey, 2);
            if ($userId) {
                UserWeaccount::update(['openid' => $message->FromUserName], ['user_id' => $userId]);
                return '通知提醒订阅成功！';
            }
        }
    }

    public function onText(Message $message)
    {

    }

    public function subscribe(Event $event)
    {
        $event->listen('wxa.notify_third_fasteregister', [$this, 'onRegister']);
        $event->listen('wxa.unauthorized', [$this, 'onUnauthorized']);

        $event->listen('wxa.biz.unsubscribe', [$this, 'onUnsubscribe']);
        $event->listen('wxa.biz.subscribe', [$this, 'onSubscribe']);
        $event->listen('wxa.biz.scan', [$this, 'onScan']);
        $event->listen('wxa.biz.text', [$this, 'onText']);
    }
}
