<?php

namespace app\middleware;

use app\model\User;
use Closure;
use think\Request;
use think\Response;
use think\Session;
use yunwuxin\Auth;

class AuthenticationSession
{
    public function __construct(protected Session $session, protected Auth $auth)
    {

    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        /** @var User $user */
        $user = $this->auth->user();
        if ($user) {
            if (!$this->session->has('remember_token')) {
                $this->session->set('remember_token', $user->remember_token);
            } elseif ($this->session->get('remember_token') != $user->remember_token) {
                $this->session->delete('remember_token');
                $this->auth->logout();
            }
        }

        $response = $next($request);

        /** @var User $user */
        $user = $this->auth->user();

        if ($user) {
            $this->session->set('remember_token', $user->remember_token);
        }

        return $response;
    }

}
