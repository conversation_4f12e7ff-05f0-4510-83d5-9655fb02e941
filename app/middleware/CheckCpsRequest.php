<?php

namespace app\middleware;

use app\model\PartnerUser;
use Closure;
use think\Request;
use think\Response;
use think\Session;
use yunwuxin\Auth;

class CheckCpsRequest
{
    public function __construct(protected Session $session, protected Auth $auth)
    {

    }

    /**
     * @param Request $request
     * @param Closure $next
     * @return Response
     */
    public function handle($request, Closure $next)
    {
        if ($request->has('cps_key', 'get')) {
            $cpsKey = $request->get('cps_key');
            $user   = $this->auth->user();
            if ($user && !$this->session->has('cps_key')) {
                //沉默用户激活
                if ($user->isInactive()) {
                    PartnerUser::association($cpsKey, $user);
                }
            }

            $this->session->set('cps_key', $cpsKey);
        }
        return $next($request);
    }
}
