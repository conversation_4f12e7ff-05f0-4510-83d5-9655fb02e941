<?php

namespace app\command;

use app\model\Application;
use think\console\Command;

class CreateApp extends Command
{
    protected function configure()
    {
        $this->setName('app:create');
    }

    public function handle()
    {
        $name = $this->output->ask($this->input, 'What should we name the app?');

        $redirectUri = $this->output->ask($this->input, 'What should we redirect the request after authorization?');

        $app = Application::create([
            'name'          => $name,
            'redirect_uri'  => $redirectUri,
            'client_id'     => md5(uniqid()),
            'client_secret' => md5(uniqid()),
        ]);

        $this->output->info('New Client created successfully.');
        $this->output->newLine();

        $this->output->writeln('<warning>Client ID:</warning> ' . $app->client_id);
        $this->output->writeln('<warning>Client Secret:</warning> ' . $app->client_secret);
    }
}
