<?php

namespace app\command;

use app\lib\wechat\wxa\Application;
use think\console\Command;
use think\console\input\Argument;

class WxaTicket extends Command
{
    protected function configure()
    {
        $this->setName('wxa:ticket');
        $this->addArgument('ticket', Argument::REQUIRED);
    }

    public function handle(Application $application)
    {
        $ticket = $this->input->getArgument('ticket');
        $application->getVerifyTicket()->setTicket($ticket);
    }
}
