<?php

namespace app\command;

use app\lib\MiniAuth;
use think\console\Command;
use think\console\input\Argument;

class AuthorizePassport extends Command
{
    protected function configure()
    {
        $this->setName('authorize:passport')
            ->addArgument('token', Argument::REQUIRED);
    }

    protected function handle(MiniAuth $mini)
    {
        $token = $this->input->getArgument('token');

        $socialUser = \yunwuxin\social\User::make([
            'id'       => 'anonymous',
            'nickname' => '微信用户',
        ], []);

        $socialUser->setChannel('wechat');

        $mini->authorize($token, $socialUser);
    }
}
