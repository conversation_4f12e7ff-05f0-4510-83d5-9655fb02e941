<?php

namespace app\command;

use app\lib\Date;
use app\model\ApiUserPackage;
use think\console\Command;

class ArchiveApiLog extends Command
{
    protected function configure()
    {
        $this->setName('api:log:archive');
    }

    public function handle()
    {
        $startMonth = Date::now()->subMonths(6)->startOfMonth();
        $endMonth   = Date::now()->subMonths(1)->startOfMonth();

        $cursor = ApiUserPackage::where('create_time', '>', Date::parse('-2 years')->subDays(7))
            ->order('create_time asc')->cursor();

        /** @var ApiUserPackage $package */
        foreach ($cursor as $package) {
            $this->output->info("Start archive package [{$package->id}]");
            $current = $startMonth->copy();

            while ($current->lte($endMonth)) {
                $month = $current->format('Ym');
                $nums  = $package->archive($month);

                if ($nums > 0) {
                    $this->output->writeln("Archive month [{$month}] with {$nums} sms");
                } else {
                    $this->output->comment("Skip month [{$month}]");
                }

                $current = $current->addMonth();
            }

            $this->output->info("Finish archive package [{$package->id}]");

            $this->output->writeln('=============================');
        }
    }
}
