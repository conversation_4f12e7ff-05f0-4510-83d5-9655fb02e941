<?php

namespace app\enum;

enum Product: string
{
    case Api = 'api';
    case Sms = 'sms';
    case Ssl = 'ssl';
    case Assistant = 'assistant';
    case Ai = 'ai';
    case Chat = 'chat';
    case Bot = 'bot';
    case Wiki = 'wiki';
    case Q = 'q';

    public function label()
    {
        return match ($this) {
            self::Api => 'ThinkAPI',
            self::Sms => 'ThinkSMS',
            self::Ssl => 'ThinkSSL',
            self::Assistant => '运营助理',
            self::Ai => 'ThinkAI',
            self::Wiki => '知识管理',
            self::Chat => 'ThinkChat',
            self::Bot => 'ThinkBot',
            self::Q => '轻言',
        };
    }

}
