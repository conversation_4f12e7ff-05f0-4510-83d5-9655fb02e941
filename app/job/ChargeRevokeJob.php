<?php

namespace app\job;

use app\lib\WebhookRunner;
use app\model\Refund;
use yunwuxin\model\SerializesModel;

class ChargeRevokeJob
{
    use SerializesModel, WebhookRunner;

    public function __construct(protected Refund $refund)
    {
    }

    public function handle()
    {
        $this->refund->transaction(function () {
            $charge = $this->refund->charge;
            
            //退还佣金
            $charge->commission_log?->delete();

            if ($charge->user) {
                //返还云币
                if ($charge->coin > 0) {
                    $charge->coin_log?->delete();
                }
                //返还余额
                if ($charge->money > 0) {
                    $charge->money_log?->delete();
                }
            }
            if ($order = $charge->ord) {
                //本地订单处理
                $order->revoke();
            } elseif ($charge->revoke_url) {
                $payload = [
                    'trade_no'   => $charge->trade_no,
                    'order_no'   => $charge->order_no,
                    'amount'     => $charge->amount,
                    'revoke_url' => $charge->revoke_url,
                ];

                $this->run($charge->revoke_url, $payload, $charge->application->client_secret);
            }

            $this->refund->save(['status' => 1]);
        });
    }

    public function failed()
    {
        $this->refund->save(['status' => 0]);
    }
}
