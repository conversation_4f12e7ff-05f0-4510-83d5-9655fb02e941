<?php

namespace app\job;

use app\lib\Producer;
use think\facade\Log;
use think\queue\Job;

class KafkaRetryJob
{
    protected Producer $producer;

    public function __construct(Producer $producer)
    {
        $this->producer = $producer;
    }

    public function fire(Job $job, $data)
    {
        [$method, $arguments] = $data;

        try {
            // 尝试重新发送消息，使用directCall方法避免再次触发队列
            $this->producer->directCall($method, $arguments);
            $job->delete();
        } catch (\Throwable $e) {
            Log::error("[kafka-retry] method: {$method}, args: " . json_encode($arguments));
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());

            // 计算重试延迟时间，每次尝试都会增加延迟时间
            $attempts = $job->attempts();
            // 基础延迟时间为60秒，每次尝试翻倍，最多半小时（1800秒）
            $delay = min(60 * pow(2, $attempts - 1), 1800);

            Log::info("[kafka-retry] 第{$attempts}次重试，延迟{$delay}秒: method: {$method}");
            $job->release($delay);
        }
    }
}
