<?php

namespace app\job;

use app\lib\wechat\wxa\Application;
use app\model\WxaWeapp;
use ArrayObject;
use Exception;
use think\helper\Arr;
use think\queue\Job;

class ReleaseWeappJob
{
    public function __construct(protected Application $application)
    {
    }

    public function fire(Job $job, $id)
    {
        $weapp = WxaWeapp::find($id);
        if (empty($weapp)) {
            $job->delete();
            return;
        }
        $template = $weapp->template;

        if (version_compare($weapp->version, $template->version, '>=')) {
            $job->delete();
            return;
        }

        try {
            $app = $this->application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

            //查看版本
            $result = $app->getClient()->post('/wxa/getversioninfo', ['json' => new ArrayObject()]);

            $this->checkResult($result);

            //体验版
            $expVersion = Arr::get($result, 'exp_info.exp_version');

            if (version_compare($expVersion, $template->version, '<')) {
                //上传体验版
                $result = $app->getClient()->post('/wxa/commit', ['json' => [
                    'template_id'  => $template->template_id,
                    'ext_json'     => json_encode(['extAppid' => $weapp->app_id, 'ext' => $weapp->ext]),
                    'user_version' => $template->version,
                    'user_desc'    => $template->desc,
                ]]);

                $this->checkResult($result);
            }

            //获取最近的审核状态
            $audit = $app->getClient()->get('/wxa/get_latest_auditstatus');

            if (Arr::get($audit, 'errcode') != 85058) {
                $this->checkResult($audit);
            }

            $auditVersion = Arr::get($audit, 'user_version');
            if (version_compare($auditVersion, $template->version, '<')) {
                if (Arr::get($audit, 'status') === 2) {
                    //撤回审核
                    $app->getClient()->get('/wxa/undocodeaudit');
                }

                //提交审核
                $this->checkResult($app->getClient()->post('/wxa/submit_audit', ['json' => [
                    'privacy_api_not_use' => true,
                ]]));
            }

            if (version_compare($auditVersion, $template->version, '==')) {
                if (Arr::get($audit, 'status') === 0) {
                    //审核通过
                    //发布
                    $this->checkResult($app->getClient()
                        ->request('post', '/wxa/release', ['json' => new ArrayObject()]));
                    $weapp->save(['version' => $template->version]);
                    $job->delete();
                }

                if (Arr::get($audit, 'status') === 1) {
                    //审核拒绝
                    $job->delete();
                }
            }
        } catch (Exception) {
            $job->release(60);
        }
    }

    protected function checkResult($result)
    {
        if (Arr::get($result, 'errcode') != 0) {
            throw new Exception(Arr::get($result, 'errmsg'));
        }
    }
}
