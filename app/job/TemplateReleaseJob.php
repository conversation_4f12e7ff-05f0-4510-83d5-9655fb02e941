<?php

namespace app\job;

use app\model\WxaTemplate;
use think\queue\Job;

class TemplateReleaseJob
{
    public function fire(Job $job, $id)
    {
        $template = WxaTemplate::find($id);

        foreach ($template->weapps as $weapp) {
            if (version_compare($weapp->version, $template->version, '<')) {
                queue(ReleaseWeappJob::class, $weapp->id, queue: 'retry');
            }
        }

        $job->delete();
    }
}
