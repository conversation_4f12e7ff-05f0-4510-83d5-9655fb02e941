<?php

namespace app\job;

use app\lib\Date;
use app\lib\WebhookRunner;
use app\model\Charge;
use app\model\CoinLog;
use app\model\CommissionLog;
use app\model\MoneyLog;
use yunwuxin\model\SerializesModel;

class ChargeCompleteJob
{
    use SerializesModel, WebhookRunner;

    public function __construct(protected Charge $charge)
    {
    }

    public function handle()
    {
        $this->charge->transaction(function () {
            //佣金,自己购买不返佣金
            if ($this->charge->promo && $this->charge->promo->partner && $this->charge->promo->partner->user_id != $this->charge->user_id) {
                $commission = $this->charge->promo->getRebate($this->charge);
                if ($commission > 0) {
                    $this->charge->promo->partner->updateCommission(
                        CommissionLog::TYPE_INC,
                        $commission,
                        $this->charge->full_subject,
                        source: $this->charge
                    );
                }
            }

            if ($this->charge->user) {
                //扣除云币
                if ($this->charge->coin > 0) {
                    $this->charge->user->updateCoin(
                        CoinLog::TYPE_DEC,
                        $this->charge->coin,
                        $this->charge->full_subject,
                        source: $this->charge
                    );
                }
                //扣除余额
                if ($this->charge->money > 0) {
                    $this->charge->user->updateMoney(
                        MoneyLog::TYPE_DEC,
                        $this->charge->money,
                        $this->charge->full_subject,
                        source: $this->charge
                    );
                }

                //邀请返利 90天内
                if (empty($this->charge->promo)) {
                    $inviter = $this->charge->user->inviter;
                    if ($inviter && $inviter->create_time->gt(Date::now()->subDays(90))) {
                        $commission = $inviter->getRebate($this->charge);
                        if ($commission > 0) {
                            $inviter->partner->updateCommission(
                                CommissionLog::TYPE_INC,
                                $commission,
                                $this->charge->full_subject,
                                source: $this->charge
                            );
                        }
                    }
                }
            }

            if ($order = $this->charge->ord) {
                //本地订单处理
                $order->paid();
            } elseif ($this->charge->notify_url) {
                $payload = [
                    'trade_no'   => $this->charge->trade_no,
                    'order_no'   => $this->charge->order_no,
                    'amount'     => $this->charge->amount,
                    'subject'    => $this->charge->subject,
                    'pay_time'   => $this->charge->pay_time->toDateTimeString(),
                    'channel'    => $this->charge->channel,
                    'notify_url' => $this->charge->notify_url,
                ];

                $this->run($this->charge->notify_url, $payload, $this->charge->application->client_secret);
            }
            $this->charge->save(['status' => 1]);
        });
    }

    public function failed()
    {
        $this->charge->save(['status' => 0]);
    }
}
