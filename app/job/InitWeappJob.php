<?php

namespace app\job;

use app\lib\wechat\wxa\Application;
use app\model\WxaWeapp;
use think\queue\Job;

class InitWeappJob
{

    public function __construct(protected Application $application)
    {
    }

    public function fire(Job $job, $id)
    {
        $weapp = WxaWeapp::where('status', 2)->find($id);

        if ($weapp) {
            $app = $this->application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

            //设置服务域名
            $app->getClient()->post('/wxa/modify_domain_directly', ['json' => [
                'action'          => 'set',
                'requestdomain'   => [$weapp->domain],
                "wsrequestdomain" => [],
                "uploaddomain"    => [],
                "downloaddomain"  => [],
                "udpdomain"       => [],
                "tcpdomain"       => [],
            ]]);

            //设置业务域名
            $app->getClient()->post('/wxa/setwebviewdomain', ['json' => [
                'action'        => 'set',
                'webviewdomain' => [$weapp->domain],
            ]]);

            //设置类目
            $app->getClient()->post('/cgi-bin/wxopen/addcategory', ['json' => [
                'categories' => [
                    'first'  => 287,
                    'second' => 612,
                ],
            ]]);

            $weapp->save(['status' => 3]);
        }

        $job->delete();
    }
}
