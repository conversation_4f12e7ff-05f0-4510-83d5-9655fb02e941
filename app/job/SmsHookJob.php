<?php

namespace app\job;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use think\Cache;
use think\facade\Log;
use think\queue\Job;

class SmsHookJob
{
    const CACHE_KEY = 'sms_webhook_%s_error';

    public function __construct(protected Cache $cache)
    {
    }

    public function fire(Job $job, $data)
    {
        [$webhook, $payload] = $data;

        $key = sprintf(self::CACHE_KEY, $webhook);

        try {
            if (!$this->cache->has($key)) {
                $client = new Client();

                $headers = [
                    'X-TopThink-Event' => 'sms',
                    'X-ThinkPHP-Event' => 'sms',
                ];

                $client->request('POST', $webhook, [
                    'headers' => $headers,
                    'json'    => $payload,
                    'timeout' => 5,
                    'verify'  => false,
                ]);
            }
            $job->delete();
        } catch (Exception $e) {
            if ($job->attempts() <= 3) {
                $job->release(60);
            } else {
                if ($e instanceof RequestException) {
                    $trace = $e->getResponse()?->getBody()->getContents() ?? '';
                } else {
                    $trace = $e->getTraceAsString();
                }

                Log::write(sprintf("%s\n%s", $e->getMessage(), $trace));

                //标记异常
                $this->cache->set($key, true, 24 * 3600);
                $job->delete();
            }
        }
    }
}
