<?php

namespace app\job;

use app\model\Sms;
use app\model\SmsTask;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\facade\Filesystem;
use think\facade\Log;
use think\queue\Job;

class SmsBatchSendJob
{
    public function fire(Job $job, $id)
    {
        /** @var SmsTask */
        $task = SmsTask::where('status', 2)->find($id);

        if (!empty($task)) {
            $sms = Sms::getByUser($task->user_id);

            switch ($task->type) {
                case 'file':
                    //解析excel
                    $disk = Filesystem::disk('uploads');
                    $path = $disk->path($task->data);
                    $reader = new Xlsx();
                    $spreadsheet = $reader->load($path);

                    $sheets = [];

                    foreach ($spreadsheet->getAllSheets() as $sheet) {
                        $phoneName = $sheet->getCellByColumnAndRow(1, 1)->getFormattedValue();
                        if ($phoneName !== 'phone') {
                            continue;
                        }

                        $column      = $sheet->getHighestColumn(1);
                        $columnIndex = Coordinate::columnIndexFromString($column);

                        $varName = [];
                        for ($i = 2; $i <= $columnIndex; $i++) {
                            $varName[] = $sheet->getCellByColumnAndRow($i, 1)->getFormattedValue();
                        }

                        $phone = [];

                        $row = $sheet->getHighestRow();
                        for ($i = 2; $i <= $row; $i++) {
                            $to = $sheet->getCellByColumnAndRow(1, $i)->getFormattedValue();

                            if (empty($varName)) {
                                $phone[] = $to;
                            } else {
                                $varValue = [];
                                for ($j = 2; $j <= $columnIndex; $j++) {
                                    $varValue[] = $sheet->getCellByColumnAndRow($j, $i)->getFormattedValue();
                                }

                                $phone[] = [
                                    'to'   => $to,
                                    'vars' => array_combine($varName, $varValue),
                                ];
                            }
                        }

                        $sheets[] = [
                            'phone' => $phone,
                            'multi' => !empty($varName),
                        ];
                    }

                    $total = array_reduce($sheets, function ($carry, $item) {
                        return $carry + count($item['phone']);
                    }, 0);

                    $task->save([
                        'total' => $total,
                    ]);

                    foreach ($sheets as $sheet) {
                        //发送
                        if ($sheet['multi']) {
                            //每次最多200个
                            foreach (array_chunk($sheet['phone'], 200) as $phone) {
                                try {
                                    $sms->batchSend($task->template->sign_id, $task->template_id, json_encode($phone), taskId: $task->id);
                                } catch (\Exception $e) {
                                    Log::write(sprintf("%s\n%s", $e->getMessage(), $e->getTraceAsString()));
                                }
                            }
                        } else {
                            //每次最多10000个
                            foreach (array_chunk($sheet['phone'], 10000) as $phone) {
                                try {
                                    $sms->batchSend($task->template->sign_id, $task->template_id, implode(',', $phone), taskId: $task->id);
                                } catch (\Exception $e) {
                                    Log::write(sprintf("%s\n%s", $e->getMessage(), $e->getTraceAsString()));
                                }
                            }
                        }
                    }
                    break;
                case 'input':
                    $phone = $task->data;
                    $total = count(explode(',', $phone));

                    $task->save([
                        'total' => $total,
                    ]);

                    try {
                        $sms->batchSend($task->template->sign_id, $task->template_id, $phone, taskId: $task->id);
                    } catch (\Exception $e) {
                        Log::write(sprintf("%s\n%s", $e->getMessage(), $e->getTraceAsString()));
                    }
                    break;
            }

            $task->save([
                'status' => 1,
            ]);
        }

        $job->delete();
    }

    public function failed($id)
    {
        /** @var SmsTask */
        $task = SmsTask::find($id);

        $task->save(['status' => -2]);
    }
}
