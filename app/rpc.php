<?php

/**
 * This file is auto-generated.
 */

declare(strict_types=1);

namespace rpc\contract\api;

use think\swoole\rpc\client\Service;

interface Api extends Service
{
	public function getList();


	public function getInfo($name);


	public function run($uri, $params = []);


	public function meta($uri);
}


namespace rpc\contract\ai;

use think\swoole\rpc\client\Service;

interface Ai extends Service
{
	public function getModels($type = null, $status = null);


	public function getPlugins();
}
return ['api' => ['rpc\contract\api\Api'], 'ai' => ['rpc\contract\ai\Ai']];