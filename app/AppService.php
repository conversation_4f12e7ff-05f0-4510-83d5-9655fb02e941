<?php
declare (strict_types = 1);

namespace app;

use app\lib\wechat\wxa\Application;
use ReflectionClass;
use think\api\Client;
use think\App;
use think\Cache;
use think\Config;
use think\helper\Str;
use think\Model;
use think\Service;

/**
 * 应用服务类
 */
class AppService extends Service
{
    public function register()
    {
        $this->app->resolving(function ($instance, App $container) {
            if ($instance instanceof BaseController) {
                $container->invoke([$instance, 'initialize'], [], true);
            }
        });

        $this->app->bind(Client::class, function () {
            return new Client('6c27b428-a04b-4cac-aba6-161d9bc1e1ff');
        });

        //
        $this->app->bind(Application::class, function (Cache $cache, Config $config) {
            $application = new Application($config->get('wechat.wxa'));
            $application->setCache($cache);
            return $application;
        });

        $this->app->bind(\app\lib\wechat\mini\Application::class, function (Cache $cache, Config $config) {
            $application = new \app\lib\wechat\mini\Application($config->get('wechat.mini'));

            $application->setCache($cache);
            return $application;
        });

        $this->app->bind(\app\lib\wechat\account\Application::class, function (Cache $cache, Config $config) {
            $application = new \app\lib\wechat\account\Application($config->get('wechat.account'));

            $application->setCache($cache);
            return $application;
        });

        Model::maker(function (Model $model) {
            $class     = new ReflectionClass($model);
            $namespace = $class->getNamespaceName();

            if (str_starts_with($namespace, 'app\\model\\')) {

                $prefix = str_replace('app\\model\\', '', $namespace);
                $name   = $prefix . '_' . Str::snake($class->getShortName());

                $model->setName($name);
            }
        });
    }

    public function boot()
    {
        // 服务启动
    }
}
