<?php

namespace app\task;

use app\lib\Date;
use app\model\SslCertificate;
use app\notification\SslWarn;
use yunwuxin\cron\Task;
use yunwuxin\facade\Notification;

class CheckSslExpired extends Task
{
    protected function configure()
    {
        $this->onOneServer()->dailyAt('16:00');
    }

    protected function execute()
    {
        $start = Date::now()->addWeek()->startOfDay();
        $end   = $start->copy()->endOfDay();

        SslCertificate::whereBetween('expire_time', [$start, $end])
            ->where('status', 1)
            ->chunk(50, function ($list) {
                /** @var SslCertificate $certificate */
                foreach ($list as $certificate) {
                    $notification = new SslWarn($certificate->expire_time->toDateString(), $certificate->domain);
                    Notification::send($certificate->user, $notification);
                }
            });
    }
}
