<?php

namespace app\task;

use app\lib\Date;
use app\model\ApiUserPackage;
use Exception;
use think\facade\Log;
use yunwuxin\cron\Task;

class SyncApiPackageUsedNums extends Task
{
    protected function configure()
    {
        $this->onOneServer()->dailyAt('2:00');
    }

    public function handle()
    {
        $cursor = ApiUserPackage::where('create_time', '>', Date::parse('-2 years')->subDays(7))
            ->order('create_time asc')->cursor();

        /** @var ApiUserPackage $package */
        foreach ($cursor as $package) {
            try {
                $package->syncUsedNums();
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
