<?php

namespace app\task;

use app\model\SmsTask;
use Exception;
use think\facade\Log;
use yunwuxin\cron\Task;

class CheckSmsTask extends Task
{
    protected function configure()
    {
        $this->onOneServer()
            ->everyFiveMinutes();
    }

    public function handle()
    {
        $cursor = SmsTask::order('create_time asc')->whereIn('status', [0, 2])->cursor();

        /** @var SmsTask $task */
        foreach ($cursor as $task) {
            try {
                $task->run();
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
