<?php

namespace app\task;

use app\facade\Sms;
use app\lib\Date;
use app\model\SmsSign;
use Exception;
use think\facade\Log;
use think\helper\Arr;
use yunwuxin\cron\Task;

class SyncSmsSignStatus extends Task
{
    protected function configure()
    {
        $this->onOneServer()
            ->everyTenMinutes();
    }

    public function handle()
    {
        $cursor = SmsSign::where('status', 3)->where('create_time', '<', Date::now()->subDay())
            ->order('create_time asc')->cursor();

        /** @var SmsSign $sign */
        foreach ($cursor as $sign) {
            try {
                $data = Sms::getSign($sign->name);

                $status = Arr::get($data, 'status');
                switch ($status) {
                    case 1:
                        $sign->pass();
                        break;
                }
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
