<?php

namespace app\task;

use app\lib\Date;
use app\model\SmsLog;
use yunwuxin\cron\Task;

class SyncSmsStatus extends Task
{
    protected function configure()
    {
        $this->onOneServer()
            ->everyFiveMinutes();
    }

    public function handle()
    {
        SmsLog::recent()
            ->where('status', 0)
            ->where('send_time', '<', Date::now()->subMinutes(10))
            ->chunk(50, function ($logs) {
                /** @var SmsLog $log */
                foreach ($logs as $log) {
                    if ($log->status == 0) {
                        $log->syncStatus();
                    }
                }
            }, 'send_time');
    }

}
