<?php

namespace app\task;

use app\model\SmsPackage;
use Exception;
use think\facade\Log;
use yunwuxin\cron\Task;

class AmendSmsPackageUsedNums extends Task
{
    protected function configure()
    {
        $this->onOneServer()
            ->dailyAt('4:00');
    }

    public function handle()
    {
        $cursor = SmsPackage::whereRaw('used_nums > total_nums')
            ->order('create_time asc')
            ->cursor();

        /** @var SmsPackage $package */
        foreach ($cursor as $package) {
            try {
                $package->amendUsedNums();
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
