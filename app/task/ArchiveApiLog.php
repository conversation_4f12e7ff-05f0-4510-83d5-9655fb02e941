<?php

namespace app\task;

use app\lib\Date;
use app\model\ApiUserPackage;
use Exception;
use think\facade\Log;
use yunwuxin\cron\Task;

class ArchiveApiLog extends Task
{
    protected function configure()
    {
        $this->onOneServer()->twiceMonthly(3, 15);
    }

    public function handle()
    {
        $startMonth = Date::now()->subMonths(3)->startOfMonth();
        $endMonth   = Date::now()->subMonths(1)->startOfMonth();

        $cursor = ApiUserPackage::where('create_time', '>', Date::parse('-2 years')->subDays(7))
            ->order('create_time asc')->cursor();

        /** @var ApiUserPackage $package */
        foreach ($cursor as $package) {
            $current = $startMonth->copy();

            while ($current->lte($endMonth)) {
                $month = $current->format('Ym');
                try {
                    $package->archive($month);
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
                $current = $current->addMonth();
            }
        }
    }
}
