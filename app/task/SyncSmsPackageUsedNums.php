<?php

namespace app\task;

use app\lib\Date;
use app\model\SmsPackage;
use Exception;
use think\facade\Log;
use yunwuxin\cron\Task;

class SyncSmsPackageUsedNums extends Task
{
    protected function configure()
    {
        $this->onOneServer()->dailyAt('3:00');
    }

    public function handle()
    {
        $cursor = SmsPackage::where('create_time', '>', Date::parse('-2 years')->subDays(7))
            ->order('create_time asc')->cursor();

        /** @var SmsPackage $package */
        foreach ($cursor as $package) {
            try {
                $package->syncUsedNums();
            } catch (Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }
}
