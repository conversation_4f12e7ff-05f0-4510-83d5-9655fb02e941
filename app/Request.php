<?php

namespace app;

// 应用请求对象类

use app\lib\oauth\HasAccessToken;

class Request extends \think\Request
{
    use HasAccessToken;

    protected $proxyServerIp       = ['127.0.0.1'];
    protected $proxyServerIpHeader = ['HTTP_X_ORIGINAL_FORWARDED_FOR', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'HTTP_X_CLIENT_IP', 'HTTP_X_CLUSTER_CLIENT_IP'];

    public function setHeader($name, $value)
    {
        $this->header[strtolower($name)] = $value;
        return $this;
    }
}
