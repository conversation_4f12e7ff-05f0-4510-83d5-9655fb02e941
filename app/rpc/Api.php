<?php

namespace app\rpc;

use app\lib\Date;
use app\lib\Producer;
use app\model\ApiMcp;
use app\model\ApiVip;
use app\notification\ApiWarn;
use think\annotation\Inject;
use think\exception\ValidateException;
use yunwuxin\throttle\RateLimiter;

class Api extends TokenService
{
    const RATE_LIMITER_KEY = 'api_rate_limiter_%u_%u';

    protected $scope  = 'api';
    protected $except = ['getMcp'];

    #[Inject]
    protected RateLimiter $limiter;

    #[Inject]
    protected Producer $producer;

    protected function getApi(string $name): ?\app\model\Api
    {
        //TODO 优化缓存
        return $this->user->apis()->cache(3600)->where('name', $name)->find();
    }

    public function check(string $name)
    {
        $api = $this->getApi($name);
        if (empty($api)) {
            throw new ValidateException('接口不存在或尚未申请');
        }

        switch (true) {
            case $api->isPay():
                //获取可用套餐
                $package = $api->pivot->getAvailablePackage();
                if ($package) {
                    $package->incUsedNums();
                    return $package->id;
                }

                throw new ValidateException('此接口您已无可用套餐');
            case $api->isVip():
                //检查是否为会员
                $vip = ApiVip::getByUser($this->user);
                if (empty($vip)) {
                    throw new ValidateException('会员接口，仅限会员使用');
                }
            case $api->isFree():
                //调用频率限制
                $key = sprintf(self::RATE_LIMITER_KEY, $api->id, $this->user->id);
                [$maxAttempts, , $message] = $api->pivot->rate;

                if ($this->limiter->tooManyAttempts($key, $maxAttempts)) {
                    //超出频率限制了
                    throw new ValidateException("您已超出该接口请求频率[{$message}]限制，请稍后重试");
                }
        }
    }

    public function report(string $name, string $path, bool $success, ?string $id)
    {
        $api = $this->getApi($name);
        if (!empty($api)) {
            $data = [
                'api_id'      => $api->id,
                'user_id'     => $this->user->id,
                'package_id'  => $id ?? 0,
                'path'        => $path,
                'success'     => $success,
                'create_time' => (string) Date::now(),
            ];

            $this->producer->send('api_log', json_encode($data));

            if ($success) {
                if ($api->isPay()) {
                    //接口预警
                    if ($api->pivot->warn_nums > 0 && $api->pivot->nums == $api->pivot->warn_nums) {
                        $this->user->notify(new ApiWarn($api->title, $api->pivot->warn_nums));
                    }
                } else {
                    $key = sprintf(self::RATE_LIMITER_KEY, $api->id, $this->user->id);
                    $this->limiter->hit($key, $api->pivot->rate[1]);
                }
            } elseif ($id) {
                //归还次数
                $api->pivot->getPackage($id)?->decUsedNums();
            }
        }
    }

    public function getMcp(string $hash)
    {
        return ApiMcp::getByHash($hash);
    }
}
