<?php

namespace app\rpc;

use app\model\AccessToken;
use app\model\User;
use think\exception\ValidateException;
use think\helper\Str;
use think\swoole\concerns\WithMiddleware;
use think\swoole\rpc\Protocol;

abstract class TokenService
{
    use WithMiddleware;

    protected $scope = null;

    protected $except = [];

    /** @var User */
    protected $user;

    public function __construct()
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();

            $token = $context['token'];
            if (Str::startsWith($token, 'user:')) {
                $id         = Str::substr($token, 5);
                $this->user = User::where('id', $id)->cache(24 * 60 * 60)->find();
                if (empty($this->user)) {
                    throw new ValidateException('用户信息不存在');
                }
            } else {
                $accessToken = AccessToken::getByToken($token, User::class, $this->scope);

                if (empty($accessToken)) {
                    throw new ValidateException('访问令牌不存在');
                }

                $this->user = $accessToken->accessible;
            }

            return $next($protocol);
        })->except($this->except);
    }
}
