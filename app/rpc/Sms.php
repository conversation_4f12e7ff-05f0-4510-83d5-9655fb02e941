<?php

namespace app\rpc;

use app\lib\Hashids;
use app\model\SmsLog;
use app\model\SmsSign;
use app\model\SmsTemplate;
use app\model\User;
use app\notification\Message;
use think\exception\ValidateException;
use think\swoole\rpc\Protocol;
use yunwuxin\facade\Notification;

class Sms extends TokenService
{

    protected $scope = 'sms';

    /** @var \app\model\Sms */
    protected $sms;

    public function __construct()
    {
        parent::__construct();

        $this->middleware(function (Protocol $protocol, $next) {
            $this->sms = \app\model\Sms::getByUser($this->user);
            return $next($protocol);
        });
    }

    public function send($signId, $templateId, string $phone, ?string $params = null)
    {
        return $this->sms->send($signId, $templateId, $phone, $params);
    }

    public function batchSend($signId, $templateId, string $phone, ?string $params = null)
    {
        return $this->sms->batchSend($signId, $templateId, $phone, $params);
    }

    public function queryStatus($id)
    {
        $log = SmsLog::where('send_id', $id)->where('user_id', $this->user->id)->find();

        if (empty($log)) {
            throw new ValidateException('短信记录不存在');
        }

        return $log->toData();
    }

    public function addSign($name, $source, $company = null, $proof = null)
    {
        $sign = SmsSign::create([
            'name'    => $name,
            'source'  => $source,
            'company' => $company,
            'proof'   => $proof,
            'user_id' => $this->user->id,
        ]);

        Notification::send(User::getAdmins(), new Message('您有一个“短信签名”审批需要处理'));

        return [
            'id' => $sign->hash_id,
        ];
    }

    public function modifySign($id, $name, $source, $company = null, $proof = null)
    {
        $sign = SmsSign::where('id', Hashids::decode($id))
            ->where('user_id', $this->user->id)
            ->where('status', -1)
            ->findOrFail();

        $sign->save([
            'name'    => $name,
            'source'  => $source,
            'company' => $company,
            'proof'   => $proof,
            'status'  => 0,
        ]);

        Notification::send(User::getAdmins(), new Message('您有一个“短信签名”审批需要处理'));
    }

    public function querySign($id)
    {
        $sign = SmsSign::where('user_id', $this->user->id)
            ->where('id', Hashids::decode($id))
            ->findOrFail();

        return $sign->toData();
    }

    public function deleteSign($id)
    {
        $sign = SmsSign::where('user_id', $this->user->id)
            ->where('id', Hashids::decode($id))
            ->findOrFail();

        if ($sign->templates()->count() > 0) {
            throw new ValidateException('该签名下存在模板，无法删除');
        }

        $sign->delete();
    }

    public function addTemplate($name, $type, $signId, $content)
    {
        $sign = SmsSign::where('user_id', $this->user->id)
            ->where('status', 1)
            ->where('id', Hashids::decode($signId))
            ->find();

        if (empty($sign)) {
            throw new ValidateException('签名不存在');
        }

        $isMarketing = SmsTemplate::isMarketing($content);

        if ($type == 1 && $isMarketing) {
            throw new ValidateException('模板内容与模板类型不匹配');
        }

        if ($type == 2 && !$isMarketing) {
            throw new ValidateException('营销类短信请在短信的末尾加上“拒收请回复R”');
        }

        //添加模板
        $outId = \app\facade\Sms::addTemplate($name, $sign->name, $content);

        $template = SmsTemplate::create([
            'name'    => $name,
            'type'    => $type,
            'content' => $content,
            'sign_id' => $sign->id,
            'user_id' => $this->user->id,
            'out_id'  => $outId,
        ]);

        return [
            'id' => $template->hash_id,
        ];
    }

    public function modifyTemplate($id, $name, $type, $signId, $content)
    {
        $template = SmsTemplate::where('user_id', $this->user->id)
            ->where('id', Hashids::decode($id))
            ->where('status', -1)
            ->findOrFail();

        $sign = SmsSign::where('user_id', $this->user->id)
            ->where('status', 1)
            ->where('id', Hashids::decode($signId))
            ->find();

        if (empty($sign)) {
            throw new ValidateException('签名不存在');
        }

        $isMarketing = SmsTemplate::isMarketing($content);

        if ($type == 1 && $isMarketing) {
            throw new ValidateException('模板内容与模板类型不匹配');
        }

        if ($type == 2 && !$isMarketing) {
            throw new ValidateException('营销类短信请在短信的末尾加上“拒收请回复R”');
        }

        //更新模板
        \app\facade\Sms::updateTemplate($template->out_id, $name, $sign->name, $content);

        $template->save([
            'name'    => $name,
            'type'    => $type,
            'content' => $content,
            'sign_id' => $sign->id,
            'status'  => 0,
        ]);
    }

    public function queryTemplate($id)
    {
        $template = SmsTemplate::where('user_id', $this->user->id)
            ->where('id', Hashids::decode($id))
            ->findOrFail();

        return $template->toData();
    }

    public function deleteTemplate($id)
    {
        $template = SmsTemplate::where('user_id', $this->user->id)
            ->where('id', Hashids::decode($id))
            ->findOrFail();

        $template->delete();
    }
}
