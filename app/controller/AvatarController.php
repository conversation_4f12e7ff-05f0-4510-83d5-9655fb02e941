<?php

namespace app\controller;

use app\BaseController;

class AvatarController extends BaseController
{
    public function index($hash)
    {
        $image = \Avram\Robohash\Robohash::make($hash, 140, 'any', 'any', 'any');
        $image->encode();

        return response($image->getEncoded())->header([
            'Cache-Control' => 'public, max-age=315360000',
            'Content-Type'  => 'image/png',
        ]);
    }
}
