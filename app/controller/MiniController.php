<?php

namespace app\controller;

use app\BaseController;
use app\lib\MiniAuth;

class MiniController extends BaseController
{
    public function qrcode(MiniAuth $mini)
    {
        $result = $mini->create();

        return json($result);
    }

    public function register(MiniAuth $mini)
    {
        $data = $this->validate([
            'token' => 'require',
            'code'  => 'require',
            'id'    => 'require',
        ]);

        $mini->register($data['token'], $data['id'], $data['code']);
    }

    public function login(MiniAuth $mini)
    {
        $data = $this->validate([
            'token' => 'require',
            'user'  => 'require',
        ]);

        $mini->login($data['token'], $data['user']);
    }

    public function check(MiniAuth $mini)
    {
        $data = $this->validate([
            'token' => 'require',
            'code'  => 'require',
        ]);

        $result = $mini->check($data['token'], $data['code']);

        return json($result);
    }

    //以下为passport使用
    public function metadata(MiniAuth $mini, $token)
    {
        return json($mini->getData($token));
    }

    public function authorize(MiniAuth $mini)
    {
        $data = $this->validate([
            'token' => 'require',
            'code'  => 'require',
        ]);

        $mini->authorize($data['token'], $data['code']);
    }

}
