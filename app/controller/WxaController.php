<?php

namespace app\controller;

use app\BaseController;
use app\lib\wechat\Message;
use app\lib\wechat\wxa\Application;
use Closure;
use think\Event;

class WxaController extends BaseController
{
    public function component(Application $application, Event $event)
    {
        $server = $application->getServer();

        $server->with(function (Message $message, Closure $next) use ($event, $application) {
            //trace("component:" . $message->toJson());

            $result = $event->trigger('wxa.' . $message->InfoType, $message, true);

            if ($result) {
                return $result;
            }

            return $next($message);
        });

        return $server->serve($this->request);
    }

    public function biz(Application $application, Event $event, $appid)
    {
        $server = $application->getServer();

        $server->with(function (Message $message, Closure $next) use ($appid, $event) {
            //trace($appid . ":" . $message->toJson());
            $message->Appid = $appid;

            switch ($message->MsgType) {
                case 'event':
                    //事件消息
                    $result = $event->trigger('wxa.biz.' . strtolower($message->Event), $message, true);
                    if ($result) {
                        return $result;
                    }
                    break;
                case 'text':
                    //文本消息
                    //trace($appid . ":" . $message->toJson());
                    $result = $event->trigger('wxa.biz.text', $message, true);

                    if ($result) {
                        return $result;
                    }
            }

            return $next($message);
        });

        return $server->serve($this->request);
    }
}
