<?php

namespace app\controller;

use think\Request;
use yunwuxin\Social;

class SocialController
{
    public function redirectToSocial(Social $social, $channel, $mode = 'redirect')
    {
        $redirectUrl = url('SOCIAL_CALLBACK', ['channel' => $channel, 'mode' => $mode])->domain(true);

        $channel = $social->channel($channel)->setRedirectUrl($redirectUrl);

        return $channel->redirect();
    }

    public function handleSocialCallback(Request $request, $channel, $mode = 'redirect')
    {
        if ($mode == 'redirect') {
            return redirect(url("/login/social/{$channel}", [
                'code'  => $request->param('code'),
                'state' => $request->param('state'),
            ]));
        } else {
            $message = json_encode([
                'source'  => 'social',
                'payload' => [
                    'channel' => $channel,
                    'code'    => $request->param('code'),
                    'state'   => $request->param('state'),
                ],
            ]);

            return "<script>window.opener.postMessage({$message},'*');window.close();</script>";
        }
    }

}
