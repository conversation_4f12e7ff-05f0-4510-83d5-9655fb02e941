<?php

namespace app\controller;

use app\BaseController;
use app\lib\MiniAuth;
use app\lib\Otp;
use app\lib\WithLoginCaptcha;
use app\model\SocialToken;
use app\model\User;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use <PERSON><PERSON><PERSON>\Agent\Agent;
use think\Session;
use yunwuxin\Auth;
use yunwuxin\Social;

class AuthController extends BaseController
{
    use WithLoginCaptcha;

    public function index(Session $session)
    {
        $force = $session->get('force', false);

        if ($this->user && !$force) {
            return $this->logined();
        }

        $agent = new Agent(userAgent: $this->request->header('user-agent'));

        return view('auth/login')->assign([
            'state' => $agent->isMobile() && !$agent->match('MicroMessenger') ? 'mobile' : 'wechat',
        ]);
    }

    public function login(Auth $auth, Otp $otp)
    {
        $data = $this->validate([
            'mobile|手机号' => 'require|mobile',
            'code|验证码'   => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['mobile'], $code, 'login-code')) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
            'social'        => '',
        ]);

        $this->user = User::retrieveByMobile($data['mobile'], $this->request->ip());

        //绑定第三方
        if (!empty($data['social'])) {
            try {
                $social = (array) JWT::decode($data['social'], new Key(config('app.token'), 'HS256'));

                /** @var \yunwuxin\social\User */
                $socialUser = unserialize($social['user']);

                $this->user->socialTokens()->save([
                    'channel' => $socialUser->getChannel(),
                    'openid'  => $socialUser->getId(),
                ]);
            } catch (Exception) {
            }
        }

        $auth->login($this->user, true);

        return $this->logined();
    }

    protected function logined()
    {
        return redirect('/')->with('logined', true)->restore();
    }

    public function mini(Auth $auth, MiniAuth $mini, $token)
    {
        $user = $mini->user($token);

        if (empty($user)) {
            abort(449);
        }

        $auth->login($user, true);

        return $this->logined();
    }

    public function logout(Auth $auth)
    {
        $this->user->resetToken();

        $auth->logout();

        return redirect('/login');
    }

    public function social(Social $social, Auth $auth, $channel)
    {
        $socialUser = $social->channel($channel)->user();

        $token = SocialToken::getBySocialUser($socialUser);

        if ($token) {
            $auth->login($token->user, true);
            return $this->logined();
        }

        $social = JWT::encode([
            'exp'  => time() + 1800,
            'iat'  => time(),
            'user' => serialize($socialUser),
        ], config('app.token'), 'HS256');

        return redirect('/login/complete')->with('social', $social);
    }

    public function complete()
    {
        $social = session('social');
        if (empty($social)) {
            return redirect('/login');
        }
        return view('auth/complete')->assign('social', $social);
    }
}
