<?php

namespace app\controller;

use app\BaseController;
use app\lib\Agent;
use app\lib\Hashids;
use app\lib\wechat\account\Application;
use app\model\Charge;
use app\model\Promo;
use think\exception\ValidateException;
use think\facade\Session;
use yunwuxin\auth\exception\AuthorizationException;

class CashierController extends BaseController
{
    /** @var Charge */
    protected $charge;

    public function initialize()
    {
        $id = $this->request->param('id');
        if (!is_numeric($id)) {
            $id = Hashids::decode($id);
        }
        $this->charge = Charge::findOrFail($id);

        if ($this->charge->user_id && (empty($this->user) || $this->user->id != $this->charge->user_id)) {
            throw new AuthorizationException();
        }
    }

    protected function checkPaid($status = null)
    {
        if ($this->charge->status == 1) {
            $url = $this->charge->return_url ?: ($status != 1 ? $this->charge->pay_url : null);
        } elseif ($this->charge->status == 2 && $status != 2) {
            $url = $this->charge->pay_url;
        }

        if (!empty($url) && $url != $this->request->url(true)) {
            abort(redirect($url));
        }
    }

    public function index(Agent $agent, Application $application)
    {
        //自动检查是否已支付
        $this->charge->check();

        $this->checkPaid(1);

        if (!$this->charge->user_id && $this->user) {
            $this->charge->save(['user_id' => $this->user->id]);
        }

        //微信中，获取用户openid
        if ($agent->isWeChat() && !Session::has('wechat_openid')) {
            $oauth = $application->getOAuth()->scopes(['snsapi_base'])
                ->setRedirectUrl($this->request->url(true));
            if ($this->request->has('code')) {
                $user = $oauth->user();
                Session::set('wechat_openid', $user->getId());
            } else {
                return $oauth->redirect();
            }
        }

        $channels = ['alipay', 'wechat', 'transfer'];

        if ($agent->isWeChat()) {
            $channels = array_diff($channels, ['alipay', 'transfer']);
        } elseif ($agent->isMobile()) {
            $channels = array_diff($channels, ['transfer']);
        }

        return view("cashier")->assign('channels', $channels)->assign('charge', $this->charge);
    }

    public function save(Agent $agent)
    {
        $this->checkPaid();

        $data = $this->validate([
            'channel|支付方式' => 'require',
            'promo|优惠码'     => '',
            'coin|云币'        => 'integer|egt:0',
            'money|余额'       => 'integer|egt:0',
        ]);

        if (empty($this->charge->channel)) {
            if (!empty($data['promo'])) {
                $promo = Promo::where('code', $data['promo'])->find();
                if (!$promo) {
                    throw new ValidateException('优惠码不存在');
                }
                $this->charge->promo_id = $promo->id;
                $this->charge->discount = $promo->getDiscount($this->charge);
            }

            if (!empty($data['coin'])) {
                if ($this->user->coin < $data['coin']) {
                    throw new ValidateException('云币不足');
                }
                $this->charge->coin = $data['coin'];
            }

            if (!empty($data['money'])) {
                if ($this->user->money < $data['money']) {
                    throw new ValidateException('余额不足');
                }
                $this->charge->money = $data['money'];
            }
        }

        $channel = $data['channel'];
        $gateway = $agent->isMobile() ? 'wap' : 'scan';

        if ($agent->isWeChat()) {
            $channel = 'wechat';
            $gateway = 'js';

            $this->charge->extra = [
                'openid' => Session::get('wechat_openid'),
            ];
        }

        return $this->charge->pay($channel, $gateway);
    }

    public function paid()
    {
        $channel = $this->request->param('channel');
        //检查支付信息
        $this->charge->check($channel);
    }

    public function check()
    {
        $status = $this->request->param('status');
        $this->checkPaid($status);

        abort(449);
    }

    public function transfer()
    {
        $this->checkPaid();

        $response = $this->charge->pay('transfer');

        $this->charge->save([
            'channel' => 'transfer',
            'raw'     => $response->getData(),
        ]);

        return redirect($this->charge->pay_url);
    }

    public function promo()
    {
        $data = $this->validate([
            'code|优惠码' => 'require',
        ]);

        $promo = Promo::where('code', $data['code'])->find();

        if (!$promo) {
            throw new ValidateException([
                'code' => '优惠码不存在',
            ]);
        }

        return [
            'title'    => $promo->title,
            'code'     => $promo->code,
            'discount' => $promo->getDiscount($this->charge),
        ];
    }
}
