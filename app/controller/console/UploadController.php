<?php

namespace app\controller\console;

use think\Filesystem;

class UploadController extends Controller
{
    public function save(Filesystem $filesystem)
    {
        $data = $this->validate([
            'dir'  => 'require|in:enterprise,invoice,assistant,api,ai,sms',
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile($data['dir'], $data['file']);

        $result = [
            'url' => (string) main_url($disk->url($path)),
        ];

        if (in_array($data['dir'], ['ai', 'sms'])) {
            $result['value'] = $path;
        }

        return json($result);
    }
}
