<?php

namespace app\controller\console\partner;

use app\controller\console\Controller;
use app\model\Partner;
use think\annotation\route\Get;
use think\annotation\route\Post;

class IndexController extends Controller
{
    #[Get('partner')]
    public function index()
    {
        return Partner::where('user_id', $this->user->id)->append(['fee'])->hidden(['license'])->findOrFail();
    }

    #[Post('partner')]
    public function save()
    {
        if (!$this->user->is_certified) {
            abort(422, '请先完成企业认证');
        }
        Partner::create([
            'user_id' => $this->user->id,
        ]);
    }
}
