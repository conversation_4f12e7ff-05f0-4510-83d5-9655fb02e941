<?php

namespace app\controller\console\partner;

use app\controller\console\Controller;
use app\enum\Product;
use app\lib\Date;
use app\lib\Hashids;
use app\model\CommissionLog;
use think\annotation\route\Get;

class PromoController extends Controller
{
    use WithPartner;

    #[Get('partner/promo')]
    public function index()
    {
        return $this->partner->promos()->order('id desc')->paginate();
    }

    #[Get('partner/promo/stats')]
    public function stats()
    {
        $key = Hashids::encode($this->partner->id);

        $urls = [
            '首页'     => (string) main_url('/', ['cps_key' => $key]),
            '知识管理' => (string) main_url('/product/knowledge', ['cps_key' => $key]),
            '运营助理' => (string) main_url('/product/assistant', ['cps_key' => $key]),
            'ThinkAI'  => (string) main_url('/product/ai', ['cps_key' => $key]),
            'ThinkBot' => (string) main_url('/product/bot', ['cps_key' => $key]),
            'ThinkSMS' => (string) main_url('/product/sms', ['cps_key' => $key]),
            'ThinkSSL' => (string) main_url('/product/ssl', ['cps_key' => $key]),
            'ThinkAPI' => (string) main_url('/product/api', ['cps_key' => $key]),
        ];

        $user     = $this->partner->customers()->wherePivot('create_time', '>', Date::now()->subMonth())->count();
        $products = array_map(fn($item) => ['value' => $item->value, 'label' => $item->label()], Product::cases());

        $commissions = $this->partner->commissionLogs()->where('type', CommissionLog::TYPE_INC)->select();

        return [
            'urls'       => $urls,
            'user'       => $user,
            'products'   => $products,
            'charge'     => $commissions->count(),
            'amount'     => array_sum($commissions->map(fn(CommissionLog $log) => $log->source->net_amount)->toArray()),
            'commission' => array_sum($commissions->map(fn(CommissionLog $log) => $log->number)->toArray()),
        ];
    }

    #[Get('partner/promo/customer')]
    public function customer()
    {
        return $this->partner->customers()
            ->order('pivot.create_time desc')
            ->wherePivot('create_time', '>', Date::now()->subDays(90))
            ->paginate();
    }
}
