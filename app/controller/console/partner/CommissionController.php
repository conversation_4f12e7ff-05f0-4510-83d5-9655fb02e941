<?php

namespace app\controller\console\partner;

use app\controller\console\Controller;
use think\annotation\route\Get;

class CommissionController extends Controller
{
    use WithPartner;

    #[Get('partner/commission')]
    public function index()
    {
        return json([
            'total' => $this->partner->commission,
        ]);
    }

    #[Get('partner/commission/logs')]
    public function logs()
    {
        return $this->partner->commissionLogs()->order('id desc')->paginate(10);
    }
}
