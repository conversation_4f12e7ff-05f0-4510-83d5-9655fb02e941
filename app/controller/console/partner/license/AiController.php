<?php

namespace app\controller\console\partner\license;

use app\controller\console\Controller;
use app\controller\console\partner\WithPartner;
use app\enum\LicensePlan;
use app\lib\Date;
use app\model\LicenseAi;
use app\Request;
use Ramsey\Uuid\Uuid;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class AiController extends Controller
{
    use WithPartner;

    /** @var LicenseAi */
    protected $license;

    public function initialized()
    {
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('id');

            if (empty($id)) {
                abort(404);
            }

            $this->license = LicenseAi::where('partner_id', $this->partner->id)->findOrFail($id);

            return $next($request);
        })->only(['update', 'token', 'renew', 'buy']);
    }

    #[Get('partner/license/ai')]
    public function index()
    {
        return LicenseAi::where('partner_id', $this->partner->id)->order('plan desc')->order('id desc')->paginate();
    }

    #[Post('partner/license/ai')]
    public function save()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
        ]);

        $data['partner_id']  = $this->partner->id;
        $data['plan']        = LicensePlan::Trial;
        $data['expire_time'] = Date::now()->addDays(15);

        LicenseAi::create($data);
    }

    #[Put('partner/license/ai/:id')]
    public function update()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
        ]);

        $this->license->save($data);
    }

    #[Get('partner/license/ai/:id/token')]
    public function token()
    {
        $accessToken = $this->license->access_token;

        if (empty($accessToken)) {
            $accessToken = $this->license->accessToken()->save([
                'name'  => '授权令牌',
                'token' => Uuid::uuid4()->toString(),
            ]);
        }

        return ['token' => $accessToken->token];
    }

    #[Post('partner/license/ai/:id/renew')]
    public function renew()
    {
        if ($this->license->plan != LicensePlan::Trial) {
            abort(400);
        }

        $this->license->transaction(function () {
            $this->license->save([
                'expire_time' => Date::now()->addDays(15),
            ]);

            $this->license->access_token?->save([
                'token' => Uuid::uuid4()->toString(),
            ]);
        });
    }

    #[Post('partner/license/ai/:id/buy')]
    public function buy()
    {
        if (empty($this->partner->fee['ai'])) {
            abort(400);
        }

        $data = $this->validate([
            'amount' => 'float',
        ]);

        $goods = new \app\lib\goods\LicenseAi($this->license, $this->partner->fee['ai'], $data['amount'] ?? null);

        if (empty($data['amount'])) {
            $goods->setUser($this->user);
        }

        $order = $goods->purchase();

        return $order->pay();
    }
}
