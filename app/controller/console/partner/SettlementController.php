<?php

namespace app\controller\console\partner;

use app\controller\console\Controller;
use app\model\CommissionLog;
use app\model\User;
use app\notification\Message;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\Db;
use yunwuxin\facade\Notification;

class SettlementController extends Controller
{
    use WithPartner;

    #[Get('partner/settlement')]
    public function index()
    {
        return $this->partner->settlements()->order('id desc')->paginate();
    }

    #[Get('partner/settlement/create')]
    public function create()
    {
        return [
            'total' => $this->partner->settle_commission,
        ];
    }

    #[Post('partner/settlement')]
    public function save(Db $db)
    {
        $data = $this->validate([
            'type|结算方式'   => 'require|in:1,2',
            'amount|结算金额' => 'require|float|egt:0|elt:' . $this->partner->settle_commission / 100,
            'invoice|发票'    => 'requireIf:type,2',
        ]);

        $db->transaction(function () use ($data) {
            $amount = (int) ($data['amount'] * 100);

            $settlement = $this->partner->settlements()->save([
                'amount'  => $amount,
                'type'    => $data['type'],
                'invoice' => $data['invoice'] ?? null,
                'status'  => 0,
            ]);

            $this->partner->updateCommission(CommissionLog::TYPE_DEC, $amount, "结算#{$settlement->id}", $settlement);

            if ($data['type'] == 1) {
                $settlement->confirm();
            } else {
                Notification::send(User::getAdmins(), new Message('您有一个“佣金结算”审批需要处理'));
            }
        });
    }
}
