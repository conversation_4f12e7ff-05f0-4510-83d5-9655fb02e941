<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\model\Certification;
use think\exception\ValidateException;
use yunwuxin\throttle\RateLimiter;

class CertificationController extends Controller
{
    public function index()
    {
        return json($this->user->certification);
    }

    public function save(RateLimiter $limiter)
    {
        $data = $this->validate([
            'name|真实姓名'     => 'require',
            'identity|身份证号' => [
                'require',
                '(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)',
            ],
        ]);

        if ($this->user->certification) {
            throw new ValidateException('已存在认证信息,请勿重复认证');
        }

        $key = "certification{$this->user->id}";

        if ($limiter->tooManyAttempts($key, 3)) {
            throw new ValidateException('24小时内只能提交3次认证申请！');
        }

        if (Certification::where('identity', $data['identity'])->where('passed', 1)->count() >= 3) {
            throw new ValidateException('每个身份证最多绑定到3个账号');
        }

        $this->user->certification()->save($data);

        $limiter->hit($key, 60 * 24 * 60);
    }

    public function delete()
    {
        if ($this->user->certification && $this->user->certification->passed == 0) {
            $this->user->certification->delete();
        }
    }

    public function check()
    {
        if ($this->user->certification && $this->user->certification->check() == 0) {
            abort(449);
        }
    }
}
