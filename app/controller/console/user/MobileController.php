<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\lib\Otp;
use app\model\User;
use app\notification\MobileCode;
use think\exception\ValidateException;
use yunwuxin\throttle\RateLimiter;

class MobileController extends Controller
{
    public function save(Otp $otp)
    {
        $data = $this->validate([
            'mobile|手机' => 'require|mobile|unique:' . User::class,
            'code|验证码' => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['mobile'], $code, 'mobile-code')) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
        ]);

        $this->user->save([
            'mobile' => $data['mobile'],
        ]);

        return json($this->user);
    }

    public function code(Otp $otp, RateLimiter $limiter)
    {
        $data = $this->validate([
            'mobile|手机' => 'require|mobile|unique:' . User::class,
        ]);

        $key = "mobile{$this->user->id}";

        if ($limiter->tooManyAttempts($key, 1)) {
            throw new ValidateException('1分钟内只能发送一次，请稍后再试');
        }

        $code = $otp->create($data['mobile'], 'mobile-code');

        $this->user->notify(new MobileCode($data['mobile'], $code));

        $limiter->hit($key);

        return response('验证码已发送，请注意查收。', 202);
    }
}
