<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\model\SocialToken;
use think\exception\ValidateException;
use yunwuxin\Social;

class SocialController extends Controller
{
    public function index()
    {
        return $this->user->socialTokens()->select();
    }

    public function read($channel)
    {
        return ['url' => (string) main_url('SOCIAL', ['channel' => $channel, 'mode' => 'popup'])];
    }

    public function delete($channel)
    {
        $this->user->socialTokens()->where('channel', $channel)->delete();
    }

    public function save(Social $social)
    {
        $channel = $this->request->param('channel');

        $socialUser = $social
            ->channel($channel)
            ->setRedirectUrl(main_url('SOCIAL_CALLBACK', ['channel' => $channel]))
            ->user();

        $token = SocialToken::getBySocialUser($socialUser);

        if ($token) {
            throw new ValidateException("该{$socialUser->getChannel()}账号已绑定过其他账号");
        }

        $this->user->socialTokens()->save([
            'channel' => $socialUser->getChannel(),
            'openid'  => $socialUser->getId(),
        ]);
    }
}
