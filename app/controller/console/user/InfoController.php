<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use think\Filesystem;

class InfoController extends Controller
{
    public function name()
    {
        $data = $this->validate([
            'name|昵称' => 'require',
        ]);

        $this->user->save($data);

        return json($this->user);
    }

    public function avatar(Filesystem $filesystem)
    {
        $data = $this->validate([
            'avatar' => 'require|image',
        ]);

        $data['avatar'] = $filesystem->disk('uploads')->putFile('avatar', $data['avatar']);

        $this->user->save($data);

        return json($this->user);
    }
}
