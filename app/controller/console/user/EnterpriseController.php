<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\model\User;
use app\notification\Message;
use think\api\Client;
use think\exception\ValidateException;
use think\helper\Arr;
use Throwable;
use yunwuxin\facade\Notification;

class EnterpriseController extends Controller
{
    public function index()
    {
        if (!$this->user->isCertified()) {
            throw new ValidateException('请先完成实名认证');
        }
        return json($this->user->enterprise);
    }

    public function save(Client $client)
    {
        if (!$this->user->isCertified()) {
            throw new ValidateException('请先完成实名认证');
        }
        if ($this->user->isEnterprise()) {
            throw new ValidateException('已通过认证，请勿重复提交');
        }

        $data = $this->validate([
            'corporation|企业法人'             => 'require',
            'name|公司名称'                    => 'require',
            'identity|注册号/统一社会信用代码' => 'require',
            'img|营业执照'                     => 'require',
        ]);

        $data['status'] = 0;

        //自动检查
        if ($this->user->certification->name == $data['corporation']) {
            try {
                $result = $client->enterpriseVerify()
                    ->withName($data['name'])
                    ->withOperName($data['corporation'])
                    ->withKeyword($data['identity'])
                    ->request();

                if (Arr::get($result, 'data.status') == 1) {
                    $data['status'] = 1;
                }
            } catch (Throwable) {
            }
        }

        if ($this->user->enterprise) {
            $this->user->enterprise->save($data);
        } else {
            $this->user->enterprise()->save($data);
        }

        if ($data['status'] != 1) {
            Notification::send(User::getAdmins(), new Message('您有一个“企业认证”审批需要处理'));
        }
    }
}
