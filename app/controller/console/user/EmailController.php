<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\lib\Otp;
use app\model\User;
use app\notification\EmailCode;
use think\exception\ValidateException;
use yunwuxin\throttle\RateLimiter;

class EmailController extends Controller
{
    public function save(Otp $otp)
    {
        $data = $this->validate([
            'email|邮箱'  => 'require|email|unique:' . User::class,
            'code|验证码' => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['email'], $code, 'email-code')) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
        ]);

        $this->user->save([
            'email' => $data['email'],
        ]);

        return json($this->user);
    }

    public function code(Otp $otp, RateLimiter $limiter)
    {
        $data = $this->validate([
            'email|邮箱' => 'require|email|unique:' . User::class,
        ]);

        $key = "email{$this->user->id}";

        if ($limiter->tooManyAttempts($key, 1)) {
            throw new ValidateException('1分钟内只能发送一次，请稍后再试');
        }

        $code = $otp->create($data['email'], 'email-code');

        $this->user->notify(new EmailCode($data['email'], $code));

        $limiter->hit($key);

        return response('验证码已发送，请注意查收。', 202);
    }
}
