<?php

namespace app\controller\console\user;

use app\controller\console\Controller;
use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\annotation\route\Resource;
use think\db\Query;

#[Resource('user/token')]
class TokenController extends Controller
{

    public function index()
    {
        return $this->user->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->order('id desc')->select();
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称'            => 'require',
            'scopes|授权范围'      => 'require',
            'expire_time|到期时间' => 'datetime',
        ]);

        return $this->user->accessTokens()->save([
            'name'        => $data['name'],
            'scopes'      => $data['scopes'],
            'token'       => Uuid::uuid4()->toString(),
            'expire_time' => $data['expire_time'] ?? null,
        ])->hidden([]);
    }

    public function delete($id)
    {
        $weaccount = $this->user->accessTokens()->findOrFail($id);
        $weaccount->delete();
    }
}
