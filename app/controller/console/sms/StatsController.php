<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\SmsLog;
use think\annotation\route\Get;
use think\db\Raw;

class StatsController extends Controller
{
    #[Get('sms/stats')]
    public function index($period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_ck_date_query('send_time', $unit);

        $data = SmsLog::where('user_id', $this->user->id)
            ->field(
                new Raw(<<<EOT
{$dateField} x,
sum(if(status>0,1,0)) success,
sum(if(status<0,1,0)) fail,
sum(if(status>0,nums,0)) nums
EOT
                )
            )
            ->where('status', '<>', 0)
            ->whereBetween('send_time', [$start, $end])
            ->group('x')
            ->select();

        return [
            'success' => fill_data($list, $data, 'x', 'success', 'y'),
            'fail'    => fill_data($list, $data, 'x', 'fail', 'y'),
            'nums'    => fill_data($list, $data, 'x', 'nums', 'y'),
        ];
    }

    #[Get('sms/stats/overview')]
    public function overview($period = '24hours')
    {
        [$start, $end] = get_period($period);

        return SmsLog::where('user_id', $this->user->id)
            ->field(
                new Raw(<<<EOT
sum(if(status>0,1,0)) success,
sum(if(status<0,1,0)) fail,
count(*) total,
sum(if(status>0,nums,0)) nums
EOT
                )
            )
            ->whereBetween('send_time', [$start, $end])
            ->find();
    }
}
