<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\SmsReply;
use think\annotation\route\Get;

class ReplyController extends Controller
{
    #[Get('sms/reply')]
    public function index()
    {
        $query = SmsReply::where('user_id', $this->user->id)->order('reply_time', 'desc');
        $this->searchField($query, 'mobile');
        return $query->paginate();
    }
}
