<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\SmsLog;
use think\annotation\route\Get;

class LogController extends Controller
{
    #[Get('sms/log')]
    public function index($period = '24hours')
    {
        [$start, $end] = get_period($period);

        $query = SmsLog::where('user_id', $this->user->id)
            ->whereBetween('send_time', [$start, $end])
            ->order('send_time desc')
            ->order('send_id asc');

        $this->filterFields($query, [
            'phone'  => true,
            'content',
            'status' => true,
        ]);

        return $query->paginate();
    }
}
