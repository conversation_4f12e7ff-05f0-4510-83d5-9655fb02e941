<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\SmsSign;
use app\model\User;
use app\notification\Message;
use think\annotation\route\Resource;
use yunwuxin\facade\Notification;

#[Resource('sms/sign')]
class SignController extends Controller
{
    public function index()
    {
        return SmsSign::where('user_id', $this->user->id)->order('id desc')->paginate();
    }

    public function read($id)
    {
        return SmsSign::where('user_id', $this->user->id)->findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'name|签名名称'   => 'require|length:2,16',
            'source|签名来源' => 'require',
            'company'         => function ($value) {
                if (isset($value)) {
                    if (empty($value['name'])) {
                        return '企事业单位名称不能为空';
                    }
                    if (empty($value['identity'])) {
                        return '统一社会信用代码不能为空';
                    }
                    if (empty($value['corporation'])) {
                        return '法人姓名不能为空';
                    }
                    if (empty($value['img'])) {
                        return '营业执照不能为空';
                    }
                }
                return true;
            },
            'proof'           => function ($value, $data) {
                switch ($data['source']) {
                    case '已注册商标名':
                        if (empty($value)) {
                            return '商标证书不能为空';
                        }
                        break;
                    case '已备案APP':
                        if (empty($value)) {
                            return '备案证明不能为空';
                        }
                        break;
                }
                return true;
            },
        ]);

        $data['user_id'] = $this->user->id;

        SmsSign::create($data);

        Notification::send(User::getAdmins(), new Message('您有一个“短信签名”审批需要处理'));
    }

    public function update($id)
    {
        $sign = SmsSign::where('user_id', $this->user->id)->where('status', -1)->findOrFail($id);

        $data = $this->validate([
            'name|签名名称'   => 'require|length:2,16',
            'source|签名来源' => 'require',
            'company'         => function ($value) {
                if (isset($value)) {
                    if (empty($value['name'])) {
                        return '企事业单位名称不能为空';
                    }
                    if (empty($value['identity'])) {
                        return '统一社会信用代码不能为空';
                    }
                    if (empty($value['corporation'])) {
                        return '法人姓名不能为空';
                    }
                    if (empty($value['img'])) {
                        return '营业执照不能为空';
                    }
                }
                return true;
            },
            'proof'           => function ($value, $data) {
                switch ($data['source']) {
                    case '已注册商标名':
                        if (empty($value)) {
                            return '商标证书不能为空';
                        }
                        break;
                    case '已备案APP':
                        if (empty($value)) {
                            return '备案证明不能为空';
                        }
                        break;
                }
                return true;
            },
        ]);

        $data['status'] = 0;

        $sign->save($data);

        Notification::send(User::getAdmins(), new Message('您有一个“短信签名”审批需要处理'));
    }

    public function delete($id)
    {
        $sign = SmsSign::where('user_id', $this->user->id)->where('id', $id)->findOrFail();

        if ($sign->templates()->count() > 0) {
            abort(422, '该签名下存在模板，无法删除');
        }

        $sign->delete();
    }

}
