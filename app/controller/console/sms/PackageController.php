<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\Sms;
use app\model\SmsPackage;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\exception\ValidateException;

class PackageController extends Controller
{
    #[Get('sms/package')]
    public function index()
    {
        return SmsPackage::where('user_id', $this->user->id)
            ->order('create_time desc')
            ->select();
    }

    #[Post('sms/package/buy')]
    public function buy()
    {
        throw new ValidateException('短信服务已关闭');

        $data = $this->validate([
            'nums' => 'require',
            'type' => 'require|in:1,2',
        ]);

        $goods = new \app\lib\goods\SmsPackage($data['nums'], $data['type']);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

    #[Get('sms/package/buyable')]
    public function buyable()
    {
        return \app\lib\goods\SmsPackage::PACKAGES;
    }

    #[Get('sms/package/balance')]
    public function balance()
    {
        $sms = Sms::getByUser($this->user);

        return [$sms->getNums(1), $sms->getNums(2)];
    }
}
