<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\facade\Sms;
use app\model\SmsSign;
use app\model\SmsTemplate;
use think\annotation\route\Get;
use think\annotation\route\Resource;

#[Resource('sms/template')]
class TemplateController extends Controller
{
    public function index()
    {
        return SmsTemplate::with(['sign'])->where('user_id', $this->user->id)->order('user_id desc')->order('id desc')
            ->paginate();
    }

    public function read($id)
    {
        return SmsTemplate::where('user_id', $this->user->id)->findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'sign_id|关联签名' => 'require',
            'name|模板名称'    => 'require',
            'type|模板类型'    => 'require|in:1,2',
            'content|模板内容' => [
                'require',
                function ($value, $data) {
                    $isMarketing = SmsTemplate::isMarketing($value);

                    if ($data['type'] == 1 && $isMarketing) {
                        return '模板内容与模板类型不匹配';
                    }

                    if ($data['type'] == 2 && !$isMarketing) {
                        return '营销类短信请在短信的末尾加上“拒收请回复R”';
                    }

                    return true;
                },
            ],
        ]);

        $sign = SmsSign::where('user_id', $this->user->id)->where('status', 1)->find($data['sign_id']);

        if (empty($sign)) {
            abort(422, '签名不存在');
        }

        //添加模板
        $outId = Sms::addTemplate($data['name'], $sign->name, $data['content']);

        SmsTemplate::create([
            'name'    => $data['name'],
            'type'    => $data['type'],
            'content' => $data['content'],
            'sign_id' => $data['sign_id'],
            'user_id' => $this->user->id,
            'out_id'  => $outId,
        ]);
    }

    public function update($id)
    {
        $template = SmsTemplate::where('user_id', $this->user->id)->where('status', -1)->findOrFail($id);

        $data = $this->validate([
            'sign_id|关联签名' => 'require',
            'type|模板类型'    => 'require|in:1,2',
            'name|模板名称'    => 'require',
            'content|模板内容' => [
                'require',
                function ($value, $data) {
                    $isMarketing = SmsTemplate::isMarketing($value);

                    if ($data['type'] == 1 && $isMarketing) {
                        return '模板内容与模板类型不匹配';
                    }

                    if ($data['type'] == 2 && !$isMarketing) {
                        return '营销类短信请在短信的末尾加上“拒收请回复R”';
                    }

                    return true;
                },
            ],
        ]);

        $sign = SmsSign::where('user_id', $this->user->id)->where('status', 1)->find($data['sign_id']);

        if (empty($sign)) {
            abort(422, '签名不存在');
        }

        //更新模板
        Sms::updateTemplate($template->out_id, $data['name'], $sign->name, $data['content']);

        $template->save([
            'name'    => $data['name'],
            'type'    => $data['type'],
            'content' => $data['content'],
            'sign_id' => $data['sign_id'],
            'status'  => 0,
        ]);
    }

    public function delete($id)
    {
        $template = SmsTemplate::where('user_id', $this->user->id)->findOrFail($id);

        $template->delete();
    }

    #[Get('sms/template/sign')]
    public function sign()
    {
        $query = SmsSign::where('user_id', $this->user->id)->where('status', 1)->order('id desc');

        if ($this->request->has('value')) {
            $query->where('id', $this->request->get('value'));
        }

        return $query->select();
    }
}
