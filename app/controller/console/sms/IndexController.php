<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\model\Sms;
use app\model\SmsPackage;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class IndexController extends Controller
{
    #[Get('sms')]
    public function index()
    {
        return Sms::where('user_id', $this->user->id)->findOrFail();
    }

    #[Post('sms')]
    public function save()
    {
        if (!$this->user->is_enterprise) {
            abort(422, '请先完成企业认证');
        }

        $this->user->transaction(function () {
            SmsPackage::create([
                'name'       => '开通服务赠送套餐',
                'user_id'    => $this->user->id,
                'total_nums' => 10,
                'status'     => 2,
            ]);
            Sms::create([
                'user_id' => $this->user->id,
            ]);
        });
    }

    #[Put('sms')]
    public function update()
    {
        $sms = Sms::where('user_id', $this->user->id)->findOrFail();

        $data = $this->validate([
            'warn_nums'    => 'integer',
            'minute_limit' => 'integer',
            'hour_limit'   => 'integer',
            'day_limit'    => 'integer',
            'webhook'      => 'url',
        ]);

        $sms->save($data);
    }
}
