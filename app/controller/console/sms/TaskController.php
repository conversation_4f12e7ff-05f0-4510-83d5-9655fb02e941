<?php

namespace app\controller\console\sms;

use app\controller\console\Controller;
use app\job\SmsBatchSendJob;
use app\lib\Date;
use app\model\SmsLog;
use app\model\SmsTask;
use app\model\SmsTemplate;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\db\Raw;
use think\exception\ValidateException;

class TaskController extends Controller
{
    #[Get('sms/task')]
    public function index($period = '3days')
    {
        [$start, $end] = get_period($period);

        return SmsTask::where('user_id', $this->user->id)
            ->whereBetween('create_time', [$start, $end])
            ->withoutField('data')->order('id desc')->paginate();
    }

    #[Get('sms/task/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        return SmsTask::where('user_id', $this->user->id)->withoutField('data')->with(['template' => ['sign']])->where('id', $id)->findOrFail();
    }

    #[Get('sms/task/:id/overview')]
    #[Pattern('id', '\d+')]
    public function overview($id)
    {
        $task = SmsTask::where('user_id', $this->user->id)->where('id', $id)->findOrFail();

        return SmsLog::where('user_id', $this->user->id)
            ->where('task_id', $task->id)
            ->field(
                new Raw(<<<EOT
sum(if(status>0,1,0)) success,
sum(if(status<0,1,0)) fail,
count(*) total,
sum(if(status>0,nums,0)) nums
EOT
                )
            )
            ->find();
    }

    #[Get('sms/task/:id/log')]
    #[Pattern('id', '\d+')]
    public function log($id)
    {
        $task = SmsTask::where('user_id', $this->user->id)->where('id', $id)->findOrFail();

        $query = SmsLog::where('user_id', $this->user->id)
            ->where('task_id', $task->id)
            ->order('send_time desc')
            ->order('send_id asc');

        $this->filterFields($query, [
            'phone'  => true,
            'content',
            'status' => true,
        ]);

        return $query->paginate();
    }

    #[Get('sms/task/:id/export')]
    #[Pattern('id', '\d+')]
    public function export($id)
    {
        $query = SmsLog::where('user_id', $this->user->id)
            ->field('phone,status,send_time,message')
            ->where('status', '<>', 0)
            ->where('task_id', $id)
            ->order('send_time desc')
            ->order('send_id asc');

        return $query->paginate(1000);
    }


    #[Post('sms/task')]
    public function save()
    {
        $data = $this->validate([
            'name|任务名称'     => 'require',
            'template|短信模板' => 'require',
            'type'          => 'require|in:file,input',
            'file'          => function ($value, $data) {
                if ($data['type'] == 'file' && empty($value)) {
                    return '请上传号码文件';
                }
                return true;
            },
            'input'         => function ($value, $data) {
                if ($data['type'] == 'input' && empty($value)) {
                    return '请输入号码';
                }
                return true;
            },
            'regular'       => '',
            'send_time'     => function ($value, $data) {
                if (!empty($data['regular']) && empty($value)) {
                    return '请选择定时任务日期与时间';
                }
                return true;
            }
        ]);

        $template = SmsTemplate::where('user_id', $this->user->id)->where('status', 1)->where('id', $data['template'])->find();

        if (empty($template)) {
            throw new ValidateException('模板不存在');
        }

        $task = SmsTask::create([
            'user_id'     => $this->user->id,
            'name'        => $data['name'],
            'template_id' => $template->id,
            'type'        => $data['type'],
            'data'        => $data['type'] == 'file' ? $data['file'] : $data['input'],
            'send_time'   => empty($data['regular']) ? null : $data['send_time'],
            'status'      => empty($data['regular']) ? 2 : 0,
        ]);

        if (empty($data['regular'])) {
            queue(SmsBatchSendJob::class, $task->id);
        }
    }

    #[Delete('sms/task/:id')]
    #[Pattern('id', '\d+')]
    public function delete($id)
    {
        $task = SmsTask::where('user_id', $this->user->id)
            ->where('status', 0)
            ->where('id', $id)
            ->findOrFail();

        //仅可撤销开始5分钟之前的任务
        $canRevoke = $task->send_time && $task->send_time->isAfter(Date::now()->addMinutes(5));

        if (!$canRevoke) {
            abort(404);
        }

        $task->save([
            'status' => -1
        ]);
    }

    #[Get('sms/task/template')]
    public function template()
    {
        return SmsTemplate::where('user_id', $this->user->id)
            ->with(['sign'])
            ->where('status', 1)
            ->order('id desc')
            ->select();
    }
}
