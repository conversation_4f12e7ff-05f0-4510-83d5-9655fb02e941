<?php

namespace app\controller\console\assistant;

use app\controller\console\Controller;
use app\lib\Date;
use app\lib\Hecong;
use app\model\AssistantHecong;

class HecongController extends Controller
{
    public function index()
    {
        return AssistantHecong::where('user_id', $this->user->id)->find();
    }

    public function save(Hecong $hecong)
    {
        $data = $this->validate([
            'company|公司名称' => 'require',
            'name|联系人'      => 'require',
        ]);

        $result = $hecong->addTeam($data['company'], $data['name'], $this->user->mobile);

        AssistantHecong::create(array_merge($data, [
            'plan'        => 'trial',
            'expire_time' => Date::now()->add('7 days'),
            'seat'        => 0,
            'ent_id'      => $result['entId'],
            'user_id'     => $this->user->id,
        ]));
    }

    public function login(Hecong $hecong)
    {
        $model = AssistantHecong::where('user_id', $this->user->id)->findOrFail();

        return $hecong->loginProxy($model->ent_id);
    }

    public function buy()
    {
        $data = $this->validate([
            'years|时长' => 'require',
            'seat|席位'  => '',
        ]);

        $model = AssistantHecong::where('user_id', $this->user->id)->findOrFail();

        $goods = new \app\lib\goods\AssistantHecong($model, $data['years'], $data['seat']);

        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

}
