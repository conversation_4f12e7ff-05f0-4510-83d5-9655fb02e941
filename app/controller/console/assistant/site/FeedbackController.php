<?php

namespace app\controller\console\assistant\site;

use app\controller\console\Controller;
use think\db\Raw;

class FeedbackController extends Controller
{
    use WithSite;

    public function index()
    {
        return $this->site->feedbacks()->withCount('entries')->withAvg('entries', 'star')->order('id desc')->select();
    }

    public function read($id)
    {
        return $this->site->feedbacks()->findOrFail($id);
    }

    public function save()
    {
        return $this->site->feedbacks()->save([
            'name' => '未命名评价',
        ]);
    }

    public function update($id)
    {
        $data = $this->validate([
            'name' => 'require',
        ]);

        $this->site->feedbacks()->findOrFail($id)->save($data);
    }

    public function delete($id)
    {
        $this->site->feedbacks()->findOrFail($id)->delete();
    }

    public function question($id)
    {
        $question = $this->request->getContent();

        $this->site->feedbacks()->findOrFail($id)->save([
            'question' => $question,
        ]);
    }

    public function distribution($id)
    {
        $distribution = $this->request->getContent();

        $this->site->feedbacks()->findOrFail($id)->save([
            'distribution' => $distribution,
        ]);
    }

    public function status($id)
    {
        $data = $this->validate([
            'value' => 'require|in:0,1',
        ]);

        $this->site->feedbacks()->findOrFail($id)->save([
            'status' => $data['value'],
        ]);
    }

    public function stats($id)
    {
        /** @var \app\model\AssistantSiteFeedback $feedback */
        $feedback = $this->site->feedbacks()->findOrFail($id);

        $stats['list']  = $feedback->entries()->order('id desc')->where('reason', '<>', '')->limit(4)->select();
        $stats['total'] = $feedback->entries()->count();

        if ($stats['total'] > 0) {
            $stats['star']  = $feedback->entries()->avg('star');
            $stats['stars'] = array_map(function ($value) use ($stats) {
                return $value / $stats['total'] * 100;
            }, $feedback->entries()
                ->field(new Raw('sum(if(star = 1,1,0)) as star1'))
                ->field(new Raw('sum(if(star = 2,1,0)) as star2'))
                ->field(new Raw('sum(if(star = 3,1,0)) as star3'))
                ->field(new Raw('sum(if(star = 4,1,0)) as star4'))
                ->field(new Raw('sum(if(star = 5,1,0)) as star5'))
                ->find()->toArray());
        } else {
            $stats['star']  = 0;
            $stats['stars'] = [
                'star1' => 0,
                'star2' => 0,
                'star3' => 0,
                'star4' => 0,
                'star5' => 0,
            ];
        }

        return $stats;
    }

    public function entry($id)
    {
        return $this->site->feedbacks()->findOrFail($id)->entries()->order('id desc')->paginate();
    }
}
