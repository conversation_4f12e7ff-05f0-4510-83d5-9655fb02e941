<?php

namespace app\controller\console\assistant\site;

use app\controller\console\Controller;
use app\model\AssistantSite;
use app\model\AssistantSiteMember;
use app\model\User;
use think\Cache;
use think\exception\ValidateException;

class InviteController extends Controller
{
    public function read(Cache $cache, $code)
    {
        $ids = $cache->get("assistant#site#invite#{$code}");

        if (empty($ids) || !is_array($ids) || count($ids) != 2) {
            abort(404);
        }

        [$siteId, $userId] = $ids;

        return [
            'site' => AssistantSite::findOrFail($siteId),
            'user' => User::findOrFail($userId),
        ];
    }

    public function save(Cache $cache, $code)
    {
        $ids = $cache->get("assistant#site#invite#{$code}");

        if (empty($ids) || !is_array($ids) || count($ids) != 2) {
            abort(404);
        }

        [$siteId] = $ids;

        $site = AssistantSite::findOrFail($siteId);

        if ($site->members()->attached($this->user)) {
            throw new ValidateException('您已经是该站点的成员了');
        }
        $site->members()->attach($this->user, ['access_level' => AssistantSiteMember::MASTER]);
        $cache->delete("assistant#site#invite#{$code}");
    }
}
