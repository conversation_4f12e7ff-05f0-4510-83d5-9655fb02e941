<?php

namespace app\controller\console\assistant\site;

use think\Request;

trait WithSite
{
    /** @var \app\model\AssistantSite */
    protected $site;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('site');
            if ($id) {
                $this->site = $this->user->assistantSites()->where('id', $id)->findOrFail();
            }
            return $next($request);
        });
    }
}
