<?php

namespace app\controller\console\assistant\site;

use app\controller\console\Controller;
use app\lib\Date;

class IndexController extends Controller
{
    use WithSite;

    public function index()
    {
        return $this->user->assistantSites()->order('id desc')->select()->append(['stats'], true);
    }

    public function save()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
            'url|网站地址'  => 'require|url',
        ]);

        $data['plan']        = 'trial';
        $data['expire_time'] = (string) Date::now()->addDays(7);

        $this->user->assistantSites()->save($data, [
            'access_level' => \app\model\AssistantSiteMember::OWNER,
        ]);
    }

    public function read()
    {
        return $this->site;
    }

    public function update()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
            'url|网站地址'  => 'require|url',
        ]);

        $this->site->save($data);
    }

    public function appearance()
    {
        $data = $this->request->getContent();

        $this->site->save([
            'appearance' => $data,
        ]);
    }

    public function features()
    {
        $data = $this->request->getContent();

        $this->site->save([
            'features' => $data,
        ]);
    }

    public function customs()
    {
        $data = $this->request->getContent();

        $this->site->save([
            'customs' => $data,
        ]);
    }

    public function broadcast()
    {
        $data = $this->request->getContent();

        $this->site->save([
            'broadcast' => $data,
        ]);
    }

    public function scripts()
    {
        $data = $this->request->getContent();

        $this->site->save([
            'scripts' => $data,
        ]);
    }

    public function delete()
    {
        $this->site->delete();
    }

    public function buy()
    {
        $data = $this->validate([
            'years|时长' => 'require',
        ]);

        $goods = new \app\lib\goods\AssistantSite($this->site, $data['years']);

        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }
}
