<?php

namespace app\controller\console\assistant\site;

use app\controller\console\Controller;
use think\Cache;
use think\exception\ValidateException;

class MemberController extends Controller
{
    use WithSite;

    public function index()
    {
        return $this->site->members()->select();
    }

    public function invite(Cache $cache)
    {
        if (!$this->site->isStandard()) {
            throw new ValidateException('升级至标准版后才能邀请成员');
        }

        $code = uniqid();

        $cache->set("assistant#site#invite#{$code}", [$this->site->id, $this->user->id], 60 * 30);

        return ['code' => $code];
    }
}
