<?php

namespace app\controller\console\assistant\site;

use app\controller\console\Controller;
use app\lib\Date;
use app\model\AssistantSiteEvent;
use think\db\Raw;
use think\facade\Db;

class TracerController extends Controller
{
    use WithSite;

    public function active()
    {
        $date = Date::now()->subMinutes(5);

        $active = $this->site->events()->where('create_time', '>=', $date)->count('distinct session_id');

        return ['active' => $active];
    }

    public function stats($period = '24hours')
    {
        $getStats = function (Date $start, Date $end) {

            $dateField = get_ck_date_query('create_time', 'day');

            $subSql = $this->site->events()
                ->field(
                    new Raw(<<<EOT
session_id, 
{$dateField} time_series,
count(*) c,
min(create_time) min_time,
max(create_time) max_time
EOT
                    )
                )
                ->where('event_type', AssistantSiteEvent::PAGEVIEW)
                ->whereBetween('create_time', [$start, $end])
                ->group('session_id, time_series')
                ->buildSql();

            $result = Db::connect('clickhouse')
                ->table($subSql . ' t')
                ->field(
                    new Raw(<<<EOT
sum(t.c) as "pageviews",
count(distinct t.session_id) as "uniques",
sum(if(t.c = 1,1,0)) as "bounces",
sum(if(max_time<min_time + interval 1 hour,max_time-min_time,0)) as "totaltime"
EOT
                    )
                )
                ->select();

            return $result[0];
        };

        [$start, $end] = $this->getPeriod($period);

        $distance  = $start->diff($end);
        $prevStart = $start->copy()->sub($distance);
        $prevEnd   = $end->copy()->sub($distance);

        $stats     = $getStats($start, $end);
        $prevStats = $getStats($prevStart, $prevEnd);

        return array_reduce(array_keys($stats), function ($obj, $key) use ($prevStats, $stats) {
            $obj[$key] = [
                'value'  => (int) $stats[$key],
                'change' => (int) $stats[$key] - (int) $prevStats[$key],
            ];
            return $obj;
        }, []);
    }

    public function pageviews($period = '24hours')
    {
        [$start, $end, $unit, $list] = $this->getPeriod($period);

        $getPageviews = function ($start, $end, $count) use ($unit) {

            $dateField = get_ck_date_query('create_time', $unit);

            return $this->site->events()
                ->field(
                    new Raw(<<<EOT
{$dateField} x,
count({$count}) y
EOT
                    )
                )
                ->where('event_type', AssistantSiteEvent::PAGEVIEW)
                ->whereBetween('create_time', [$start, $end])
                ->group('x')
                ->select();
        };

        $pageviews = fill_data($list, $getPageviews($start, $end, '*'), 'x', 'y');
        $sessions  = fill_data($list, $getPageviews($start, $end, 'distinct session_id'), 'x', 'y');

        return [
            'pageviews' => $pageviews,
            'sessions'  => $sessions,
        ];
    }

    public function metrics($type, $period = '24hours')
    {
        [$start, $end] = $this->getPeriod($period);

        $sessionColumns = [
            'browser'  => 'browser',
            'os'       => 'os',
            'device'   => 'device',
            'screen'   => 'screen',
            'province' => [
                'province_code',
                function ($query) {
                    $query->where('country_code', 'CN');
                },
            ],
        ];

        $eventColumns = [
            'url'      => 'url_path',
            'referrer' => [
                "trim(leading 'www.' from referrer_domain)",
                function ($query) {
                    $query->whereRaw('referrer_domain != url_domain');
                },
            ],
        ];

        if (array_key_exists($type, $sessionColumns)) {
            $getSessionMetrics = function ($column, $start, $end) {
                $callback = null;
                if (is_array($column)) {
                    [$column, $callback] = $column;
                }

                $query = $this->site->events()
                    ->field(
                        new Raw(<<<EOT
{$column} x, 
count(distinct session_id) y
EOT
                        )
                    )
                    ->where('event_type', AssistantSiteEvent::PAGEVIEW)
                    ->whereBetween('create_time', [$start, $end])
                    ->group('x')
                    ->order('y desc')
                    ->limit(100);

                if ($callback) {
                    $callback($query);
                }

                return $query->select();
            };

            return $getSessionMetrics($sessionColumns[$type], $start, $end);
        }

        if (array_key_exists($type, $eventColumns)) {
            $getPageviewMetrics = function ($column, $start, $end) {
                $callback = null;
                if (is_array($column)) {
                    [$column, $callback] = $column;
                }

                $query = $this->site->events()
                    ->field(
                        new Raw(<<<EOT
{$column} x,
count(*) y
EOT
                        )
                    )
                    ->where('event_type', AssistantSiteEvent::PAGEVIEW)
                    ->whereBetween('create_time', [$start, $end])
                    ->group('x')
                    ->order('y desc')
                    ->limit(100);

                if ($callback) {
                    $callback($query);
                }

                return $query->select();
            };

            return $getPageviewMetrics($eventColumns[$type], $start, $end);
        }

        abort(404);
    }

    protected function getPeriod($period = '24hours')
    {
        if (!$this->site->isStandard() && $period != '24hours') {
            abort(403);
        }

        return get_period($period);
    }

}
