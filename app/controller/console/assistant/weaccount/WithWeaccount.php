<?php

namespace app\controller\console\assistant\weaccount;

use app\model\AssistantWeaccount;
use app\Request;

trait WithWeaccount
{
    /** @var \app\model\AssistantWeaccount */
    protected $weaccount;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function (Request $request, $next) {
            $this->weaccount = AssistantWeaccount::where('user_id', $this->user->id)
                ->findOrFail($request->param('weaccount'));
            return $next($request);
        });
    }
}
