<?php

namespace app\controller\console\assistant\weaccount;

use app\controller\console\Controller;
use app\lib\Date;
use Ramsey\Uuid\Uuid;
use think\db\Query;

class TokenController extends Controller
{
    use WithWeaccount;

    public function index()
    {
        return $this->weaccount->accessTokens()->where(function (Query $query) {
            $query->whereNull('expire_time', 'OR');
            $query->whereOr('expire_time', '>', Date::now());
        })->order('id desc')->select();
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称'            => 'require',
            'expire_time|到期时间' => 'datetime',
        ]);

        return $this->weaccount->accessTokens()->save([
            'name'        => $data['name'],
            'token'       => Uuid::uuid4()->toString(),
            'expire_time' => $data['expire_time'] ?? null,
        ])->hidden([]);
    }

    public function delete($id)
    {
        $weaccount = $this->weaccount->accessTokens()->findOrFail($id);
        $weaccount->delete();
    }
}
