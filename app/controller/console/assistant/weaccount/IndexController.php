<?php

namespace app\controller\console\assistant\weaccount;

use app\controller\console\Controller;
use app\lib\Date;
use app\lib\wechat\wxa\Application;
use app\model\AssistantWeaccount;
use app\model\WxaWeaccount;
use Spatie\Url\Url;
use think\facade\Db;
use think\helper\Arr;

class IndexController extends Controller
{
    use WithWeaccount;

    public function index()
    {
        return AssistantWeaccount::where('user_id', $this->user->id)->order('id desc')->with(['weaccount'])->select();
    }

    public function read($id)
    {
        return AssistantWeaccount::where('user_id', $this->user->id)->where('id', $id)->with(['weaccount'])
            ->findOrFail();
    }

    public function qrcode(Application $application)
    {
        $result = $application->getClient()->post('/cgi-bin/component/api_create_preauthcode', ['json' => [
            'component_appid' => $application->getAccount()->getAppId(),
        ]]);

        $result = $application->getHttpClient()->post('https://mp.weixin.qq.com/safe/safeqrconnect', [
            'form_params' => [
                'action'                  => 'bindcomponent',
                'state'                   => '0',
                'component_appid'         => $application->getAccount()->getAppId(),
                'component_pre_auth_code' => $result['pre_auth_code'],
                'component_redirect_uri'  => 'https://www.topthink.com',
            ],
        ]);

        $result = json_decode($result->getBody()->getContents(), true);

        return json([
            'url'  => "https://mp.weixin.qq.com/safe/safeqrcode?action=bindcomponent&uuid={$result['uuid']}&auth_type=1",
            'uuid' => $result['uuid'],
        ]);
    }

    public function check(Application $application)
    {
        $result = $application->getHttpClient()->get('https://mp.weixin.qq.com/safe/safeuuid', [
            'query' => [
                'timespam' => time(),
                'uuid'     => $this->request->get('uuid'),
            ],
        ]);

        $result = json_decode($result->getBody()->getContents(), true);

        if (Arr::get($result, 'errcode') == 405) {
            $res = Arr::get($result, 'confirm_resp');

            $redirectUri = Url::fromString(Arr::get($res, 'redirect_uri'));

            $authCode = $redirectUri->getQueryParameter('auth_code');

            $auth = $application->getAuthorization($authCode);

            $info = $application->getAuthorizerInfo($auth->getAppId());

            return Db::transaction(function () use ($info, $auth) {
                $weaccount = WxaWeaccount::where('app_id', $auth->getAppId())->find();

                if ($weaccount) {
                    $weaccount->save([
                        'refresh_token' => $auth->getRefreshToken(),
                        'nickname'      => $info['authorizer_info']['nick_name'],
                        'avatar'        => $info['authorizer_info']['head_img'],
                        'signature'     => $info['authorizer_info']['signature'],
                        'status'        => 1,
                    ]);
                } else {
                    $weaccount = WxaWeaccount::create([
                        'app_id'        => $auth->getAppId(),
                        'refresh_token' => $auth->getRefreshToken(),
                        'nickname'      => $info['authorizer_info']['nick_name'],
                        'avatar'        => $info['authorizer_info']['head_img'],
                        'signature'     => $info['authorizer_info']['signature'],
                        'status'        => 1,
                    ]);
                }

                $assistant = AssistantWeaccount::where('user_id', $this->user->id)
                    ->where('weaccount_id', $weaccount->id)
                    ->find();

                if (empty($assistant)) {
                    AssistantWeaccount::create([
                        'user_id'      => $this->user->id,
                        'weaccount_id' => $weaccount->id,
                        'plan'         => 'trial',
                        'expire_time'  => Date::now()->addDays(7),
                    ]);
                }
            });
        } else {
            return json($result, 449);
        }
    }

}
