<?php

namespace app\controller\console\ai;

use app\controller\console\Controller;
use app\lib\goods\AiPackage;
use app\model\Ai;
use app\model\ai\Log;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\db\Raw;

class IndexController extends Controller
{
    #[Get('ai')]
    public function index()
    {
        return Ai::where('user_id', $this->user->id)->findOrFail();
    }

    #[Post('ai')]
    public function save()
    {
        Ai::create([
            'user_id' => $this->user->id,
            'token'   => 100 * 1000,
        ]);
    }

    #[Post('ai/buy')]
    public function buy()
    {
        $data = $this->validate([
            'package' => ['require', function ($value) {
                return in_array($value, array_keys(Ai::PACKAGES));
            }],
        ]);

        $goods = new AiPackage($data['package']);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

    #[Post('ai/warn')]
    public function warn()
    {
        $data = $this->validate([
            'token' => 'require|integer|egt:0',
        ]);

        $this->user->ai->save([
            'warn_token' => $data['token'],
        ]);
    }

    #[Get('ai/stats')]
    public function stats($period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $getTokens = function ($start, $end, $type) use ($unit) {

            $dateField = get_ck_date_query('create_time', $unit);

            return Log::where('user_id', $this->user->id)
                ->where('user_type', Log::TYPE_USER)
                ->field(new Raw(<<<EOT
{$dateField} x,
sum(prompt+completion) y
EOT
                ))
                ->where('type', $type)
                ->whereBetween('create_time', [$start, $end])
                ->group('x')
                ->select();
        };

        $chat  = fill_data($list, $getTokens($start, $end, 'chat'), 'x', 'y');
        $image = fill_data($list, $getTokens($start, $end, 'image'), 'x', 'y');
        $text  = fill_data($list, $getTokens($start, $end, 'text'), 'x', 'y');
        $audio = fill_data($list, $getTokens($start, $end, 'audio'), 'x', 'y');
        $video = fill_data($list, $getTokens($start, $end, 'video'), 'x', 'y');

        return [
            'chat'  => $chat,
            'image' => $image,
            'text'  => $text,
            'audio' => $audio,
            'video' => $video,
        ];
    }
}
