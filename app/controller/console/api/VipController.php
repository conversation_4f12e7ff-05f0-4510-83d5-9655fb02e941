<?php

namespace app\controller\console\api;

use app\controller\console\Controller;
use app\lib\Date;
use app\lib\goods\ApiVipPlan;
use app\lib\goods\ApiVipUpgrade;
use app\model\ApiVip;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;
use think\exception\ValidateException;

class VipController extends Controller
{
    #[Get('api/vip')]
    public function index()
    {
        return ApiVip::getByUser($this->user);
    }

    #[Post('api/vip')]
    public function save()
    {
        $data = $this->validate([
            'years' => 'require',
            'plan'  => 'require|in:senior,gold',
        ]);

        $vip = ApiVip::where('user_id', $this->user->id)->find();

        if ($vip && !$vip->isExpired() && $vip->plan != $data['plan']) {
            throw new ValidateException('会员类型不匹配');
        }

        $goods = new ApiVipPlan($data['plan'], $data['years']);

        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

    #[Put('api/vip')]
    public function update()
    {
        $vip = ApiVip::where('user_id', $this->user->id)->find();

        if (!$vip || $vip->isExpired() || $vip->plan != ApiVipPlan::SENIOR) {
            throw new ValidateException('会员类型不匹配');
        }

        $days = $vip->expire_time->diffInDays(Date::now());

        $goods = new ApiVipUpgrade($days);

        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }
}
