<?php

namespace app\controller\console\api;

use app\controller\console\Controller;
use app\model\Api;
use app\model\ApiCategory;
use app\model\ApiUser;
use app\model\ApiUserPackage;
use app\model\ApiVip;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\exception\ValidateException;

class ApplyController extends Controller
{

    #[Post('api/apply/:id')]
    public function save($id)
    {
        $api = Api::where('status', 1)->findOrFail($id);

        $api->ensureAvailableForUser($this->user);

        //检查是否达上限
        if ($api->isFree()) {
            $count = $this->user->apis()->where('price_type', 0)->count();

            $vip = ApiVip::getByUser($this->user);

            $max = match (true) {
                empty($vip) => 10,
                $vip->isSenior() => 20,
                $vip->isGold() => -1,
            };

            if ($max > 0 && $count >= $max) {
                throw new ValidateException('您免费接口申请数量已达上限，可升级会员享受更多接口');
            }
        }

        $this->user->transaction(function () use ($api) {
            if ($this->user->apis()->attached($api)) {
                throw new ValidateException('您已申请过该接口');
            }

            //赠送试用次数
            if ($api->price_type == 2 && $api->trial_nums > 0) {
                ApiUserPackage::create([
                    'name'       => '首次申请赠送套餐',
                    'user_id'    => $this->user->id,
                    'api_id'     => $api->id,
                    'total_nums' => $api->trial_nums,
                    'status'     => 2,
                ]);
            }

            $this->user->apis()->attach($api);
        });
    }

    #[Get('api/apply/category')]
    public function category()
    {
        return ApiCategory::order('sort desc')->select();
    }

    #[Get('api/apply/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        $api = Api::where('status', 1)->findOrFail($id);

        $api->applied = !!$this->user->apis()->attached($api);

        return $api;
    }

    #[Get('api/apply/list')]
    public function list()
    {
        $applied = ApiUser::where('user_id', $this->user->id)->column('api_id');

        $query = Api::where('status', 1);

        if ($this->request->get('category')) {
            $query->where('category_id', $this->request->get('category'));
        } elseif ($this->request->get('keyword')) {
            $query->whereLike('title|description', '%' . $this->request->get('keyword') . '%');
        } else {
            $query->whereNotNull('recommend_time');
        }

        $query->order('recommend_time desc');

        return $query->select()->each(function ($item) use ($applied) {
            $item->applied = in_array($item->id, $applied);
        });
    }
}
