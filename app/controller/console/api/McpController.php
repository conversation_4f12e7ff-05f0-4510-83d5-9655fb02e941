<?php

namespace app\controller\console\api;

use app\controller\console\Controller;
use app\model\ApiMcp;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class McpController extends Controller
{
    #[Get('api/mcp')]
    public function index()
    {
        return ApiMcp::where('user_id', $this->user->id)->order('id', 'desc')->paginate();
    }

    #[Get('api/mcp/:id')]
    public function read($id)
    {
        return ApiMcp::where('user_id', $this->user->id)->findOrFail($id);
    }

    #[Post('api/mcp')]
    public function save()
    {
        $data = $this->validate([
            'name|名称'    => 'require',
            'apis|关联API' => 'require',
        ]);

        ApiMcp::create([
            'user_id' => $this->user->id,
            'name'    => $data['name'],
            'apis'    => $data['apis'],
        ]);
    }

    #[Put('api/mcp/:id')]
    public function update($id)
    {
        $mcp = ApiMcp::where('user_id', $this->user->id)->findOrFail($id);

        $data = $this->validate([
            'name|名称'    => 'require',
            'apis|关联API' => 'require',
        ]);

        $mcp->save($data);
    }

    #[Delete('api/mcp/:id')]
    public function delete($id)
    {
        $mcp = ApiMcp::where('user_id', $this->user->id)->findOrFail($id);

        $mcp->delete();
    }

}
