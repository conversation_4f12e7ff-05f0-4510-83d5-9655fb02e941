<?php

namespace app\controller\console\api;

use app\controller\console\Controller;
use app\lib\goods\ApiPackage;
use app\model\Api;
use app\model\ApiUserPackage;
use think\annotation\route\Get;
use think\annotation\route\Post;

class PackageController extends Controller
{
    protected Api $api;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function ($request, $next) {
            $this->api = $this->user->apis()->findOrFail($request->param('api_id'));
            return $next($request);
        });
    }

    #[Get('api/:api_id/package')]
    public function index()
    {
        return ApiUserPackage::where('user_id', $this->user->id)
            ->where('api_id', $this->api->id)
            ->order('create_time desc')
            ->select();
    }

    #[Post('api/:api_id/package/buy')]
    public function buy()
    {
        $this->api->ensureAvailableForUser($this->user);

        $data = $this->validate([
            'package' => 'require',
        ]);

        $package = $this->api->packages()->findOrFail($data['package']);

        $goods = new ApiPackage($this->api, $package);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }
}
