<?php

namespace app\controller\console\api;

use app\controller\console\Controller;
use app\model\Api;
use app\model\ApiLog;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\db\Raw;

class IndexController extends Controller
{
    #[Get('api')]
    public function index()
    {
        return $this->user->apis()->order('pivot.create_time desc')->paginate();
    }

    #[Get('api/search')]
    public function search()
    {
        $query = $this->user->apis();

        if ($this->request->has('query')) {
            $query->whereLike('title', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (Api $api) {
            return [
                'label' => $api->title,
                'value' => $api->id,
            ];
        });
    }

    #[Get('api/:id')]
    #[Pattern('id', '\d+')]
    public function read($id)
    {
        return $this->user->apis()->findOrFail($id);
    }

    #[Get('api/:id/packages')]
    public function packages($id)
    {
        return $this->user->apis()->with(['packages'])->findOrFail($id);
    }

    #[Post('api/:id/warn')]
    public function warn($id)
    {
        $data = $this->validate([
            'warn_nums|预警次数' => 'require|integer|egt:0',
        ]);

        /** @var \app\model\Api $api */
        $api = $this->user->apis()->findOrFail($id);

        $api->pivot->save([
            'warn_nums' => $data['warn_nums'],
        ]);
    }

    #[Get('api/:id/stats')]
    public function stats($id, $period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_ck_date_query('create_time', $unit);

        $data = ApiLog::where('api_id', $id)
            ->where('user_id', $this->user->id)
            ->field(new Raw(<<<EOT
{$dateField} x,
sum(if(success>0,1,0)) s,
sum(if(success=0,1,0)) f
EOT
            ))
            ->whereBetween('create_time', [$start, $end])
            ->group('x')
            ->select();

        return [
            'success' => fill_data($list, $data, 'x', 's', 'y'),
            'fail'    => fill_data($list, $data, 'x', 'f', 'y'),
        ];
    }

}
