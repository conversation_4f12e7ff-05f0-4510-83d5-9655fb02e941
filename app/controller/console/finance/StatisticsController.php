<?php

namespace app\controller\console\finance;

use app\controller\console\Controller;
use app\model\Charge;
use think\db\Query;

class StatisticsController extends Controller
{
    public function basic()
    {
        $paying = Charge::where('user_id', $this->user->id)
            ->where(function (Query $query) {
                $query->where('is_paid', 0);
                $query->where('channel', 'transfer');
            })
            ->count();

        $invoicing = Charge::invoiceable()
            ->where('user_id', $this->user->id)
            ->sum('amount');

        return json([
            'coin'      => $this->user->coin,
            'money'     => $this->user->money,
            'paying'    => $paying,
            'invoicing' => $invoicing,
        ]);
    }
}
