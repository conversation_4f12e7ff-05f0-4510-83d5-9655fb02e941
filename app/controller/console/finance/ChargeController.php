<?php

namespace app\controller\console\finance;

use app\controller\console\Controller;
use app\lib\Date;
use app\model\Charge;
use think\db\Query;

class ChargeController extends Controller
{
    public function index()
    {
        return Charge::where('user_id', $this->user->id)
            ->where(function (Query $query) {
                $query->whereOr('is_paid', 1);
                $query->whereOr(function (Query $query) {
                    //一个星期内的
                    $query->where('channel', 'transfer');
                    $query->whereTime('create_time', '>', Date::now()->subWeek());
                });
            })
            ->order('create_time desc')
            ->paginate();
    }
}
