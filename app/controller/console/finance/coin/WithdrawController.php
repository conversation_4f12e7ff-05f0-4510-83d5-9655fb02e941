<?php

namespace app\controller\console\finance\coin;

use app\controller\console\Controller;
use app\lib\Soho;
use app\model\CoinLog;
use app\model\User;
use app\notification\Message;
use think\Db;
use think\exception\ValidateException;
use yunwuxin\facade\Notification;

class WithdrawController extends Controller
{
    public function index()
    {
        return $this->user->coinWithdraws()->order('create_time desc')->paginate(10);
    }

    public function create()
    {
        if (!$this->user->isCertified()) {
            throw new ValidateException('请先完成实名认证');
        }

        $account = $this->user->coinWithdraws()->order('id desc')->where('status', 1)->value('account');

        return json([
            'total'   => $this->user->coin,
            'name'    => $this->user->certification->name,
            'account' => $account,
        ]);
    }

    public function save(Db $db, Soho $soho)
    {
        if (!$this->user->isCertified()) {
            throw new ValidateException('请先完成实名认证');
        }

        $data = $this->validate([
            'amount|提现金额'    => 'require|float|egt:100|elt:' . $this->user->coin / 100,
            'account|支付宝账号' => 'require',
        ]);

        $db->transaction(function () use ($soho, $data) {
            $amount = (int) ($data['amount'] * 100);

            $withdraw = $this->user->coinWithdraws()->save([
                'amount'  => $amount,
                'account' => $data['account'],
                'name'    => $this->user->certification->name,
            ]);

            $this->user->updateCoin(CoinLog::TYPE_DEC, $amount, "提现#{$withdraw->id}");

            $soho->register(
                $this->user->certification->name,
                $this->user->certification->identity,
                $this->user->mobile,
                $data['account']
            );
        });

        Notification::send(User::getAdmins(), new Message('您有一个“云币提现”审批需要处理'));
    }
}
