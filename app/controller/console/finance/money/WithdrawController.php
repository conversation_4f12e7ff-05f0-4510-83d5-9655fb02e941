<?php

namespace app\controller\console\finance\money;

use app\controller\console\Controller;
use app\model\MoneyLog;
use app\model\MoneyWithdraw;
use app\model\User;
use app\notification\Message;
use think\Db;
use think\exception\ValidateException;
use yunwuxin\facade\Notification;

class WithdrawController extends Controller
{
    public function index()
    {
        return $this->user->moneyWithdraws()->order('id desc')->paginate(10);
    }

    public function create()
    {
        if (!$this->user->isEnterprise()) {
            throw new ValidateException('请先完成企业认证');
        }

        $data = [
            'total'      => $this->user->money,
            'enterprise' => $this->user->enterprise->name,
        ];

        /** @var MoneyWithdraw $last */
        $last = $this->user->moneyWithdraws()->order('id desc')->where('status', 1)->find();

        if ($last) {
            $data['last'] = ['name' => $last->name, 'account' => $last->account];
        }

        return json($data);
    }

    public function save(Db $db)
    {
        if (!$this->user->isCertified()) {
            throw new ValidateException('请先完成实名认证');
        }

        $names = [$this->user->certification->name];
        if ($this->user->isEnterprise()) {
            $names[] = $this->user->enterprise->name;
        }

        $data = $this->validate([
            'amount|提现金额'    => 'require|float|egt:0|elt:' . $this->user->money / 100,
            'account|支付宝账号' => 'require',
            'name|真实姓名'      => 'require|in:' . join(',', $names),
        ]);

        $db->transaction(function () use ($data) {
            $amount = (int) ($data['amount'] * 100);

            $withdraw = $this->user->moneyWithdraws()->save([
                'amount'  => $amount,
                'account' => $data['account'],
                'name'    => $data['name'],
            ]);

            $this->user->updateMoney(MoneyLog::TYPE_DEC, $amount, "提现#{$withdraw->id}");
        });

        Notification::send(User::getAdmins(), new Message('您有一个“余额提现”审批需要处理'));
    }
}
