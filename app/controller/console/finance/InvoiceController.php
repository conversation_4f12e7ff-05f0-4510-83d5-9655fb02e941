<?php

namespace app\controller\console\finance;

use app\controller\console\Controller;
use app\model\Charge;
use app\model\Invoice;
use app\model\User;
use app\notification\Message;
use think\Db;
use think\exception\ValidateException;
use think\model\Collection;
use yunwuxin\facade\Notification;

class InvoiceController extends Controller
{
    public function index()
    {
        return Invoice::where('user_id', $this->user->id)->order('id desc')->paginate();
    }

    public function invoiceable()
    {
        return Charge::invoiceable()->where('user_id', $this->user->id)->select();
    }

    public function save(Db $db)
    {
        $data = $this->validate([
            'type|发票类型'              => 'require|in:1,2',
            'title|发票抬头'             => 'require',
            'taxpayer_number|纳税人识别号' => 'require',
        ]);

        /** @var Collection $charges */
        $charges = Charge::invoiceable()
            ->where('user_id', $this->user->id)
            ->whereIn('id', $this->request->param('orders'))
            ->select();

        $amount = array_sum($charges->column('net_amount'));

        if ($amount <= 0) {
            throw new ValidateException('请选择开票订单');
        }

        $data['user_id'] = $this->user->id;
        $data['amount']  = $amount;

        $db->transaction(function () use ($charges, $data) {
            $invoice = Invoice::create($data);

            $charges->update([
                'invoice_id' => $invoice->id,
            ]);
        });

        Notification::send(User::getAdmins(), new Message('您有一个“发票申请”审批需要处理'));
    }

    public function charge($id)
    {
        $invoice = Invoice::where('user_id', $this->user->id)->findOrFail($id);

        return $invoice->charges;
    }

    public function delete($id)
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::where('status', -1)->findOrFail($id);

        $invoice->delete();
    }
}
