<?php

namespace app\controller\console;

use app\model\Order;

class Order<PERSON>ontroller extends Controller
{
    /** @var \app\model\Order */
    protected $order;

    protected function initialize()
    {
        $this->middleware(function ($request, $next) {

            $this->order = Order::where('user_id', $this->user->id)
                ->where('order_no', $this->request->param('order_no'))
                ->findOrFail();

            return $next($request);
        })->only(['paid', 'check']);
    }

    public function check()
    {
        if ($this->order->charge->status != 1) {
            abort(449);
        }
    }
}
