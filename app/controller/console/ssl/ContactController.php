<?php

namespace app\controller\console\ssl;

use app\controller\console\Controller;
use app\model\SslContact;

class ContactController extends Controller
{
    public function index()
    {
        return SslContact::where('user_id', $this->user->id)->select();
    }

    public function save()
    {
        $data = $this->validate([
            'first_name|姓'    => 'require',
            'last_name|名'     => 'require',
            'position|职位'    => 'require',
            'telephone|手机号' => 'require|mobile',
            'email|邮箱'       => 'require|email',
        ]);

        $data['user_id'] = $this->user->id;

        return SslContact::create($data);
    }

    public function update($id)
    {
        $contact = SslContact::where('user_id', $this->user->id)->where('id', $id)->findOrFail();
        $data    = $this->validate([
            'first_name|姓'    => 'require',
            'last_name|名'     => 'require',
            'position|职位'    => 'require',
            'telephone|手机号' => 'require|mobile',
            'email|邮箱'       => 'require|email',
        ]);
        return $contact->save($data);
    }

    public function delete($id)
    {
        SslContact::where('user_id', $this->user->id)->where('id', $id)->delete();
    }
}
