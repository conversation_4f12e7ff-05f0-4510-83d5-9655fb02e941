<?php

namespace app\controller\console\ssl;

use app\controller\console\Controller;
use app\model\SslCertificate;
use Exception;
use think\exception\ValidateException;

class CertController extends Controller
{
    public function index()
    {
        return SslCertificate::where('user_id', $this->user->id)->order('id desc')->where('status', 1)->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'name|证书名称'        => 'require',
            'certificate|证书内容' => 'require',
            'key|私钥内容'         => 'require',
        ]);
        try {
            $info = \Spatie\SslCertificate\SslCertificate::createFromString($data['certificate']);
        } catch (Exception) {
            throw new ValidateException(['certificate' => '证书格式有误']);
        }
        if (!$info->isValid()) {
            throw new ValidateException(['certificate' => '证书不在有效期内']);
        }

        if (!openssl_x509_check_private_key($data['certificate'], $data['key'])) {
            throw new ValidateException(['key' => '证书内容与私钥不匹配']);
        }

        $data['user_id']     = $this->user->id;
        $data['brand']       = '自有';
        $data['expire_time'] = $info->expirationDate();
        $data['domain']      = $info->getDomain();
        SslCertificate::create($data);
    }

    public function read($id)
    {
        return SslCertificate::where('user_id', $this->user->id)->where('id', $id)->findOrFail();
    }

    public function delete($id)
    {
        SslCertificate::where('user_id', $this->user->id)->where('id', $id)->delete();
    }
}
