<?php

namespace app\controller\console\ssl;

use app\controller\console\Controller;
use app\model\SslOrg;

class OrgController extends Controller
{

    public function index()
    {
        return SslOrg::where('user_id', $this->user->id)->select();
    }

    public function save()
    {
        $data = $this->validate([
            'name|公司名称'            => 'require',
            'credit_code|社会信用代码' => 'require',
            'country|国家'             => 'require',
            'province|省份'            => 'require',
            'locality|城市'            => 'require',
            'address|地址'             => 'require',
            'postal_code|邮编'         => 'require|\d{6}',
            'telephone|联系电话'       => 'require|mobile',
        ]);

        $data['user_id'] = $this->user->id;
        SslOrg::create($data);
    }

    public function update($id)
    {
        $contact = SslOrg::where('user_id', $this->user->id)->where('id', $id)->findOrFail();
        $data    = $this->validate([
            'name|公司名称'            => 'require',
            'credit_code|社会信用代码' => 'require',
            'country|国家'             => 'require',
            'province|省份'            => 'require',
            'locality|城市'            => 'require',
            'address|地址'             => 'require',
            'postal_code|邮编'         => 'require|\d{6}',
            'telephone|联系电话'       => 'require|mobile',
        ]);
        return $contact->save($data);
    }

    public function delete($id)
    {
        SslOrg::where('user_id', $this->user->id)->where('id', $id)->delete();
    }
}
