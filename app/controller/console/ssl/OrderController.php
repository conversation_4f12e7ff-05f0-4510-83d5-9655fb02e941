<?php

namespace app\controller\console\ssl;

use app\controller\console\Controller;
use app\lib\Certbase;
use app\model\SslBrand;
use app\model\SslCertificate;
use app\model\SslContact;
use app\model\SslOrder;
use app\model\SslOrg;
use think\exception\ValidateException;
use think\helper\Str;

class OrderController extends Controller
{

    public function brands()
    {
        return SslBrand::order('sort desc')->where('status', 1)->select();
    }

    public function index()
    {
        return SslOrder::where('user_id', $this->user->id)->order('id desc')->paginate();
    }

    public function issue(Certbase $certbase, $id)
    {
        $order = SslOrder::where('user_id', $this->user->id)->findOrFail($id);

        $data = $this->validate([
            'one'      => function ($value) use ($order) {
                if ($order->one > 0 && count(array_filter($value)) < $order->one) {
                    return '请补全标准域名';
                }
                return true;
            },
            'wildcard' => function ($value) use ($order) {
                if ($order->wildcard > 0 && count(array_filter($value)) < $order->wildcard) {
                    return '请补全泛域名';
                }
                return true;
            },
            'contact'  => 'require',
            'org'      => function ($value) use ($order) {
                if ($order->type == 'ov') {
                    if (empty($value)) {
                        return '请选择公司信息';
                    }
                    if (!SslOrg::where('user_id', $this->user->id)->find($value)) {
                        return '公司信息有误';
                    }
                }
                return true;
            },
        ]);

        $domains = array_merge($data['one'] ?? [], array_map(function ($domain) {
            return "*.{$domain}";
        }, $data['wildcard'] ?? []));

        $contact = SslContact::where('user_id', $this->user->id)->find($data['contact']);
        if (empty($contact)) {
            throw new ValidateException('联系人信息有误');
        }

        $params = [
            'year'        => 1,
            'dcvMethod'   => $order->auth_type,
            'contactInfo' => [
                'lastname'  => $contact->last_name,
                'firstname' => $contact->first_name,
                'position'  => $contact->position,
                'email'     => $contact->email,
                'telephone' => $contact->telephone,
            ],
        ];

        if ($order->type == 'ov') {
            if (empty($data['org'])) {
                throw new ValidateException('请选择公司信息');
            }
            $org = SslOrg::where('user_id', $this->user->id)->find($data['org']);
            if (empty($org)) {
                throw new ValidateException('公司信息有误');
            }

            $params['orgInfo'] = [
                'orgName'             => $org->name,
                'creditCode'          => $org->credit_code,
                'country'             => $org->country,
                'province'            => $org->province,
                'locality'            => $org->locality,
                'address'             => $org->address,
                'postalCode'          => $org->postal_code,
                'telephone'           => $org->telephone,
                'joiCountry'          => $org->country,
                'joiProvince'         => $org->province,
                'joiLocality'         => $org->locality,
                'registryAddr'        => $org->address,
                'dateOfIncorporation' => date('Y-m-d'),
            ];

            $csr = $certbase->csr($domains[0], [
                'country'      => $org->country,
                'locality'     => $org->locality,
                'orgUnit'      => '技术部',
                'organization' => $org->name,
                'province'     => $org->province,
            ]);
        } else {
            $csr = $certbase->csr($domains[0]);
        }

        $params['csr'] = $csr['csr'];

        if (count($domains) > 1) {
            $params['domainNames'] = implode(',', $domains);
        }

        $result = $certbase->create($order->product_id, $params);

        $cert = SslCertificate::create([
            'user_id' => $this->user->id,
            'key'     => $csr['key'],
            'brand'   => $order->brand,
            'name'    => 'cert-' . $result['certID'],
            'status'  => 0,
        ]);

        $order->save([
            'status'       => 1,
            'cert_id'      => $cert->id,
            'domains'      => implode(',', $domains),
            'contact'      => $params['contactInfo'],
            'org'          => $params['orgInfo'] ?? null,
            'out_order_no' => $result['orderNo'],
        ]);
    }

    public function domains(Certbase $certbase, $id)
    {
        $order = SslOrder::where('user_id', $this->user->id)->findOrFail($id);

        return $certbase->domains($order->out_order_no);
    }

    public function verify(Certbase $certbase, $id)
    {
        $order = SslOrder::where('user_id', $this->user->id)->findOrFail($id);

        $certbase->verifyDomains($order->out_order_no);

        $result = $certbase->status($order->out_order_no);

        if ($result['dcvStatus'] == '2002') {
            $order->save(['status' => 2]);
        } else {
            abort(422, '验证失败，请稍后再试');
        }
    }

    public function save()
    {
        $data = $this->validate([
            'brand'    => 'require',
            'type'     => 'require|in:dv,ov',
            'one'      => 'require|number|egt:0',
            'wildcard' => 'require|number|egt:0',
        ]);

        $brand = SslBrand::where('status', 1)->where('id', $data['brand'])->findOrFail();

        if ($data['one'] + $data['wildcard'] <= 0) {
            throw new ValidateException('至少购买一个域名');
        }

        $info = $brand[$data['type']];
        $flex = !Str::contains($info['product'], ',');

        if ($data['one'] + $data['wildcard'] > 1 && !$flex) {
            throw new ValidateException('提交信息有误，请重新提交');
        }

        $price = $info['price'];

        $price['one']      = explode(',', $price['one']);
        $price['wildcard'] = explode(',', $price['wildcard']);

        $goods = new \app\lib\goods\SslOrder($brand, $data['type'], $data['one'], $data['wildcard']);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }
}
