<?php

namespace app\controller\console\admin\finance;

use app\controller\console\admin\Controller;
use app\job\ChargeRevokeJob;
use app\model\Refund;

class RefundController extends Controller
{
    public function index()
    {
        $query = Refund::order('id desc')->with(['charge']);

        return $query->paginate(10);
    }

    public function retry($id)
    {
        $refund = Refund::findOrFail($id);
        if ($refund->status != 1) {
            queue(new ChargeRevokeJob($refund));
            $refund->save([
                'status' => 2,
            ]);
        }
    }
}
