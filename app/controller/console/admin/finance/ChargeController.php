<?php

namespace app\controller\console\admin\finance;

use app\controller\console\admin\Controller;
use app\job\ChargeCompleteJob;
use app\model\Charge;

class ChargeController extends Controller
{
    public function index()
    {
        $query = Charge::order('id desc')->with(['user', 'ord']);

        $this->filterFields($query, [
            'trade_no'    => function ($query, $value) {
                $query->where('trade_no|order_no', $value);
            },
            'user'        => function ($query, $value) {
                $query->where('user_id', $value);
            },
            'app'         => function ($query, $value) {
                $query->where('application_id', $value);
            },
            'subject',
            'status'      => function ($query, $value) {
                switch ($value) {
                    case 1:
                        $query->where('is_paid', 1);
                        break;
                    case 0:
                        $query->where('is_paid', 0);
                        break;
                    case -1:
                        $query->whereNotNull('revoke_time');
                        break;
                    case 2:
                        $query->where(function ($query) {
                            $query->whereNotNull('revoke_time');
                            $query->whereOr('is_paid', 1);
                        });
                        break;
                }
            },
            'create_time' => function ($query, $value) {
                $query->whereBetweenTime('create_time', $value[0], $value[1]);
            },
        ]);

        if (empty($query->getOptions('where'))) {
            $query->where('is_paid', 1);
        }

        return $query->paginate()->append(['can_revoke'], true);
    }

    public function retry($id)
    {
        $charge = Charge::findOrFail($id);
        if ($charge->status == 0 && !$charge->revoke_time) {
            queue(new ChargeCompleteJob($charge));
            $charge->save([
                'status' => 2,
            ]);
        }
    }

    public function paid($id)
    {
        $charge = Charge::findOrFail($id);
        $data   = $this->validate([
            'channel' => 'require',
        ]);
        if (!$charge->is_paid) {
            if ($data['channel'] === 'transfer') {
                //确认企业转账
                $charge->confirm();
            } else {
                //检查支付信息
                $charge->check($data['channel']);
            }
        }
    }

    public function revoke($id)
    {
        $charge = Charge::findOrFail($id);

        $charge->revoke();
    }

    public function invoicing()
    {
        return Charge::invoiceable()->where('channel', 'transfer')->where('user_id', '>', 0)->order('id asc')
            ->with(['user'])->paginate();
    }

    public function invoiced($id)
    {
        $charge = Charge::invoiceable()->where('channel', 'transfer')->where('user_id', '>', 0)->findOrFail($id);

        $charge->save([
            'invoice_id' => -1,
        ]);
    }
}
