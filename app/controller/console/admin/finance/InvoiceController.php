<?php

namespace app\controller\console\admin\finance;

use app\controller\console\admin\Controller;
use app\model\Charge;
use app\model\Invoice;
use think\Db;
use think\exception\ValidateException;

class InvoiceController extends Controller
{

    public function index()
    {
        return Invoice::with(['user'])->where('status', 1)->order('create_time desc')->paginate();
    }

    public function save(Db $db)
    {
        $data = $this->validate([
            'type|发票类型'              => 'require|in:1,2',
            'title|发票抬头'             => 'require',
            'taxpayer_number|纳税人识别号' => 'require',
            'file|发票文件'              => 'require',
            'order|订单'               => 'require',
        ]);

        $charge = Charge::invoiceable()->where('channel', 'transfer')->where('user_id', '>', 0)->find($data['order']);

        if (!$charge) {
            throw new ValidateException('订单不存在');
        }

        $db->transaction(function () use ($data, $charge) {
            $invoice = Invoice::create([
                'type'            => $data['type'],
                'title'           => $data['title'],
                'taxpayer_number' => $data['taxpayer_number'],
                'user_id'         => $charge->user_id,
                'amount'          => $charge->amount,
                'remark'          => $data['file'],
                'status'          => 1,
            ]);

            $charge->save([
                'invoice_id' => $invoice->id,
            ]);
        });
    }

    public function order($id)
    {
        $invoice = Invoice::findOrFail($id);

        return $invoice->charges;
    }
}
