<?php

namespace app\controller\console\admin;

use app\lib\Goods;
use app\model\ApplicationUser;
use app\model\Charge;
use app\model\CoinWithdraw;
use app\model\Enterprise;
use app\model\Invoice;
use app\model\MoneyWithdraw;
use app\model\Order;
use app\model\SmsSign;
use app\model\User;
use Carbon\Carbon;
use think\db\Query;
use think\db\Raw;

class StatisticController extends Controller
{
    public function basic()
    {
        $user = [
            'total'      => User::count(),
            'yesterday'  => User::whereBetween('create_time', [Carbon::yesterday(), Carbon::today()])->count(),
            'enterprise' => Enterprise::where('status', 1)->count(),
        ];

        $order = [
            'total'  => Order::where('status', 1)->count(),
            'amount' => Order::where('status', 1)->sum('amount'),
        ];

        $charge = [
            'total'  => Charge::where('is_paid', 1)->count(),
            'amount' => Charge::where('is_paid', 1)->sum(new Raw('amount-discount')),
        ];

        $approval = [
            'enterprise' => Enterprise::where('status', 0)->count(),
            'invoice'    => Invoice::where('status', 0)->count(),
            'withdraw'   => [
                'coin'  => CoinWithdraw::where('status', 0)->count(),
                'money' => MoneyWithdraw::where('status', 0)->count(),
            ],
            'sms'        => [
                'sign' => SmsSign::where('status', 0)->count(),
            ],
        ];

        return json(compact('user', 'order', 'charge', 'approval'));
    }

    public function charge($period = '30days')
    {
        [$start, $end] = get_period($period);

        $prevStart = $start->copy()->subYear();
        $prevEnd   = $end->copy()->subYear();

        $previousApps = Charge::where('is_paid', 1)
            ->whereBetween('create_time', [$prevStart, $prevEnd])
            ->whereNull('revoke_time')
            ->group('application_id')
            ->field('application_id')
            ->field('SUM(`amount`-`discount`) as preview')
            ->select()
            ->append(['application_name'])
            ->visible([])
            ->toArray();

        $charges = Charge::where('is_paid', 1)
            ->whereBetween('create_time', [$start, $end])
            ->whereNull('revoke_time')
            ->group('application_id')
            ->field('application_id')
            ->field('SUM(`amount`-`discount`) as value')
            ->select()
            ->append(['application_name'])
            ->visible([])
            ->toArray();

        $apps = array_column($previousApps, null, 'application_id');

        foreach ($charges as $charge) {
            $id = $charge['application_id'];

            $charge['items'] = $this->getPeriodData(
                $period,
                Charge::where('is_paid', 1)->where('application_id', $id)->whereNull('revoke_time')
                    ->field('SUM(`amount`-`discount`) as value'),
            );

            if (isset($apps[$id])) {
                $apps[$id] = array_merge($apps[$id], $charge);
            } else {
                $apps[$id] = $charge;
            }
        }

        return json(array_values($apps));
    }

    public function user($period = '30days')
    {
        $new = $this->getPeriodData(
            $period,
            User::field('COUNT(*) as value')
        );

        [$start, $end] = get_period($period);

        $app = ApplicationUser::whereIn('user_id', function (Query $query) use ($end, $start) {
            $query->table('user')->whereBetween('create_time', [$start, $end])->field('id');
        })
            ->group('application_id')
            ->field('application_id')
            ->order('value asc')
            ->field('COUNT(*) as value')
            ->select()
            ->append(['application_name']);

        return json([
            'new' => $new,
            'app' => $app,
        ]);
    }

    public function order($period = '30days')
    {
        [$start, $end] = get_period($period);

        $goods = Order::where('status', 1)
            ->whereBetween('create_time', [$start, $end])
            ->group('goods_type')
            ->field('goods_type')
            ->field('SUM(`amount`) as value')
            ->select()
            ->withAttr('goods_name', function ($v, $order) {
                if (class_exists($order['goods_type'])) {
                    return constant("{$order['goods_type']}::NAME");
                } else {
                    return Goods::NAME;
                }
            })
            ->append(['goods_name'])
            ->reduce(function (array $carry, $item) {
                // 合并相同NAME的商品
                if (empty($carry[$item['goods_name']])) {
                    $carry[$item['goods_name']] = [
                        'goods_name' => $item['goods_name'],
                        'goods_type' => [],
                        'value'      => 0,
                    ];
                }

                $carry[$item['goods_name']]['value']      += $item['value'];
                $carry[$item['goods_name']]['goods_type'] = array_unique(
                    array_merge(
                        $carry[$item['goods_name']]['goods_type'],
                        [$item['goods_type']]
                    )
                );
                return $carry;
            }, []);

        $goods = array_values($goods);

        foreach ($goods as &$item) {
            $item['items'] = $this->getPeriodData(
                $period,
                Order::where('status', 1)->whereIn('goods_type', $item['goods_type'])->field('SUM(`amount`) as value'),
            );
        }

        return json($goods);
    }

    /**
     * @param $period
     * @param Query|\think\Model $query
     * @param $field
     * @return array
     */
    protected function getPeriodData($period, $query, $field = 'create_time')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_date_query($field, $unit);

        $data = $query
            ->field("{$dateField} as date")
            ->whereBetween($field, [$start, $end])
            ->group('date')
            ->order('date asc')
            ->select()
            ->append([])
            ->visible([]);

        return fill_data($list, $data);
    }

}
