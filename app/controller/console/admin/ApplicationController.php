<?php

namespace app\controller\console\admin;

use app\model\Application;

class ApplicationController extends Controller
{
    public function index()
    {
        return Application::paginate(10);
    }

    public function read($id)
    {
        return Application::findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'name'            => 'require',
            'redirect_uri'    => '',
            'logout_uri'      => '',
            'is_confidential' => 'boolean',
            'scopes'          => '',
            'product'         => '',
        ]);

        $data['client_id']     = md5(uniqid());
        $data['client_secret'] = md5(uniqid());

        return Application::create($data);
    }

    public function update($id)
    {
        $app  = Application::findOrFail($id);
        $data = $this->validate([
            'name'            => 'require',
            'redirect_uri'    => 'require',
            'logout_uri'      => '',
            'is_confidential' => 'boolean',
            'scopes'          => '',
            'product'         => '',
        ]);

        $app->save($data);
    }

    public function delete($id)
    {
        $app = Application::findOrFail($id);

        $app->delete();
    }

    public function search()
    {
        $query = Application::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('name', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (Application $app) {
            return [
                'label' => "{$app->name}",
                'value' => $app->id,
            ];
        });
    }
}
