<?php

namespace app\controller\console\admin\wxa;

use app\controller\console\admin\Controller;
use app\job\TemplateReleaseJob;
use app\model\WxaTemplate;
use think\exception\ValidateException;

class TemplateController extends Controller
{
    public function index()
    {
        return WxaTemplate::order('id desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'name' => 'require',
        ]);

        WxaTemplate::create($data);
    }

    public function release($id)
    {
        $template = WxaTemplate::findOrFail($id);

        $data = $this->validate([
            'template_id' => 'require',
            'version'     => 'require',
            'desc'        => 'require',
        ]);

        if (version_compare($data['version'], $template->version, '<=')) {
            throw new ValidateException('新版本不等低于旧版本');
        }

        $template->save($data);

        queue(TemplateReleaseJob::class, $template->id);
    }

    public function retry($id)
    {
        $template = WxaTemplate::findOrFail($id);
        queue(TemplateReleaseJob::class, $template->id);
    }

}
