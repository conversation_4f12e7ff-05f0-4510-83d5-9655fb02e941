<?php

namespace app\controller\console\admin\wxa;

use app\controller\console\admin\Controller;
use app\lib\wechat\wxa\Application;
use app\model\WxaWeapp;

class WeappController extends Controller
{
    public function index()
    {
        return WxaWeapp::with(['template'])->where('status', '>', 0)->order('id desc')->paginate();
    }

    public function audit(Application $application, $id)
    {
        $weapp = WxaWeapp::findOrFail($id);

        $app = $application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);
        if ($this->request->isGet()) {
            return $app->getClient()->get('/wxa/get_latest_auditstatus');
        } else {
            return $app->getClient()->post('/wxa/submit_audit', ['json' => [
                'privacy_api_not_use' => true,
            ]]);
        }
    }

    public function experience(Application $application, $id)
    {
        $weapp = WxaWeapp::findOrFail($id);

        $app = $application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

        return json(['image' => base64_encode($app->getClient()->get('/wxa/get_qrcode'))]);
    }

    public function domain(Application $application, $id)
    {
        $weapp = WxaWeapp::findOrFail($id);

        $app = $application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

        if ($this->request->isGet()) {
            $requestDomain = $app->getClient()->post('/wxa/get_effective_domain', ['json' => new \ArrayObject()]);

            $webviewDomain = $app->getClient()->post('/wxa/setwebviewdomain', ['json' => [
                'action' => 'get',
            ]]);

            return [
                'request' => $requestDomain['effective_domain']['requestdomain'],
                'webview' => $webviewDomain['webviewdomain'],
            ];
        } else {
            //设置服务域名
            $app->getClient()->post('/wxa/modify_domain_directly', ['json' => [
                'action'          => 'set',
                'requestdomain'   => [$weapp->domain],
                "wsrequestdomain" => [],
                "uploaddomain"    => [],
                "downloaddomain"  => [],
                "udpdomain"       => [],
                "tcpdomain"       => [],
            ]]);

            //设置业务域名
            $app->getClient()->post('/wxa/setwebviewdomain', ['json' => [
                'action'        => 'set',
                'webviewdomain' => [$weapp->domain],
            ]]);
        }
    }
}
