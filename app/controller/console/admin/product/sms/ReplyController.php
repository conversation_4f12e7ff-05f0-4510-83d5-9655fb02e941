<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\SmsReply;
use think\annotation\route\Get;

class ReplyController extends Controller
{
    #[Get('product/sms/reply')]
    public function index()
    {
        $query = SmsReply::order('reply_time', 'desc');
        $this->searchField($query, 'mobile');
        return $query->paginate();
    }
}
