<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\SmsLog;
use think\annotation\route\Get;
use think\db\BaseQuery;
use think\db\Raw;

class StatsController extends Controller
{
    #[Get('product/sms/stats')]
    public function index($period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_ck_date_query('send_time', $unit);

        $data = SmsLog::field(
            new Raw(<<<EOT
{$dateField} x,
sum(if(status>0,1,0)) success,
sum(if(status<0,1,0)) fail,
sum(if(status>0,nums,0)) nums
EOT
            )
        )
            ->whereBetween('send_time', [$start, $end])
            ->group('x')
            ->select();

        return [
            'success' => fill_data($list, $data, 'x', 'success', 'y'),
            'fail'    => fill_data($list, $data, 'x', 'fail', 'y'),
            'nums'    => fill_data($list, $data, 'x', 'nums', 'y'),
        ];
    }

    #[Get('product/sms/stats/overview')]
    public function overview($period = '24hours')
    {
        [$start, $end] = get_period($period);

        return SmsLog::field(
            new Raw(<<<EOT
sum(if(status>0,1,0)) success,
sum(if(status<0,1,0)) fail,
count(*) total,
sum(if(status>0,nums,0)) nums
EOT
            )
        )
            ->whereBetween('send_time', [$start, $end])
            ->find();
    }

    #[Get('product/sms/stats/metric/:type')]
    public function metric($type, $period = '24hours')
    {
        $types = [
            'user'     => [
                function (BaseQuery $query) {
                    $query->field(['user_id'])->group('user_id');
                },
                function (SmsLog $item) {
                    return [
                        'x'       => $item->getUsername(),
                        'y'       => $item->total,
                        'user_id' => $item->user_id,
                    ];
                },
            ],
            'sign'     => [
                function (BaseQuery $query) {
                    $query->field(['sign_id'])->group('sign_id');
                },
                function (SmsLog $item) {
                    $sign = $item->getSign();
                    if ($sign) {
                        return [
                            'x' => $sign->name,
                            'y' => $item->total,
                        ];
                    }
                    return [
                        'x' => '未知签名',
                        'y' => $item->total,
                    ];
                },
            ],
            'template' => [
                function (BaseQuery $query) {
                    $query->field(['template_id'])->group('template_id');
                },
                function (SmsLog $item) {
                    $template = $item->getTemplate();
                    if ($template) {
                        return [
                            'x'       => $template->name,
                            'content' => $template->content,
                            'y'       => $item->total,
                        ];
                    }

                    return [
                        'x' => '未知模板',
                        'y' => $item->total,
                    ];
                },
            ],
        ];

        [$dataCallback, $labelCallback] = $types[$type];

        [$start, $end] = get_period($period);

        $total = SmsLog::whereBetween('send_time', [$start, $end])->where('status', 1)->sum('nums');

        $dataQuery = SmsLog::whereBetween('send_time', [$start, $end])
            ->where('status', 1)
            ->field(new Raw('sum(nums) total'));

        $dataCallback($dataQuery);

        $data = $dataQuery->order('total desc')
            ->limit(10)
            ->select()
            ->map($labelCallback);

        return [
            'total' => $total,
            'data'  => $data,
        ];
    }
}
