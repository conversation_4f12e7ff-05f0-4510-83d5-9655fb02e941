<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\SmsLog;
use think\annotation\route\Get;

class LogController extends Controller
{
    #[Get('product/sms/log')]
    public function index($period = '24hours')
    {
        [$start, $end] = get_period($period);

        $query = SmsLog::whereBetween('send_time', [$start, $end])
            ->order('send_time desc')
            ->order('send_id asc');

        $this->filterFields($query, [
            'user'   => function ($query, $value) {
                $query->where('user_id', $value);
            },
            'phone'  => true,
            'content',
            'status' => true,
        ]);

        return $query->paginate();
    }
}
