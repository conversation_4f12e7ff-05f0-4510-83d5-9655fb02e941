<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\SmsTemplate;
use think\annotation\route\Post;
use think\annotation\route\Resource;

#[Resource('product/sms/template')]
class TemplateController extends Controller
{
    public function index($type = 'user')
    {
        $query = SmsTemplate::with(['user', 'sign'])->order('id desc');

        if ($type == 'user') {
            $query->where('user_id', '>', 0);

            $this->filterFields($query, [
                'user' => function ($query, $value) {
                    $query->where('user_id', $value);
                },
                'name',
                'content',
            ]);
        } else {
            $query->where('user_id', 0);
        }

        $this->searchField($query, 'name');

        return $query->paginate();
    }

    public function read($id)
    {
        return SmsTemplate::findOrFail($id);
    }

    public function save()
    {
        $data = $this->validate([
            'name|模板名称'    => 'require',
            'content|模板内容' => 'require',
        ]);

        SmsTemplate::create([
            'name'    => $data['name'],
            'content' => $data['content'],
            'status'  => 1,
        ]);
    }

    public function update($id)
    {
        $template = SmsTemplate::findOrFail($id);

        $data = $this->validate([
            'type|模板类型' => 'require|in:1,2',
        ]);

        $template->save([
            'type' => $data['type'],
        ]);
    }

    public function delete($id)
    {
        $template = SmsTemplate::findOrFail($id);

        $template->delete();
    }

    #[Post('product/sms/template/:id/freeze')]
    public function freeze($id)
    {
        $template = SmsTemplate::where('status', 1)->findOrFail($id);

        $template->save(['status' => 2]);
    }

    #[Post('product/sms/template/:id/unfreeze')]
    public function unfreeze($id)
    {
        $template = SmsTemplate::where('status', 2)->findOrFail($id);

        $template->save(['status' => 1]);
    }
}
