<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\Sms;
use think\annotation\route\Get;
use think\annotation\route\Post;

class UserController extends Controller
{
    #[Get('product/sms/user')]
    public function index()
    {
        $query = Sms::order('id desc')->append(['current_rate_limit'])->with('user');

        $this->filterFields($query, [
            'user' => function ($query, $value) {
                $query->where('user_id', $value);
            },
        ]);

        return $query->paginate();
    }

    #[Post('product/sms/user/:id/batch_send')]
    public function batchSend($id)
    {
        $sms = Sms::findOrFail($id);

        $data = $this->validate([
            'batch_send' => 'require|integer',
        ]);

        $sms->save([
            'batch_send' => $data['batch_send'],
        ]);
    }

    #[Post('product/sms/user/:id/rate_limit')]
    public function rateLimit($id)
    {
        $sms = Sms::findOrFail($id);

        $data = $this->validate([
            'rate_limit' => 'require|integer',
        ]);

        $sms->save([
            'rate_limit' => $data['rate_limit'],
        ]);
    }
}
