<?php

namespace app\controller\console\admin\product\sms;

use app\controller\console\admin\Controller;
use app\model\SmsSign;
use think\annotation\route\Get;
use think\annotation\route\Post;

class SignController extends Controller
{
    #[Get('product/sms/sign')]
    public function index()
    {
        $query = SmsSign::with(['user'])->whereIn('status', [1, 2])->order('id desc');

        $this->filterFields($query, [
            'user' => function ($query, $value) {
                $query->where('user_id', $value);
            },
            'name',
        ]);

        return $query->paginate();
    }

    #[Post('product/sms/sign/:id/freeze')]
    public function freeze($id)
    {
        $sign = SmsSign::where('status', 1)->findOrFail($id);

        $sign->save(['status' => 2]);
    }

    #[Post('product/sms/sign/:id/unfreeze')]
    public function unfreeze($id)
    {
        $sign = SmsSign::where('status', 2)->findOrFail($id);

        $sign->save(['status' => 1]);
    }
}
