<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\lib\Date;
use app\model\Api;
use app\model\ApiCategory;
use app\model\ApiLog;
use think\annotation\route\Get;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Resource;
use think\annotation\route\Route;
use think\db\Raw;

#[Resource('product/api')]
#[Pattern('id', '\d+')]
class IndexController extends Controller
{
    public function index()
    {
        $query = Api::with(['category']);

        $this->searchField($query, 'title');

        return $query->withoutField('content')->withCount(['users'])->order('id desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'name|关联API'           => 'require',
            'category_id|分类'       => 'require',
            'title|标题'             => 'require',
            'description|简介'       => 'require',
            'content|内容'           => 'require',
            'identify_type|认证类型' => 'require|in:0,1,2',
            'price_type|价格类型'    => 'require|in:0,1,2',
            'logo|Logo'              => 'require',
            'trial_nums|试用次数'    => 'number',
        ]);

        Api::create($data);
    }

    public function read($id)
    {
        return Api::findOrFail($id);
    }

    public function update($id)
    {
        $api  = Api::findOrFail($id);
        $data = $this->validate([
            'name|关联API'           => 'require',
            'category_id|分类'       => 'require',
            'title|标题'             => 'require',
            'description|简介'       => 'require',
            'content|内容'           => 'require',
            'identify_type|认证类型' => 'require|in:0,1,2',
            'price_type|价格类型'    => 'require|in:0,1,2',
            'logo|Logo'              => 'require',
            'trial_nums|试用次数'    => 'number',
        ]);
        $api->save($data);
    }

    #[Post('product/api/:id/up')]
    public function up($id)
    {
        $api = Api::findOrFail($id);

        $api->save([
            'status' => 1,
        ]);
    }

    #[Post('product/api/:id/down')]
    public function down($id)
    {
        $api = Api::findOrFail($id);

        $api->save([
            'status' => 0,
        ]);
    }

    #[Route('POST|DELETE', 'product/api/:id/recommend')]
    public function recommend($id)
    {
        $api = Api::findOrFail($id);

        if ($this->request->isDelete()) {
            $api->save([
                'recommend_time' => null,
            ]);
        } else {
            $api->save([
                'recommend_time' => Date::now(),
            ]);
        }
    }

    #[Get('product/api/:id/stats')]
    public function stats($id, $period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_ck_date_query('create_time', $unit);

        $data = ApiLog::where('api_id', $id)
            ->field(new Raw(<<<EOT
{$dateField} x,
sum(if(success>0,1,0)) s,
sum(if(success=0,1,0)) f
EOT
            ))
            ->whereBetween('create_time', [$start, $end])
            ->group('x')
            ->select();

        return [
            'success' => fill_data($list, $data, 'x', 's', 'y'),
            'fail'    => fill_data($list, $data, 'x', 'f', 'y'),
        ];
    }

    #[Get('product/api/categories')]
    public function categories()
    {
        return ApiCategory::order('sort desc')->select()->map(function ($item) {
            return [
                'type'  => 'number',
                'enum'  => [$item->id],
                'title' => $item->name,
            ];
        });
    }

    #[Get('product/api/search')]
    public function search()
    {
        $query = Api::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('title', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (Api $api) {
            return [
                'label' => $api->title,
                'value' => $api->id,
            ];
        });
    }

}
