<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\model\ApiLog;
use app\model\SmsLog;
use think\annotation\route\Get;
use think\db\BaseQuery;
use think\db\Raw;

class StatsController extends Controller
{
    #[Get('product/api/stats')]
    public function index($period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_ck_date_query('create_time', $unit);

        $data = ApiLog::field(new Raw(<<<EOT
{$dateField} x,
sum(if(success>0,1,0)) s,
sum(if(success=0,1,0)) f
EOT
        ))
            ->whereBetween('create_time', [$start, $end])
            ->group('x')
            ->select();

        return [
            'success' => fill_data($list, $data, 'x', 's', 'y'),
            'fail'    => fill_data($list, $data, 'x', 'f', 'y'),
        ];
    }

    #[Get('product/api/stats/metric/:type')]
    public function metric($type, $period = '24hours')
    {
        $types = [
            'user' => [
                function (BaseQuery $query) {
                    $query->field('user_id')
                        ->group('user_id');
                },
                function (ApiLog $item) {
                    return [
                        'x'       => $item->getUsername(),
                        'y'       => $item->total,
                        'user_id' => $item->user_id,
                    ];
                },
                null,
            ],
            'free' => [
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 0)->column('id');
                    $query->whereIn('api_id', $ids)->field('api_id')->group('api_id');
                },
                function (ApiLog $item) {
                    return [
                        'x' => $item->getApiName(),
                        'y' => $item->total,
                    ];
                },
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 0)->column('id');
                    $query->whereIn('api_id', $ids);
                },
            ],
            'vip'  => [
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 1)->column('id');
                    $query->whereIn('api_id', $ids)->field('api_id')->group('api_id');
                },
                function (ApiLog $item) {
                    return [
                        'x' => $item->getApiName(),
                        'y' => $item->total,
                    ];
                },
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 1)->column('id');
                    $query->whereIn('api_id', $ids);
                },
            ],
            'paid' => [
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 2)->column('id');
                    $query->whereIn('api_id', $ids)->field('api_id')->group('api_id');
                },
                function (ApiLog $item) {
                    return [
                        'x' => $item->getApiName(),
                        'y' => $item->total,
                    ];
                },
                function (BaseQuery $query) {
                    $ids = \app\model\Api::where('price_type', 2)->column('id');
                    $query->whereIn('api_id', $ids);
                },
            ],
        ];

        [$dataCallback, $labelCallback, $totalCallback] = $types[$type];

        [$start, $end] = get_period($period);

        $totalQuery = ApiLog::whereBetween('create_time', [$start, $end])->where('success', 1);
        if ($totalCallback) {
            $totalCallback($totalQuery);
        }
        $total = $totalQuery->count();

        $dataQuery = ApiLog::whereBetween('create_time', [$start, $end])
            ->where('success', 1)
            ->field(new Raw('count(*) total'));

        $dataCallback($dataQuery);

        $data = $dataQuery->order('total desc')
            ->limit(10)
            ->select()
            ->map($labelCallback);

        return [
            'total' => $total,
            'data'  => $data,
        ];
    }
}
