<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\model\Api;
use think\annotation\route\Resource;

#[Resource('product/api/:api_id/package')]
class PackageController extends Controller
{
    /** @var Api */
    protected $api;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function ($request, $next) {
            $this->api = Api::findOrFail($request->param('api_id'));
            return $next($request);
        });
    }

    public function index()
    {
        return $this->api->packages;
    }

    public function save()
    {
        $data = $this->validate([
            'nums|套餐次数'       => 'require|number',
            'price|价格'          => 'require|float',
            'original_price|原价' => 'require|float',
            'agency_price|代理价' => 'require|float',
        ]);

        $this->api->packages()->save($data);
    }

    public function update($id)
    {
        $package = $this->api->packages()->findOrFail($id);

        $data = $this->validate([
            'nums|套餐次数'       => 'require|number',
            'price|价格'          => 'require|float',
            'original_price|原价' => 'require|float',
            'agency_price|代理价' => 'require|float',
        ]);

        $package->save($data);
    }

    public function delete($id)
    {
        $package = $this->api->packages()->findOrFail($id);

        $package->delete();
    }
}
