<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\model\ApiUserPackage;
use think\annotation\route\Get;

class UserPackageController extends Controller
{
    #[Get('product/api/package')]
    public function index()
    {
        $query = ApiUserPackage::with(['user', 'api']);

        $this->filterFields($query, [
            'user'  => function ($query, $value) {
                $query->where('user_id', $value);
            },
            'api'   => function ($query, $value) {
                $query->where('api_id', $value);
            },
            'status',
            'usage' => function ($query, $value) {
                switch ($value) {
                    case 1:
                        $query->where('used_nums', 0);
                        break;
                    case 2:
                        $query->where(function ($query) {
                            $query->where('used_nums', '>', 0);
                            $query->whereRaw('used_nums < total_nums');
                        });
                        break;
                    case 3:
                        $query->whereRaw('used_nums >= total_nums');
                        break;
                }
            },
        ]);

        return $query->order('create_time desc')->paginate();
    }
}
