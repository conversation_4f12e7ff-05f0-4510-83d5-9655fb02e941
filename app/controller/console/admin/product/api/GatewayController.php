<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use rpc\contract\api\Api;
use think\annotation\route\Get;

class GatewayController extends Controller
{
    #[Get('product/api/gateway/list')]
    public function list(Api $api)
    {
        $names = \app\model\Api::column('name');

        $list = $api->getList();

        $data = [];

        foreach ($list as $name => $apis) {
            // 排除已存在的
            if (in_array($name, $names)) continue;

            // 排除已下架的
            $apis = array_filter($apis, function ($item) {
                return !isset($item['deprecated']);
            });

            if (empty($apis)) {
                continue;
            }

            $first = array_shift($apis);

            $first['name'] = $name;
            $first['id']   = $name;

            if (!empty($apis)) {
                $first['children'] = array_map(function ($item, $index) use ($name) {
                    $item['id'] = $name . '_' . $index;
                    return $item;
                }, $apis, array_keys($apis));
            }
            $data[] = $first;
        }

        return $data;
    }
}
