<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\model\ApiCategory;
use think\annotation\route\Resource;

#[Resource('product/api/category')]
class CategoryController extends Controller
{
    public function index()
    {
        return ApiCategory::order('sort desc')->select();
    }

    public function save()
    {
        $data = $this->validate([
            'name' => 'require',
            'sort' => 'integer',
        ]);

        ApiCategory::create($data);
    }

    public function update($id)
    {
        $category = ApiCategory::findOrFail($id);

        $data = $this->validate([
            'name' => 'require',
            'sort' => 'integer',
        ]);

        $category->save($data);
    }

    public function delete($id)
    {
        $category = ApiCategory::findOrFail($id);

        $category->delete();
    }
}
