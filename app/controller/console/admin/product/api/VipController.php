<?php

namespace app\controller\console\admin\product\api;

use app\controller\console\admin\Controller;
use app\model\ApiVip;
use think\annotation\route\Get;
use think\annotation\route\Post;

class VipController extends Controller
{
    #[Get('product/api/vip')]
    public function index()
    {
        return ApiVip::with('user')->order('create_time desc')->paginate();
    }

    #[Post('product/api/vip/:id/plan')]
    public function plan($id)
    {
        $vip = ApiVip::where('user_id', $id)->findOrFail();

        $data = $this->validate([
            'plan'        => 'require',
            'expire_time' => 'require',
        ]);

        $vip->save($data);
    }
}
