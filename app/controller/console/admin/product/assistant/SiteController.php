<?php

namespace app\controller\console\admin\product\assistant;

use app\controller\console\admin\Controller;
use app\model\AssistantSite;

class SiteController extends Controller
{
    public function index()
    {
        $query = AssistantSite::order('id desc');

        $this->searchField($query, 'name');

        return $query->paginate();
    }

    public function plan($id)
    {
        $site = AssistantSite::findOrFail($id);

        $data = $this->validate([
            'plan'        => 'require',
            'expire_time' => 'require',
        ]);

        $site->save($data);
    }
}
