<?php

namespace app\controller\console\admin\product\write;

use app\controller\console\admin\Controller;
use app\model\WritePrivatization;

class PrivatizationController extends Controller
{
    public function index()
    {
        return WritePrivatization::paginate(10);
    }

    public function save()
    {
        $data = $this->validate([
            'type'          => 'require',
            'name'          => 'require|unique:' . WritePrivatization::class,
            'url'           => 'require',
            'client_id'     => 'require',
            'client_secret' => 'require',
            'expire_time'   => 'date',
            'remark'        => '',
        ]);

        WritePrivatization::create($data);
    }

    public function update($id)
    {
        $app  = WritePrivatization::findOrFail($id);
        $data = $this->validate([
            'type'          => 'require',
            'name'          => 'require|unique:' . WritePrivatization::class,
            'url'           => 'require',
            'client_id'     => 'require',
            'client_secret' => 'require',
            'expire_time'   => 'date',
            'remark'        => '',
        ]);
        $app->save($data);
    }
}
