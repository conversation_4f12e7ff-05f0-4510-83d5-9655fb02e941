<?php

namespace app\controller\console\admin\product\ssl;

use app\controller\console\admin\Controller;
use app\model\SslOrder;
use think\annotation\route\Get;
use think\db\Raw;

class StatsController extends Controller
{
    #[Get('product/ssl/stats')]
    public function index($period = '30days')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $dateField = get_date_query('create_time', $unit);

        $data = SslOrder::field(
            new Raw(<<<EOT
{$dateField} date,
sum(if(status>0,1,0)) total,
sum(if(status=3,agency_price,0)) cost,
sum(if(status>0,price,0)) revenue
EOT
            )
        )
            ->whereBetween('create_time', [$start, $end])
            ->group('date')
            ->order('date asc')
            ->select();

        return [
            'total'   => fill_data($list, $data, 'date', 'total', 'value'),
            'cost'    => fill_data($list, $data, 'date', 'cost', 'value'),
            'revenue' => fill_data($list, $data, 'date', 'revenue', 'value'),
        ];
    }
}
