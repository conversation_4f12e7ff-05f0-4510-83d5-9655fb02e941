<?php

namespace app\controller\console\admin\product\ssl;

use app\controller\console\admin\Controller;
use app\model\SslBrand;
use think\helper\Arr;

class BrandController extends Controller
{
    public function index()
    {
        return SslBrand::order('sort desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'name'        => 'require',
            'description' => 'require',
            'sort'        => '',
        ]);

        SslBrand::create($data);
    }

    public function update($id)
    {
        $brand = SslBrand::findOrFail($id);

        $data = $this->validate([
            'name'        => 'require',
            'description' => 'require',
            'sort'        => '',
        ]);

        $brand->save($data);
    }

    public function cert($id)
    {
        $brand = SslBrand::findOrFail($id);

        $data = $this->validate([
            'dv'        => '',
            'ov'        => '',
            'auth_type' => '',
        ]);

        if (!Arr::get($data['dv'], 'support', false)) {
            $data['dv'] = null;
        }

        if (!Arr::get($data['ov'], 'support', false)) {
            $data['ov'] = null;
        }

        $brand->save($data);
    }

    public function status($id)
    {
        $brand = SslBrand::findOrFail($id);

        $data = $this->validate([
            'status' => 'require|in:0,1',
        ]);

        $brand->save($data);
    }
}
