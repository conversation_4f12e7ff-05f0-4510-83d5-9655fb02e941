<?php

namespace app\controller\console\admin\product\ai;

use app\controller\console\admin\Controller;
use app\model\Ai;
use app\model\ai\Log;
use think\annotation\route\Get;
use think\db\Raw;

class UserController extends Controller
{
    #[Get('product/ai/user')]
    public function index()
    {
        $query = Ai::order('update_time desc')->with('user');

        $this->filterFields($query, [
            'user' => function ($query, $value) {
                $query->where('user_id', $value);
            },
        ]);

        return $query->paginate();
    }

    #[Get('product/ai/user/:id/stats')]
    public function stats($id, $period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $getTokens = function ($start, $end, $type) use ($id, $unit) {

            $dateField = get_ck_date_query('create_time', $unit);

            return Log::field(new Raw(<<<EOT
{$dateField} x,
sum(prompt+completion) y
EOT
            ))
                ->where('user_type', Log::TYPE_USER)
                ->where('user_id', $id)
                ->where('type', $type)
                ->whereBetween('create_time', [$start, $end])
                ->group('x')
                ->select();
        };

        $chat   = fill_data($list, $getTokens($start, $end, 'chat'), 'x', 'y');
        $image  = fill_data($list, $getTokens($start, $end, 'image'), 'x', 'y');
        $text   = fill_data($list, $getTokens($start, $end, 'text'), 'x', 'y');
        $audio  = fill_data($list, $getTokens($start, $end, 'audio'), 'x', 'y');
        $video  = fill_data($list, $getTokens($start, $end, 'video'), 'x', 'y');
        $plugin = fill_data($list, $getTokens($start, $end, 'plugin'), 'x', 'y');

        return [
            'chat'   => $chat,
            'image'  => $image,
            'text'   => $text,
            'audio'  => $audio,
            'video'  => $video,
            'plugin' => $plugin,
        ];
    }
}
