<?php

namespace app\controller\console\admin\product\ai;

use app\controller\console\admin\Controller;
use app\model\ai\Log;
use think\annotation\route\Get;
use think\db\BaseQuery;
use think\db\Raw;

class StatsController extends Controller
{
    #[Get('product/ai/stats/basic/:user')]
    public function user($user = 'user', $period = '24hours')
    {
        [$start, $end, $unit, $list] = get_period($period);

        $getTokens = function ($start, $end, $type) use ($user, $unit) {

            $dateField = get_ck_date_query('create_time', $unit);

            $query = Log::field(new Raw(<<<EOT
{$dateField} x,
sum(prompt+completion) y
EOT
            ));

            if ($user == 'user') {
                $query->where('user_type', Log::TYPE_USER);
            } else {
                $query->where('user_type', Log::TYPE_APPLICATION);
            }

            return $query->where('type', $type)
                ->whereBetween('create_time', [$start, $end])
                ->group('x')
                ->select();
        };

        $chat   = fill_data($list, $getTokens($start, $end, 'chat'), 'x', 'y');
        $image  = fill_data($list, $getTokens($start, $end, 'image'), 'x', 'y');
        $text   = fill_data($list, $getTokens($start, $end, 'text'), 'x', 'y');
        $audio  = fill_data($list, $getTokens($start, $end, 'audio'), 'x', 'y');
        $video  = fill_data($list, $getTokens($start, $end, 'video'), 'x', 'y');
        $plugin = fill_data($list, $getTokens($start, $end, 'plugin'), 'x', 'y');

        return [
            'chat'   => $chat,
            'image'  => $image,
            'text'   => $text,
            'audio'  => $audio,
            'video'  => $video,
            'plugin' => $plugin,
        ];
    }

    #[Get('product/ai/stats/metric/:type')]
    public function metric($type, $period = '24hours')
    {
        $types = [
            'app'   => [
                function (BaseQuery $query) {
                    $query->where('user_type', Log::TYPE_APPLICATION);
                },
                function (BaseQuery $query) {
                    $query->where('user_type', Log::TYPE_APPLICATION)
                        ->field(['user_type', 'user_id'])
                        ->group('user_type,user_id');
                },
                function (Log $item) {
                    return [
                        'x' => $item->getUsername(),
                        'y' => $item->total,
                    ];
                },
            ],
            'user'  => [
                function (BaseQuery $query) {
                    $query->where('user_type', Log::TYPE_USER);
                },
                function (BaseQuery $query) {
                    $query->where('user_type', Log::TYPE_USER)
                        ->field(['user_type', 'user_id'])
                        ->group('user_type,user_id');
                },
                function (Log $item) {
                    return [
                        'x'       => $item->getUsername(),
                        'y'       => $item->total,
                        'user_id' => $item->user_id,
                    ];
                },
            ],
            'model' => [
                function (BaseQuery $query) {
                    $query->where('type', 'chat');
                },
                function (BaseQuery $query) {
                    $query->where('type', 'chat')
                        ->field(['type', 'model'])
                        ->group('type,model');
                },
                function (Log $item) {
                    return [
                        'x' => $item->getModelName(),
                        'y' => $item->total,
                    ];
                },
            ],
        ];

        [$totalCallback, $dataCallback, $labelCallback] = $types[$type];

        [$start, $end] = get_period($period);

        $totalQuery = Log::whereBetween('create_time', [$start, $end]);

        $totalCallback($totalQuery);

        $total = $totalQuery->sum('prompt+completion');

        $dataQuery = Log::whereBetween('create_time', [$start, $end])
            ->field(new Raw('sum(prompt+completion) total'));

        $dataCallback($dataQuery);

        $data = $dataQuery->order('total desc')
            ->limit(10)
            ->select()
            ->map($labelCallback);

        return [
            'total' => $total,
            'data'  => $data,
        ];
    }

}
