<?php

namespace app\controller\console\admin\product\ai;

use app\controller\console\admin\Controller;
use app\model\ai\Model;
use think\annotation\route\Post;
use think\annotation\route\Resource;

#[Resource('product/ai/model')]
class ModelController extends Controller
{
    public function index($type)
    {
        return Model::where('type', $type)->order('channel desc,id desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'channel'     => 'require',
            'code'        => 'require',
            'label'       => 'require',
            'type'        => 'require',
            'description' => 'require',
            'factor'      => 'require',
            'version'     => '',
            'params'      => '',
            'sort'        => '',
            'status'      => '',
        ]);

        return Model::create($data);
    }

    public function update($id)
    {
        $model = Model::findOrFail($id);

        $data = $this->validate([
            'channel'     => 'require',
            'code'        => 'require',
            'label'       => 'require',
            'type'        => 'require',
            'description' => 'require',
            'factor'      => 'require',
            'version'     => '',
            'params'      => '',
            'sort'        => '',
        ]);

        return $model->save($data);
    }

    #[Post('product/ai/model/:id/status')]
    public function status($id)
    {
        $model = Model::findOrFail($id);

        $data = $this->validate([
            'status' => '',
        ]);

        return $model->save($data);
    }

    public function delete($id)
    {
        $model = Model::findOrFail($id);

        return $model->delete();
    }
}
