<?php

namespace app\controller\console\admin\product\ai;

use app\BaseController;
use app\lib\llm\plugin\OpenApi;
use app\model\ai\Plugin;
use rpc\contract\ai\Llm;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Resource;

#[Resource('product/ai/plugin')]
class PluginController extends BaseController
{
    public function index()
    {
        return Plugin::order('id desc')->paginate();
    }

    public function save()
    {
        $data = $this->validate([
            'title'       => 'require',
            'description' => 'require',
            'type'        => 'require',
            'icon'        => 'require',
            'sort'        => '',
        ]);

        return Plugin::create($data);
    }

    public function update($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'title'       => 'require',
            'description' => 'require',
            'type'        => 'require',
            'icon'        => 'require',
            'sort'        => '',
        ]);

        $plugin->save($data);
    }

    public function delete($id)
    {
        $plugin = Plugin::findOrFail($id);

        $plugin->delete();
    }

    #[Post('product/ai/plugin/:id/config')]
    public function config($id)
    {
        $plugin = Plugin::findOrFail($id);

        switch ($plugin->type) {
            case 'OpenApi':
                $data = $this->validate([
                    'schema' => 'require',
                    'auth'   => '',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
            case 'ThinkApi':
                $data = $this->validate([
                    'api' => 'require',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
            case 'Vision':
            case 'Artist':
                $data = $this->validate([
                    'models' => 'require',
                ]);
                $plugin->save([
                    'config' => $data,
                ]);
                break;
        }
    }

    #[Post('product/ai/plugin/:id/status')]
    public function status($id)
    {
        $plugin = Plugin::findOrFail($id);

        $data = $this->validate([
            'status' => '',
        ]);

        return $plugin->save($data);
    }

    #[Post('product/ai/plugin/parse')]
    public function parse()
    {
        $data = $this->validate([
            'schema' => '',
        ]);

        $plugin = new OpenApi($data['schema']);

        return $plugin->getTools();
    }

    #[Get('product/ai/plugin/types')]
    public function types(Llm $llm)
    {
        $types = $llm->getPluginTypes();
        return array_map(function ($type) {
            return ['label' => $type, 'value' => $type];
        }, $types);
    }
}
