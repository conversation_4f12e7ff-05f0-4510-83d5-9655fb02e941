<?php

namespace app\controller\console\admin\sales;

use app\controller\console\admin\Controller;
use app\model\Partner;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\db\Query;

class PartnerController extends Controller
{
    #[Get('sales/partner')]
    public function index()
    {
        $query = Partner::order('id desc')->with('user')->withCount(['wikis', 'bots', 'chats', 'ais']);

        $this->filterFields($query, [
            'user'    => function (Query $query, $value) {
                $query->where('user_id', $value);
            },
            'license' => function (Query $query, $value) {
                $query->when($value == 1, [['license', '>', 0]]);
                $query->when($value == -1, [['license', '=', 0]]);
            },
        ]);

        return $query->paginate();
    }

    #[Post('sales/partner/:id/license')]
    public function license($id)
    {
        $partner = Partner::findOrFail($id);
        $data    = $this->validate([
            'license|代理价' => 'require|integer|egt:0',
        ]);

        $partner->save([
            'license' => $data['license'],
        ]);
    }

    #[Get('sales/partner/:id/wikis')]
    public function wikis($id)
    {
        $partner = Partner::findOrFail($id);

        return $partner->wikis()->order('plan desc')->order('id desc')->paginate();
    }

    #[Get('sales/partner/:id/bots')]
    public function bots($id)
    {
        $partner = Partner::findOrFail($id);

        return $partner->bots()->order('plan desc')->order('id desc')->paginate();
    }

    #[Get('sales/partner/:id/chats')]
    public function chats($id)
    {
        $partner = Partner::findOrFail($id);

        return $partner->chats()->order('plan desc')->order('id desc')->paginate();
    }

    #[Get('sales/partner/:id/ais')]
    public function ais($id)
    {
        $partner = Partner::findOrFail($id);

        return $partner->ais()->order('plan desc')->order('id desc')->paginate();
    }

    #[Get('sales/partner/search')]
    public function search()
    {
        $query = Partner::hasWhere('user', function ($query) {
            if ($this->request->has('query')) {
                $query->whereLike('name|mobile', "%{$this->request->get('query')}%");
            }
        });

        return $query->limit(10)->select()->map(function (Partner $partner) {
            return [
                'label' => "{$partner->user->name}",
                'value' => $partner->id,
            ];
        });
    }
}
