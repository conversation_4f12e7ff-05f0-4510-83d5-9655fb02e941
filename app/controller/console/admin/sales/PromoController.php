<?php

namespace app\controller\console\admin\sales;

use app\controller\console\admin\Controller;
use app\enum\Product;
use app\model\Promo;
use think\annotation\route\Delete;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class PromoController extends Controller
{
    #[Get('sales/promo')]
    public function index()
    {
        return Promo::order('id desc')->withCount('charges')->with(['partner.user'])->paginate();
    }

    #[Post('sales/promo')]
    public function save()
    {
        $data = $this->validate([
            'title|名称'          => 'require',
            'code|代码'           => 'require|alphaDash',
            'product|适用产品'    => 'require',
            'discount|折扣'       => 'require|integer',
            'partner_id|合作伙伴' => '',
            'rebate|佣金比例'     => '',
            'expire_time|有效期'  => '',
        ]);

        Promo::create($data);
    }

    #[Put('sales/promo/:id')]
    public function update($id)
    {
        $promo = Promo::findOrFail($id);
        $data  = $this->validate([
            'title|名称'          => 'require',
            'code|代码'           => 'require|alphaDash',
            'product|适用产品'    => 'require',
            'discount|折扣'       => 'require|integer',
            'partner_id|合作伙伴' => '',
            'rebate|佣金比例'     => '',
            'expire_time|有效期'  => '',
        ]);

        $promo->save($data);
    }

    #[Delete('sales/promo/:id')]
    public function delete($id)
    {
        $promo = Promo::findOrFail($id);
        $promo->delete();
    }

    #[Get('sales/promo/:id/charges')]
    public function charges($id)
    {
        $promo = Promo::findOrFail($id);
        return $promo->charges()->order('id desc')->paginate();
    }

    #[Get('sales/promo/products')]
    public function products()
    {
        return array_map(fn($item) => ['value' => $item->value, 'label' => $item->label()], Product::cases());
    }
}
