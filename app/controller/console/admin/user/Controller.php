<?php

namespace app\controller\console\admin\user;

use app\model\User;
use app\Request;

class Controller extends \app\controller\console\admin\Controller
{
    /** @var User */
    protected $target;

    protected function initialize()
    {
        parent::initialize();
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('user');
            if ($id) {
                $this->target = User::where('id', $id)->findOrFail();
            }
            return $next($request);
        });
    }
}
