<?php

namespace app\controller\console\admin\user;

use app\model\Notification;
use app\model\User;
use app\model\UserTagMap;

class IndexController extends Controller
{
    public function index()
    {
        $query = User::order('create_time desc');

        $this->filterFields($query, [
            'id'  => true,
            'name',
            'email',
            'mobile',
            'tag' => function ($query, $value) {
                $ids = UserTagMap::where('tag_id', $value)->column('user_id');
                $query->whereIn('id', $ids);
            },
        ]);

        return $query->paginate()->visible(['money', 'coin'], true);
    }

    public function read()
    {
        return $this->target->visible(['reg_ip', 'last_ip', 'last_time'], true);
    }

    public function features()
    {
        $data = $this->validate([
            'assistant' => 'require|boolean',
        ]);

        $features = $this->target->features;

        if ($data['assistant']) {
            $features = $features | User::ASSISTANT_FEATURE;
        } else {
            $features = $features & (~User::ASSISTANT_FEATURE);
        }

        $this->target->save([
            'features' => $features,
        ]);
    }

    public function tags()
    {
        if ($this->request->isGet()) {
            return $this->target->tags;
        } else {
            $data = $this->validate([
                'tags' => 'array',
            ]);

            $this->target->tags()->sync($data['tags']);
        }
    }

    public function notify()
    {
        $data = $this->validate([
            'content|通知内容' => 'require',
        ]);

        Notification::notify([
            'to_id'   => $this->target->id,
            'content' => $data['content'],
        ]);
    }

    public function search()
    {
        $query = User::order('id desc');

        if ($this->request->has('query')) {
            $query->whereLike('name|mobile', "%{$this->request->get('query')}%");
        }

        return $query->limit(10)->select()->map(function (User $user) {
            return [
                'label' => "{$user->name}[{$user->mobile}]",
                'value' => $user->id,
            ];
        });
    }
}
