<?php

namespace app\controller\console\admin\user;

class MoneyController extends Controller
{
    public function logs()
    {
        return $this->target->moneyLogs()->order('id desc')->paginate(10);
    }

    public function sync()
    {
        $this->target->syncMoney();
    }

    public function save()
    {
        $data = $this->validate([
            'type|类型'     => 'require|in:1,2',
            'number|金额'   => 'require|gt:0',
            'info|操作原因' => 'require',
        ]);

        $this->target->updateMoney($data['type'], $data['number'] * 100, "管理员#{$this->user->id}：{$data['info']}");
    }
}
