<?php

namespace app\controller\console\admin\user;

use app\model\UserTag;
use think\annotation\route\Resource;

#[Resource('user/tag')]
class TagController extends \app\controller\console\admin\Controller
{
    public function index()
    {
        return UserTag::order('id desc')->select();
    }

    public function save()
    {
        $data = $this->validate([
            'name|名称' => 'require',
        ]);

        UserTag::create($data);
    }

    public function update($id)
    {
        $tag  = UserTag::findOrFail($id);
        $data = $this->validate([
            'name|名称' => 'require',
        ]);

        $tag->save($data);
    }

    public function delete($id)
    {
        $tag = UserTag::findOrFail($id);

        $tag->delete();
    }
}
