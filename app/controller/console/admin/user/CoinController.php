<?php

namespace app\controller\console\admin\user;

class CoinController extends Controller
{
    public function logs()
    {
        return $this->target->coinLogs()->order('id desc')->paginate(10);
    }

    public function sync()
    {
        $this->target->syncCoin();
    }

    public function save()
    {
        $data = $this->validate([
            'type|类型'     => 'require|in:1,2',
            'number|数量'   => 'require|gt:0',
            'info|操作原因' => 'require',
        ]);

        $this->target->updateCoin($data['type'], $data['number'] * 100, "管理员#{$this->user->id}：{$data['info']}");
    }
}
