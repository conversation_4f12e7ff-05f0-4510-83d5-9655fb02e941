<?php

namespace app\controller\console\admin\approval;

use app\controller\console\admin\Controller;
use app\model\PartnerSettlement;
use app\notification\Message;
use yunwuxin\facade\Notification;

class SettlementController extends Controller
{
    public function index()
    {
        return PartnerSettlement::with(['partner.user'])->where('status', 0)->order('id desc')->paginate();
    }

    public function confirm($id)
    {
        $settlement = PartnerSettlement::findOrFail($id);

        $settlement->confirm();

        Notification::send($settlement->partner->user_id, new Message('您提交的佣金结算申请已审核通过。'));
    }

    public function cancel($id)
    {
        $settlement = PartnerSettlement::findOrFail($id);

        $data = $this->validate([
            'remark|取消原因' => 'require',
        ]);

        $settlement->cancel($data['remark']);

        Notification::send($settlement->partner->user_id, new Message('您提交的佣金结算申请审核未通过。'));
    }
}
