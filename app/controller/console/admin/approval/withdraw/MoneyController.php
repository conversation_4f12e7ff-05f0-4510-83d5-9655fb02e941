<?php

namespace app\controller\console\admin\approval\withdraw;

use app\controller\console\admin\Controller;
use app\model\MoneyWithdraw;
use app\notification\Message;
use yunwuxin\facade\Notification;

class MoneyController extends Controller
{
    public function index()
    {
        return MoneyWithdraw::order('id desc')->with(['user'])->where('status', 0)->paginate();
    }

    public function confirm($id)
    {
        $withdraw = MoneyWithdraw::findOrFail($id);

        $withdraw->confirm();

        Notification::send($withdraw->user_id, new Message("您提交的余额提现申请已审核通过。"));
    }

    public function cancel($id)
    {
        $withdraw = MoneyWithdraw::findOrFail($id);

        $data = $this->validate([
            'remark|取消原因' => 'require',
        ]);

        $withdraw->cancel($data['remark']);

        Notification::send($withdraw->user_id, new Message("您提交的余额提现申请审核未通过。"));
    }
}
