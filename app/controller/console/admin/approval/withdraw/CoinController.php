<?php

namespace app\controller\console\admin\approval\withdraw;

use app\controller\console\admin\Controller;
use app\model\CoinWithdraw;
use app\notification\Message;
use yunwuxin\facade\Notification;

class CoinController extends Controller
{
    public function index()
    {
        return CoinWithdraw::order('create_time desc')->with(['user'])->where('status', 0)->paginate();
    }

    public function confirm($id)
    {
        $withdraw = CoinWithdraw::findOrFail($id);

        $withdraw->confirm();

        Notification::send($withdraw->user_id, new Message("您提交的云币提现申请已审核通过。"));
    }

    public function cancel($id)
    {
        $withdraw = CoinWithdraw::findOrFail($id);

        $data = $this->validate([
            'remark|取消原因' => 'require',
        ]);

        $withdraw->cancel($data['remark']);

        Notification::send($withdraw->user_id, new Message("您提交的云币提现申请审核未通过。"));
    }
}
