<?php

namespace app\controller\console\admin\approval;

use app\controller\console\admin\Controller;
use app\model\Enterprise;
use app\notification\Message;
use yunwuxin\facade\Notification;

class EnterpriseController extends Controller
{
    public function index()
    {
        return Enterprise::with(['user'])
            ->where('status', 0)->order('create_time desc')->paginate();
    }

    public function pass($id)
    {
        $enterprise = Enterprise::where('status', 0)->findOrFail($id);

        $enterprise->save(['status' => 1]);

        Notification::send($enterprise->user_id, new Message("您提交的企业认证已审核通过。"));
    }

    public function reject($id)
    {
        $enterprise = Enterprise::where('status', 0)->findOrFail($id);

        $enterprise->save(['status' => -1]);

        Notification::send($enterprise->user_id, new Message("您提交的企业认证审核未通过。"));
    }
}
