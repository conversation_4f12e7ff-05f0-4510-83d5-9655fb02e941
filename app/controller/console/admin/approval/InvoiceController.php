<?php

namespace app\controller\console\admin\approval;

use app\controller\console\admin\Controller;
use app\model\Invoice;
use app\notification\Message;
use think\Db;
use yunwuxin\facade\Notification;

class InvoiceController extends Controller
{
    public function index()
    {
        return Invoice::with(['user'])
            ->where('status', 0)->order('create_time desc')->paginate();
    }

    public function pass($id)
    {
        $invoice = Invoice::where('status', 0)->findOrFail($id);

        $data = $this->validate([
            'file|发票文件' => 'require',
        ]);

        $invoice->save([
            'status' => 1,
            'remark' => $data['file'],
        ]);

        Notification::send($invoice->user_id, new Message("您提交的发票申请已审核通过。"));
    }

    public function reject(Db $db, $id)
    {
        $invoice = Invoice::where('status', 0)->findOrFail($id);

        $data = $this->validate([
            'remark|驳回原因' => 'require',
        ]);

        $db->transaction(function () use ($data, $invoice) {
            $invoice->save([
                'status' => -1,
                'remark' => $data['remark'],
            ]);

            $invoice->charges->update([
                'invoice_id' => 0,
            ]);
        });

        Notification::send($invoice->user_id, new Message("您提交的发票申请审核未通过。"));
    }
}
