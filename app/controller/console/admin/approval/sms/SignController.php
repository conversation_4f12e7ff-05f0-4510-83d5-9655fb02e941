<?php

namespace app\controller\console\admin\approval\sms;

use app\controller\console\admin\Controller;
use app\model\SmsSign;
use think\annotation\route\Get;
use think\annotation\route\Post;

class SignController extends Controller
{

    #[Get('approval/sms/sign')]
    public function index()
    {
        return SmsSign::with(['user'])
            ->whereIn('status', [0, 3])->order('create_time desc')->paginate();
    }

    #[Post('approval/sms/sign/:id/submit')]
    public function submit($id)
    {
        $sign = SmsSign::where('status', 0)->findOrFail($id);
        $sign->submit();
    }

    #[Post('approval/sms/sign/:id/pass')]
    public function pass($id)
    {
        $sign = SmsSign::whereIn('status', [0, 3])->findOrFail($id);

        $sign->pass();
    }

    #[Post('approval/sms/sign/:id/reject')]
    public function reject($id)
    {
        $sign = SmsSign::whereIn('status', [0, 3])->findOrFail($id);

        $data = $this->validate(['error|原因' => 'require']);

        $sign->reject($data['error']);
    }
}
