<?php

namespace app\controller\console;

use app\lib\provider\Console;
use app\model\User;
use app\Request;
use Exception;
use yunwuxin\auth\exception\AuthenticationException;
use yunwuxin\oauth\server\ResourceServer;

class LoginController extends Controller
{
    public function index(Request $request)
    {
        $params = [
            'client_id'     => env('CLOUD_CLIENT_ID', ''),
            'response_type' => 'token',
            'redirect_uri'  => $request->param('redirect_uri'),
        ];

        return json(['url' => (string) main_url('/oauth/authorize', $params)]);
    }

    public function token(ResourceServer $server, Request $request)
    {
        try {
            $req = $server->validateAuthenticatedRequest($request);

            $userId = $req->getAttribute('oauth_user_id');

            $user = User::find($userId);
            if ($user) {
                return json([
                    'token' => Console::createToken($user),
                ]);
            }
        } catch (Exception) {

        }
        throw new AuthenticationException();
    }
}
