<?php

namespace app\controller;

use app\BaseController;
use app\lib\MiniAuth;
use app\lib\Otp;
use app\lib\WithLoginCaptcha;
use app\model\Application;
use app\model\User;
use think\Event;
use yunwuxin\auth\event\Login;
use yunwuxin\oauth\server\AuthorizationServer;
use yunwuxin\oauth\server\exception\OAuthServerException;

class PassportController extends BaseController
{
    use WithLoginCaptcha;

    public function js()
    {
        $file = root_path('asset/passport/dist') . 'passport.min.js';

        return \think\swoole\helper\file($file);
    }

    public function login(Otp $otp)
    {
        $data = $this->validate([
            'mobile|手机号' => 'require|mobile',
            'code|验证码'   => [
                'require',
                '\d{6}',
                function ($code, $data) use ($otp) {
                    if ($otp->verify($data['mobile'], $code, 'login-code')) {
                        return true;
                    }

                    return '验证码不正确';
                },
            ],
            'social'        => '',
        ]);

        $user = User::retrieveByMobile($data['mobile'], $this->request->ip());

        return $this->logined($user);
    }

    public function mini(MiniAuth $mini, Event $event, $token)
    {
        $user = $mini->user($token);

        if (empty($user)) {
            abort(449);
        }

        $event->trigger(new Login($user, false));

        return $this->logined($user);
    }

    protected function logined(User $user)
    {
        $server = $this->app->make(AuthorizationServer::class);

        try {
            $authRequest = $server->validateAuthorizationRequest($this->request);

            $client = $authRequest->getClient();

            $app = Application::getByClientId($client->getIdentifier());

            $user->applications()->attach($app);

            $authRequest->setUser($user->id);

            $authRequest->setAuthorizationApproved(true);
            // 完成后重定向至客户端请求重定向地址
            return $server->completeAuthorizationRequest($authRequest);
        } catch (OAuthServerException $e) {
            return $e->generateHttpResponse();
        }
    }

}
