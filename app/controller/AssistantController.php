<?php

namespace app\controller;

use app\BaseController;
use app\lib\Date;
use app\lib\Detect;
use app\lib\Hashids;
use app\lib\Producer;
use app\model\AssistantSite;
use app\model\AssistantSiteEvent;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use League\Uri\Uri;
use Ramsey\Uuid\Uuid;
use think\Filesystem;

class AssistantController extends BaseController
{

    public function js($filename = 'assistant.js')
    {
        $file = root_path('asset/assistant/dist') . $filename;

        if (!is_file($file)) {
            abort(404);
        }

        $response = \think\swoole\helper\file($file);

        if ($filename !== 'assistant.js') {
            //除引导页外，其余文件缓存一年
            $response->header([
                'Cache-Control' => 'max-age=315360000',
            ]);
        }

        return $response;
    }

    protected function resolveSite($id): AssistantSite
    {
        $id = Hashids::decode($id);

        return AssistantSite::where('id', $id)->cache(3600)->findOrFail();
    }

    public function config($id)
    {
        $site = $this->resolveSite($id);

        if ($site->getData('status') == 0) {
            //标记SDK已安装
            $site->save(['status' => 1]);
        }

        $ip = $this->request->ip();

        $sessionId = Uuid::uuid5(Uuid::NAMESPACE_DNS, "{$site->id}#{$ip}#{$this->request->param('client_id')}");

        $config = $site->features;

        $config['appearance'] = $site->appearance;
        $config['customs']    = $site->customs;
        $config['broadcast']  = ['enable' => false];
        $config['scripts']    = '';
        $config['feedback']   = null;
        $config['robot']      = ['enable' => false];

        if ($site->isStandard()) {
            //标准版
            $config['broadcast'] = $site->broadcast;
            $config['scripts']   = $site->scripts;
            $config['feedback']  = $site->feedbacks()->where('status', 1)->order('create_time', 'desc')->find();
        }

        return [
            'config'    => $config,
            'sessionId' => (string) $sessionId,
        ];
    }

    public function feedback(Filesystem $filesystem, $id)
    {
        $site = $this->resolveSite($id);

        $data = $this->validate([
            'sessionId'  => 'require',
            'feedback'   => 'require|integer',
            'star'       => 'require|in:1,2,3,4,5',
            'reason'     => '',
            'screenshot' => 'image',
        ]);

        $feedback = $site->feedbacks()->where('status', 1)->findOrFail($data['feedback']);

        if (!empty($data['screenshot'])) {
            $disk = $filesystem->disk('uploads');

            $path = $disk->putFile('assistant', $data['screenshot']);

            $data['screenshot'] = (string) main_url($disk->url($path));
        }

        $info = Detect::getClientInfo($this->request->header('user-agent'));

        $feedback->entries()->save([
            'session_id' => $data['sessionId'],
            'star'       => $data['star'],
            'reason'     => $data['reason'] ?? '',
            'screenshot' => $data['screenshot'] ?? '',
            'os'         => "{$info['os']} {$info['os_version']}",
            'browser'    => "{$info['browser']} {$info['browser_version']}",
        ]);
    }

    public function send(Producer $producer, $id)
    {
        $type    = $this->request->param('type');
        $payload = $this->request->param('payload', '');

        if ($type !== 'event') {
            abort(400, 'Wrong payload type.');
        }

        $payload = json_decode($payload, true);

        // Validate eventData is JSON
        if (!empty($payload['data']) && !is_array($payload['data'])) {
            abort(400, 'Invalid event data.');
        }

        $info = Detect::getClientInfo($this->request->header('user-agent'));

        // Ignore robots
        if ($info['device'] === 'robot') {
            return;
        }

        try {
            $cacheToken = $this->request->param('cache');

            if ($cacheToken) {
                $decoded = (array) JWT::decode($cacheToken, new Key(config('app.token'), 'HS256'));
                $session = (array) $decoded['session'];
            }
        } catch (Exception) {
        }

        if (empty($session)) {
            $site = $this->resolveSite($id);

            $ip = $this->request->ip();

            $location = Detect::getLocation($ip);

            $session = [
                'site_id'       => $site->id,
                'session_id'    => $payload['sessionId'],
                'browser'       => $info['browser'],
                'os'            => $info['os'],
                'device'        => $info['device'],
                'screen'        => $payload['screen'],
                'language'      => $payload['language'],
                'country'       => $location['country'] ?? '',
                'country_code'  => $location['country_code'] ?? '',
                'province'      => $location['province'] ?? '',
                'province_code' => $location['province_code'] ?? '',
                'city'          => $location['city'] ?? '',
            ];
        }

        $url = Uri::createFromString($payload['url']);

        $urlPath   = $url->getPath();
        $urlDomain = $url->getHost();

        if ($payload['referrer']) {
            $refUrl = Uri::createFromString($payload['referrer']);

            $referrerPath   = $refUrl->getPath();
            $referrerDomain = $refUrl->getHost();
        }

        $message = array_merge($session, [
            'event_id'        => Uuid::uuid4(),
            'event_type'      => isset($payload['name']) ? AssistantSiteEvent::CUSTOM : AssistantSiteEvent::PAGEVIEW,
            'event_name'      => $payload['name'] ?? '',
            'url'             => $payload['url'],
            'url_path'        => $urlPath,
            'url_domain'      => $urlDomain,
            'referrer'        => $payload['referrer'] ?: '',
            'referrer_path'   => $referrerPath ?? '',
            'referrer_domain' => $referrerDomain ?? '',
            'page_title'      => $payload['title'],
            'create_time'     => Date::now(),
        ]);

        $producer->send('assistant_site_event', json_encode($message));

        return JWT::encode([
            'exp'     => time() + 1800,
            'iat'     => time(),
            'session' => $session,
        ], config('app.token'), 'HS256');
    }

}
