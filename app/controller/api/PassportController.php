<?php

namespace app\controller\api;

use app\lib\MiniAuth;

class PassportController extends ApiController
{
    public function login(MiniAuth $mini)
    {
        $data = $this->validate([
            'name' => 'require',
        ]);

        $res = $mini->create('passport', $data);

        return json($res);
    }

    public function user(MiniAuth $mini)
    {
        $data = $this->validate([
            'token' => 'require',
        ]);

        $user = $mini->user($data['token']);

        if (!empty($user)) {
            return json($user);
        }
    }
}
