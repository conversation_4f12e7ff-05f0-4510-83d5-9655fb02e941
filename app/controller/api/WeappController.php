<?php

namespace app\controller\api;

use app\job\ReleaseWeappJob;
use app\lib\Date;
use app\lib\wechat\wxa\Application;
use app\model\WxaWeapp;
use think\exception\ValidateException;
use think\Filesystem;
use think\helper\Arr;

class WeappController extends ApiController
{
    public function read($id)
    {
        $weapp = WxaWeapp::where('application_id', $this->application->id)->findOrFail($id);

        if ($weapp->status == 0 && $weapp->create_time->lt(Date::yesterday())) {
            $weapp->save(['status' => -1]);
        }

        return $weapp;
    }

    public function save(Application $application)
    {
        $data = $this->validate([
            'name'                 => 'require',
            'code_type'            => 'require',
            'code'                 => 'require',
            'legal_persona_wechat' => 'require',
            'legal_persona_name'   => 'require',
            'ext'                  => '',
            'domain'               => 'require',
            'template_id'          => 'require',
        ]);

        return WxaWeapp::transaction(function () use ($data, $application) {
            $identity = md5($data['name'] . $data['code'] . $data['legal_persona_wechat'] . $data['legal_persona_name']);

            $weapp = WxaWeapp::create([
                'application_id' => $this->application->id,
                'identity'       => $identity,
                'domain'         => $data['domain'],
                'ext'            => $data['ext'],
                'template_id'    => $data['template_id'],
                'status'         => 0,
            ]);

            $result = $application->getClient()->request('post', '/cgi-bin/component/fastregisterweapp', [
                'query' => [
                    'action' => 'create',
                ],
                'json'  => [
                    'name'                 => $data['name'],
                    'code_type'            => $data['code_type'],
                    'code'                 => $data['code'],
                    'legal_persona_wechat' => $data['legal_persona_wechat'],
                    'legal_persona_name'   => $data['legal_persona_name'],
                    'component_phone'      => '021-61398032',
                ],
            ]);

            if (Arr::get($result, 'errcode') != 0) {
                throw new ValidateException(Arr::get($result, 'errmsg'));
            }

            return $weapp;
        });
    }

    public function update(Application $application, $id)
    {
        $weapp = WxaWeapp::where('application_id', $this->application->id)->where('status', 3)->findOrFail($id);

        $data = $this->validate([
            'name'      => 'require',
            'signature' => 'require',
            'avatar'    => 'require',
        ]);

        $app = $application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

        if (empty($weapp->nickname)) {
            //检查名称是否有效
            $result = $app->getClient()->post('/cgi-bin/wxverify/checkwxverifynickname', [
                'json' => ['nick_name' => $data['name']],
            ]);

            if (Arr::get($result, 'errcode') != 0 || Arr::get($result, 'hit_condition')) {
                throw new ValidateException('小程序名称不合规或已被占用');
            }

            $weapp->transaction(function () use ($weapp, $data, $app) {
                $weapp->save(['nickname' => $data['name']]);
                //设置名称
                $result = $app->getClient()->post('/wxa/setnickname', [
                    'json' => ['nick_name' => $data['name']],
                ]);

                if (Arr::get($result, 'errcode') != 0) {
                    throw new ValidateException('小程序名称不合规或已被占用');
                }
            });
        }

        if (empty($weapp->signature)) {
            $weapp->transaction(function () use ($weapp, $data, $app) {
                $weapp->save(['signature' => $data['signature']]);

                $result = $app->getClient()->post('/cgi-bin/account/modifysignature', [
                    'json' => ['signature' => $data['signature']],
                ]);

                if (Arr::get($result, 'errcode') != 0) {
                    throw new ValidateException('小程序介绍设置失败');
                }
            });
        }

        if (empty($weapp->avatar)) {
            $weapp->transaction(function () use ($weapp, $data, $app) {

                $result = $app->getClient()->post('/cgi-bin/media/upload', [
                    'query'     => [
                        'type' => 'image',
                    ],
                    'multipart' => [
                        [
                            'name'     => 'media',
                            'contents' => base64_decode($data['avatar']),
                            'filename' => 'avatar.png',
                        ],
                    ],
                ]);

                $mediaId = Arr::get($result, 'media_id');

                $result = $app->getClient()->post('/cgi-bin/account/modifyheadimage', [
                    'json' => [
                        'head_img_media_id' => $mediaId,
                        "x1"                => "0",
                        "y1"                => "0",
                        "x2"                => "1",
                        "y2"                => "1",
                    ],
                ]);

                if (Arr::get($result, 'errcode') != 0) {
                    throw new ValidateException('头像设置失败');
                }

                //获取最新头像
                $result = $app->getClient()->post('/cgi-bin/account/getaccountbasicinfo');

                $weapp->save(['avatar' => Arr::get($result, 'head_image_info.head_image_url')]);
            });
        }

        $weapp->save(['status' => 1]);

        //发布小程序
        queue(ReleaseWeappJob::class, $weapp->id, queue: 'retry');
    }

    public function qrcode(Application $application, Filesystem $filesystem, $id, $page = '', $scene = 'default')
    {
        $weapp = WxaWeapp::where('application_id', $this->application->id)->where('status', 1)->findOrFail($id);
        $app   = $application->getMiniAppWithRefreshToken($weapp->app_id, $weapp->refresh_token);

        $disk = $filesystem->disk('uploads');

        $filename = 'qrcode/' . md5("weapp#{$weapp->id}#{$page}#$scene") . '.jpg';

        if (!$disk->has($filename)) {
            $data = [
                'scene' => $scene,
            ];

            if ($page) {
                $data['page'] = $page;
            }

            $result = $app->getClient()->request('post', '/wxa/getwxacodeunlimit', ['json' => $data]);
            if (!is_string($result)) {
                throw new ValidateException('获取失败');
            }
            $disk->put($filename, $result);
        }

        $url = $disk->url($filename);

        return json(['url' => (string) main_url($url)]);
    }
}
