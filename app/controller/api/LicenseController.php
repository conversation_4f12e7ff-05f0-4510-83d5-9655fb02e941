<?php

namespace app\controller\api;

use app\model\AccessToken;
use app\model\LicenseAi;
use app\model\LicenseBot;
use app\model\LicenseChat;
use app\model\LicenseWiki;

class LicenseController extends ApiController
{
    public function verify()
    {
        $data = $this->validate([
            'type'  => 'require',
            'token' => 'require',
        ]);

        //上报ip
        $ip = $this->request->ip();

        switch ($data['type']) {
            case 'ai':
                $token = AccessToken::getByToken($data['token'], LicenseAi::class);
                if (empty($token)) {
                    abort(422, '验证未通过');
                }
                break;
            case 'chat':
                $token = AccessToken::getByToken($data['token'], LicenseChat::class);
                if (empty($token)) {
                    abort(422, '验证未通过');
                }
                break;
            case 'bot':
                $token = AccessToken::getByToken($data['token'], LicenseBot::class);
                if (empty($token)) {
                    abort(422, '验证未通过');
                }
                break;
            case 'wiki':
                $token = AccessToken::getByToken($data['token'], LicenseWiki::class);
                if (empty($token)) {
                    abort(422, '验证未通过');
                }
                break;
            default:
                abort(422, '验证未通过');
        }

        return $token->accessible;
    }

    public function read($type, $id)
    {
        switch ($type) {
            case 'ai':
                $license = LicenseAi::findOrFail($id);
                break;
            case 'chat':
                $license = LicenseChat::findOrFail($id);
                break;
            case 'bot':
                $license = LicenseBot::findOrFail($id);
                break;
            case 'wiki':
                $license = LicenseWiki::findOrFail($id);
                break;
            default:
                abort(404);
        }

        return $license->append(['code']);
    }
}
