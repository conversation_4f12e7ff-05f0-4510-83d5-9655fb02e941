<?php

namespace app\controller\api\me;

use app\lib\Date;
use app\model\SslCertificate;

class CertController extends Controller
{
    public function index()
    {
        $result = SslCertificate::where('user_id', $this->user->id)
            ->hidden(['key', 'certificate'])
            ->where('status', 1)
            ->whereNotNull('key')
            ->where('expire_time', '>', Date::now())
            ->select();

        if ($this->request->has('domain')) {
            $domain = $this->request->param('domain');
            $result = $result->filter(function (SslCertificate $certificate) use ($domain) {
                return $certificate->getInfo()->appliesToUrl($domain);
            })->values();
        }

        return $result;
    }

    public function read($id)
    {
        return SslCertificate::where('user_id', $this->user->id)->where('status', 1)->findOrFail($id);
    }
}
