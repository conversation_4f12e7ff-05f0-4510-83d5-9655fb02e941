<?php

namespace app\controller\api\me;

class IndexController extends Controller
{
    public function index()
    {
        $user = $this->user->append(['is_certified', 'is_enterprise', 'is_admin', 'setting_url', 'ssl_url', 'finance_url'])
            ->hidden(['certification', 'enterprise']);

        return json($user);
    }

    public function logout()
    {
        $this->user->resetToken();
    }
}
