<?php

namespace app\controller\api;

use app\BaseController;
use app\model\Application;
use think\App;
use think\View;
use yunwuxin\Auth;

abstract class ApiController extends BaseController
{
    /** @var Application */
    protected $application;

    public function __construct(App $app, Auth $auth, View $view)
    {
        parent::__construct($app, $auth, $view);
        $accessToken = $this->request->getAccessToken();
        if ($accessToken) {
            $this->application = $accessToken->application;
        }
    }
}
