<?php

namespace app\controller\api;

use app\model\Notification;

class NotificationController extends ApiController
{
    public function save()
    {
        $data = $this->validate([
            'to_id'   => 'require',
            'from_id' => '',
            'content' => 'require',
            'url'     => '',
            'message' => '',
            'sms'     => '',
            'channel' => '',
            'app'     => '',
        ]);

        Notification::notify($data, $this->application);
    }

    public function token()
    {
        $data = $this->validate([
            'user_id' => 'require',
        ]);

        $token = \app\lib\provider\Notification::createToken($data['user_id'], $this->application);

        return [
            'token' => $token,
        ];
    }
}
