<?php

namespace app\controller\api\user;

use app\model\CoinLog;

class CoinController extends Controller
{

    public function inc()
    {
        $data = $this->validate([
            'number' => 'require|number',
            'info'   => 'require',
        ]);

        $this->user->updateCoin(CoinLog::TYPE_INC, $data['number'], $data['info'], $this->application);
    }

    public function dec()
    {
        $data = $this->validate([
            'number' => 'require|number',
            'info'   => 'require',
        ]);

        $this->user->updateCoin(CoinLog::TYPE_DEC, $data['number'], $data['info'], $this->application);
    }
}
