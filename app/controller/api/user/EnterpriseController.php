<?php

namespace app\controller\api\user;

class EnterpriseController extends Controller
{
    public function save()
    {
        $data = $this->validate([
            'corporation|企业法人'             => 'require',
            'name|公司名称'                    => 'require',
            'identity|注册号/统一社会信用代码' => 'require',
            'img|营业执照'                     => 'require',
            'status'                           => '',
        ]);

        $this->user->enterprise()->save($data, false);
    }

    public function read()
    {
        if ($this->user->isEnterprise()) {
            return $this->user->enterprise->visible(['corporation', 'name', 'identity', 'img', 'status']);
        }
        abort(404);
    }
}
