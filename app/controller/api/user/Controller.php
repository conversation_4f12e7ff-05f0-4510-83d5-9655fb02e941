<?php

namespace app\controller\api\user;

use app\controller\api\ApiController;
use app\model\User;
use app\Request;

abstract class Controller extends ApiController
{
    protected function initialize()
    {
        $this->middleware(function (Request $request, $next) {
            $this->user = User::where('id', $request->route('user'))->findOrFail();
            return $next($request);
        });
    }
}
