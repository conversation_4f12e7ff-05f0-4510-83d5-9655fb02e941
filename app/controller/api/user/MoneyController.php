<?php

namespace app\controller\api\user;

use app\model\MoneyLog;

class MoneyController extends Controller
{
    public function inc()
    {
        $data = $this->validate([
            'number' => 'require|number',
            'info'   => 'require',
        ]);

        $this->user->updateMoney(MoneyLog::TYPE_INC, $data['number'], $data['info'], $this->application);
    }

    public function dec()
    {
        $data = $this->validate([
            'number' => 'require|number',
            'info'   => 'require',
        ]);

        $this->user->updateMoney(MoneyLog::TYPE_DEC, $data['number'], $data['info'], $this->application);
    }
}
