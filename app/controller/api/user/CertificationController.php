<?php

namespace app\controller\api\user;

class CertificationController extends Controller
{
    public function save()
    {
        $data = $this->validate([
            'name|真实姓名'     => 'require',
            'identity|身份证号' => [
                'require',
                '(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)',
            ],
            'certify_id'        => '',
            'passed'            => '',
        ]);

        $this->user->certification()->save($data, false);
    }

    public function read()
    {
        if ($this->user->isCertified()) {
            return $this->user->certification->visible(['name', 'identity', 'passed']);
        }
        abort(404);
    }

}
