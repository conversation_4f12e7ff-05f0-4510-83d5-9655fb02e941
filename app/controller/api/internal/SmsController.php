<?php

namespace app\controller\api\internal;

use app\controller\api\ApiController;
use app\facade\Sms;
use app\lib\Hashids;
use app\model\SmsSign;
use app\model\SmsTemplate;

class SmsController extends ApiController
{
    public function sign()
    {
        $data = $this->validate([
            'name|签名名称'   => 'require|length:2,16',
            'source|签名来源' => 'require',
            'remark|场景说明' => 'require',
            'user_id'         => 'require',
            'status'          => '',
        ]);

        //检查签名是否存在
        $sign = SmsSign::where('name', $data['name'])->where('user_id', $data['user_id'])->find();

        if ($sign) {
            return $sign;
        }

        return SmsSign::create($data);
    }

    public function template()
    {
        $data = $this->validate([
            'sign_id|关联签名' => 'require',
            'name|模板名称'    => 'require',
            'content|模板内容' => 'require',
            'remark|场景说明'  => 'require',
            'user_id'          => 'require',
        ]);

        $sign = SmsSign::findOrFail(Hashids::decode($data['sign_id']));

        //检查模板是否存在
        $template = SmsTemplate::where('name', $data['name'])
            ->where('content', $data['content'])
            ->where('sign_id', $sign->id)
            ->where('user_id', $data['user_id'])
            ->find();

        if ($template) {
            return $template;
        }

        //添加模板
        $outId = Sms::addTemplate($data['name'], $sign->name, $data['content']);

        return SmsTemplate::create([
            'name'    => $data['name'],
            'content' => $data['content'],
            'remark'  => $data['remark'],
            'sign_id' => $sign->id,
            'user_id' => $data['user_id'],
            'out_id'  => $outId,
        ]);
    }
}
