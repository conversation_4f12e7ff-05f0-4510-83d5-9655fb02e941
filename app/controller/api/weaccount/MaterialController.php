<?php

namespace app\controller\api\weaccount;

use think\exception\ValidateException;
use think\helper\Arr;

class MaterialController extends Controller
{
    public function save()
    {
        $data = $this->validate([
            'type'  => 'require',
            'media' => 'require|file',
        ]);

        /** @var \think\file\UploadedFile $media */
        $media = $data['media'];

        $result = $this->application->getClient()->post('/cgi-bin/material/add_material', [
            'multipart' => [
                [
                    'name'     => 'type',
                    'contents' => $data['type'],
                ],
                [
                    'name'     => 'media',
                    'contents' => fopen($media->getRealPath(), 'r'),
                    'filename' => $media->getOriginalName(),
                ],
            ],
        ]);

        if (Arr::get($result, 'errcode')) {
            throw new ValidateException(Arr::get($result, 'errmsg'));
        }
        return json($result);
    }
}
