<?php

namespace app\controller\api\weaccount;

use think\exception\ValidateException;
use think\helper\Arr;

class MediaController extends Controller
{
    public function image()
    {
        $data = $this->validate([
            'media' => 'file',
            'url'   => 'url',
        ]);

        /** @var \think\file\UploadedFile $media */
        $media = Arr::get($data, 'media');

        if (empty($media)) {
            $url = Arr::get($data, 'url');
            if (empty($url)) {
                throw new ValidateException('url and media is empty');
            }
            //下载图片
            $response = $this->application->getHttpClient()->get($url);

            $body = $response->getBody();
        } else {
            $body = fopen($media->getRealPath(), 'r');
        }

        $result = $this->application->getClient()->post('/cgi-bin/media/uploadimg', [
            'multipart' => [
                [
                    'name'     => 'media',
                    'contents' => $body,
                    'filename' => 'image.png',
                ],
            ],
        ]);

        if (Arr::get($result, 'errcode')) {
            throw new ValidateException(Arr::get($result, 'errmsg'));
        }
        return json($result);
    }
}
