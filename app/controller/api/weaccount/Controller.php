<?php

namespace app\controller\api\weaccount;

use app\BaseController;
use app\model\AccessToken;
use app\model\AssistantWeaccount;
use app\Request;
use think\helper\Str;
use yunwuxin\auth\exception\AuthenticationException;

abstract class Controller extends BaseController
{

    /** @var \app\lib\wechat\account\Application */
    protected $application;

    protected function initialize()
    {
        $this->middleware(function (Request $request, $next) {

            $header = $request->header('Authorization');
            if (!empty($header) && Str::startsWith($header, 'Bearer ')) {
                $token = Str::substr($header, 7);

                $accessToken = AccessToken::getByToken($token, AssistantWeaccount::class);
                if ($accessToken) {
                    $weaccount = $accessToken->accessible->weaccount;

                    $application = app(\app\lib\wechat\wxa\Application::class);

                    $this->application = $application->getAccountAppWithRefreshToken($weaccount->app_id, $weaccount->refresh_token);

                    return $next($request);
                }
            }

            throw new AuthenticationException();
        });
    }
}
