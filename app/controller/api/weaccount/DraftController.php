<?php

namespace app\controller\api\weaccount;

use think\exception\ValidateException;
use think\helper\Arr;

class DraftController extends Controller
{
    public function save()
    {
        $data = $this->validate([
            'articles' => 'require',
        ]);

        $result = $this->application->getClient()->post('/cgi-bin/draft/add', [
            'json' => $data,
        ]);

        if (Arr::get($result, 'errcode')) {
            throw new ValidateException(Arr::get($result, 'errmsg'));
        }
        return json($result);
    }
}
