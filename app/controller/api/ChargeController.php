<?php

namespace app\controller\api;

use app\model\Charge;
use think\exception\ValidateException;
use function yunwuxin\pay\generate_no;

class ChargeController extends ApiController
{
    public function save()
    {
        $data = $this->validate([
            'order_no'   => 'require',
            'subject'    => 'require',
            'amount'     => 'require',
            'user_id'    => '',
            'return_url' => '',
            'notify_url' => '',
            'revoke_url' => '',
        ]);

        $data['client_id']      = $this->application->client_id;
        $data['application_id'] = $this->application->id;

        $charge = Charge::where('application_id', $data['application_id'])
            ->where('order_no', $data['order_no'])
            ->find();

        if (empty($charge)) {
            $data['trade_no'] = generate_no();
            $charge           = Charge::create($data);
        }

        return json($charge->visible(['order_no', 'trade_no'])->append(['pay_url']));
    }

    protected function queryCharge()
    {
        $query = Charge::where('application_id', $this->application->id);

        if ($this->request->has('trade_no')) {
            $query->where('trade_no', $this->request->param('trade_no'));
        } elseif ($this->request->has('order_no')) {
            $query->where('order_no', $this->request->param('order_no'));
        } else {
            throw new ValidateException('trade_no or order_no not found');
        }
        return $query;
    }

    public function revoke()
    {
        $charge = $this->queryCharge()->find();

        if (empty($charge)) {
            abort(404, '交易订单不存在');
        }

        $charge->revoke();
    }

    public function query()
    {
        $charge = $this->queryCharge()->find();
        if (empty($charge)) {
            abort(404, '交易订单不存在');
        }
        return $charge;
    }
}
