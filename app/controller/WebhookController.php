<?php

namespace app\controller;

use app\BaseController;
use app\lib\Date;
use app\model\Sms;
use app\model\SmsLog;
use app\model\SmsReply;
use app\model\SmsSign;
use app\model\SmsTemplate;
use app\notification\Message;
use think\annotation\route\Post;
use think\helper\Arr;
use think\tracing\Tracer;
use yunwuxin\facade\Notification;

class WebhookController extends BaseController
{
    #[Post('webhook/submail')]
    public function submail(Tracer $tracer)
    {
        $data  = $this->request->param();
        $event = Arr::get($data, 'events');
        $tracer->getActiveSpan()?->setTag('event', $event);

        switch ($event) {
            case 'mo':
                //上行短信
                $mobile = Arr::get($data, 'address');

                //submail的时间戳有BUG 需要减去16小时
                $replyTime = Date::createFromTimestamp(Arr::get($data, 'timestamp'))->subHours(16);

                //获取上一次发送的短信
                $log = SmsLog::recent()
                    ->where('phone', $mobile)
                    ->where('send_time', '<', $replyTime)
                    ->order('send_time', 'desc')
                    ->findOrFail();

                $reply = SmsReply::where('user_id', $log->user_id)
                    ->where('mobile', $mobile)
                    ->where('content', Arr::get($data, 'content'))
                    ->where('reply_time', $replyTime)
                    ->find();

                if (!$reply) {
                    SmsReply::create([
                        'user_id'    => $log->user_id,
                        'mobile'     => $mobile,
                        'content'    => Arr::get($data, 'content'),
                        'reply_time' => $replyTime,
                        'source'     => $log->content,
                    ]);

                    Sms::callWebhook($log->user_id, [
                        'action' => 'mo',
                        'reply'  => [
                            'mobile'     => $mobile,
                            'content'    => Arr::get($data, 'content'),
                            'reply_time' => $replyTime,
                        ],
                    ]);
                }
                break;
            case 'delivered':
                //短信发送成功
                $id  = Arr::get($data, 'send_id');
                $log = SmsLog::recent()->where('send_id', $id)->findOrFail();

                $log->updateStatus(1);

                Sms::callWebhook($log->user_id, [
                    'action' => 'report',
                    'sms'    => $log->toData(),
                ]);
                break;
            case 'dropped':
                //短信发送失败
                $id  = Arr::get($data, 'send_id');
                $log = SmsLog::recent()->where('send_id', $id)->findOrFail();

                $reason = Arr::get($data, 'report_desc', '未知错误');
                //替换SUBMAIL字样
                $reason = str_ireplace('SUBMAIL', 'TopThink', $reason);

                $log->updateStatus(-1, $reason);

                Sms::callWebhook($log->user_id, [
                    'action' => 'report',
                    'sms'    => $log->toData(),
                ]);

                break;
            case 'template_accept':
                //模板审核通过
                $template = SmsTemplate::where('out_id', Arr::get($data, 'template_id'))->findOrFail();
                $template->save(['status' => 1]);

                Notification::send($template->user_id, new Message("您提交的短信模板“{$template->name}”已审核通过。"));

                Sms::callWebhook($template->user_id, [
                    'action'   => 'template',
                    'template' => $template->toData(),
                ]);
                break;
            case 'template_reject':
                //模板审核失败
                $template = SmsTemplate::where('out_id', Arr::get($data, 'template_id'))->findOrFail();

                $reason = Arr::get($data, 'reason');

                //替换SUBMAIL字样
                $reason = str_ireplace("SUBMAIL", "TopThink", $reason);

                $template->save(['status' => -1, 'error' => $reason]);

                Notification::send($template->user_id, new Message("您提交的短信模板“{$template->name}”审核未通过。"));

                Sms::callWebhook($template->user_id, [
                    'action'   => 'template',
                    'template' => $template->toData(),
                ]);
                break;
            case 'sms_signature':
                $sign   = preg_replace('/^[\p{Z}\p{P}]+|[\p{Z}\p{P}]+$/u', '', Arr::get($data, 'sms_signature'));
                $sign   = SmsSign::whereIn('status', [0, 3])->where('name', $sign)->findOrFail();
                $status = Arr::get($data, 'status');
                switch ($status) {
                    case 1:
                        $sign->pass();
                        break;
                    case 2:
                        $sign->reject(Arr::get($data, 'reason'));
                        break;
                }
                break;
        }
    }
}
