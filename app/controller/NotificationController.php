<?php

namespace app\controller;

use app\BaseController;
use app\lib\Date;
use app\model\Notification;
use app\model\UserWeaccount;
use app\Request;

class NotificationController extends BaseController
{
    protected $application = 0;
    protected $channel;

    protected function initialize()
    {
        $this->middleware(function (Request $request, $next) {
            $this->application = $request->header('Application', 0);
            $this->channel     = $request->header('Channel');
            return $next($request);
        })->except('js');
    }

    public function index($type)
    {
        $query = Notification::where('to_id', $this->user->id)
            ->where('application_id', $this->application)
            ->order('id desc');

        if ($this->channel) {
            $query->where('channel', $this->channel);
        }

        switch ($type) {
            case 'unread':
                $query->whereNull('read_time');
                break;
        }

        return $query->paginate(8);
    }

    public function update($id = null)
    {
        $where = [
            'application_id' => $this->application,
            'to_id'          => $this->user->id,
            'read_time'      => null,
        ];

        if ($id) {
            $where['id'] = $id;
        }

        if ($this->channel) {
            $where['channel'] = $this->channel;
        }

        Notification::update([
            'read_time' => (string) Date::now(),
        ], $where);
    }

    public function delete()
    {
        $where = [
            'application_id' => $this->application,
            'to_id'          => $this->user->id,
        ];

        if ($this->channel) {
            $where['channel'] = $this->channel;
        }

        Notification::destroy($where);
    }

    public function count()
    {
        $where = [
            'application_id' => $this->application,
            'to_id'          => $this->user->id,
        ];

        if ($this->channel) {
            $where['channel'] = $this->channel;
        }

        return [
            'unread' => Notification::where($where)->whereNull('read_time')->count(),
            'all'    => Notification::where($where)->count(),
        ];
    }

    public function js()
    {
        $file = root_path('asset/notification/dist') . 'notification.min.js';

        return \think\swoole\helper\file($file);
    }

    public function qrcode()
    {
        $account = UserWeaccount::where('user_id', $this->user->id)->find();

        if (empty($account) || $account->isTicketExpired()) {

            $weaccount = Notification::getWeaccount();

            $result = $weaccount->getClient()->post('/cgi-bin/qrcode/create', ['json' => [
                'expire_seconds' => 5 * 60,
                'action_name'    => 'QR_STR_SCENE',
                'action_info'    => ['scene' => ['scene_str' => "notification:{$this->user->id}"]],
            ]]);

            if (empty($account)) {
                $account = UserWeaccount::create([
                    'user_id'     => $this->user->id,
                    'ticket'      => $result['ticket'],
                    'ticket_time' => Date::now()->addSeconds($result['expire_seconds']),
                ]);
            } else {
                $account->save([
                    'ticket'      => $result['ticket'],
                    'ticket_time' => Date::now()->addSeconds($result['expire_seconds']),
                ]);
            }
        }

        return ['qrcode' => (string) url('/cgi-bin/showqrcode', [
            'ticket' => $account->ticket,
        ])->domain('https://mp.weixin.qq.com')];
    }
}
