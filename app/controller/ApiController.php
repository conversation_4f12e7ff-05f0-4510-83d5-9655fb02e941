<?php

namespace app\controller;

use app\BaseController;
use app\model\Api;
use think\annotation\route\Get;

class ApiController extends BaseController
{
    #[Get('api/:id')]
    public function read(\rpc\contract\api\Api $gateway, $id)
    {
        $api = Api::findOrFail($id);

        $users = $api->users()->count();

        $applied = !!$api->users()->attached($this->user);

        $info = $gateway->getInfo($api->name);

        return view('api/read')
            ->assign('api', $api)
            ->assign('applied', $applied)
            ->assign('info', $info)
            ->assign('users', $users);
    }
}
