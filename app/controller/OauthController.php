<?php

namespace app\controller;

use app\BaseController;
use app\model\Application;
use think\Session;
use yunwuxin\oauth\server\AuthorizationServer;
use yunwuxin\oauth\server\exception\OAuthServerException;

class OauthController extends BaseController
{
    public function authorize(AuthorizationServer $server, Session $session)
    {
        $force   = $this->request->has('force');
        $logined = $session->get('logined', false);

        if (!$this->user || ($force && !$logined)) {
            return redirect('/login')
                ->with('force', $force)
                ->remember();
        }

        try {
            $authRequest = $server->validateAuthorizationRequest($this->request);

            $client = $authRequest->getClient();
            $scopes = $authRequest->getScopes();

            $app = Application::getByClientId($client->getIdentifier());

            if (
                $this->request->isGet() &&
                (
                    (!empty($scopes) || $this->request->param('confirm', false)) &&
                    !$this->user->applications()->attached($app)
                )
            ) {
                $name = $this->request->param('app', $app->name);

                return view('auth/authorize')
                    ->assign('app', $name)
                    ->assign('scopes', $scopes);
            }

            $this->user->applications()->attach($app);

            $authRequest->setUser($this->user->id);

            $authRequest->setAuthorizationApproved(true);
            // 完成后重定向至客户端请求重定向地址
            return $server->completeAuthorizationRequest($authRequest);
        } catch (OAuthServerException $e) {
            return $e->generateHttpResponse();
        }
    }

    public function token(AuthorizationServer $server)
    {
        try {
            return $server->respondToAccessTokenRequest($this->request);
        } catch (OAuthServerException $e) {
            return $e->generateHttpResponse();
        }
    }

    public function jwk()
    {
        $publicKey = "-----BEGIN PUBLIC KEY-----\n"
            . wordwrap(config('oauth-server.public_key'), 64, "\n", true)
            . "\n-----END PUBLIC KEY-----";

        $options = [
            'use' => 'sig',
            'alg' => 'RS256',
            'kid' => 'oauth',
        ];

        $keyInfo = openssl_pkey_get_details(\openssl_pkey_get_public($publicKey));

        $jsonData = array_merge(
            $options,
            [
                'kty' => 'RSA',
                'n'   => rtrim(strtr(base64_encode($keyInfo['rsa']['n']), '+/', '-_'), '='),
                'e'   => rtrim(strtr(base64_encode($keyInfo['rsa']['e']), '+/', '-_'), '='),
            ]
        );

        return json($jsonData);
    }
}
