FROM registry.cn-shanghai.aliyuncs.com/topthink/php:8.2-swoole-nginx

#开启openssl兼容模式 支持DES-ECB
COPY docker/openssl.cnf /etc/ssl/openssl.cnf

#配置supervisor
ADD docker/supervisord.conf /etc/supervisor/conf.d/web.conf

COPY docker/entrypoint.sh /sbin/entrypoint.sh
RUN chmod 755 /sbin/entrypoint.sh

#配置nginx
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

#安装代码
COPY . /opt/htdocs

RUN chown www-data:www-data -R /opt/htdocs/runtime

EXPOSE 80

ENTRYPOINT ["/sbin/entrypoint.sh"]
CMD ["app:start"]
