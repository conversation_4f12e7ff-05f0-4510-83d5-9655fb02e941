apiVersion: apps/v1
kind: Deployment
metadata:
  name: core
  namespace: topthink
  labels:
    app: core
spec:
  replicas: 2
  selector:
    matchLabels:
      app: core
  template:
    metadata:
      labels:
        app: core
    spec:
      initContainers:
        - name: init
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/core:IMAGE_TAG
          imagePullPolicy: Always
          args:
            - 'app:init'
          env:
            - name: PHP_NOTIFY_APPID
              value: wxf3347576c862bab6
            - name: PHP_NOTIFY_TEMPLATE
              value: HLZL-e-ikRWCWnY9x8tnD7ULZsM2-yEJSxBkz9J2d9w
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_DB_NAME
              value: topthink
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_CLOUD_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: cloud-client-id
            - name: PHP_CLOUD_CLIENT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: cloud-client-secret
            - name: PHP_PROXY
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: proxy
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_KAFKA_HOST
              value: kafka.thirdparty
            - name: PHP_CK_HOST
              value: clickhouse.thirdparty
            - name: PHP_CK_NAME
              value: topthink
            - name: PHP_API_HOST
              value: api-svc
            - name: PHP_AI_HOST
              value: ai-svc
            - name: PHP_QDRANT_HOST
              value: qdrant-svc.thirdparty
            - name: PHP_SMTP_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-host
            - name: PHP_SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-port
            - name: PHP_SMTP_USER
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-user
            - name: PHP_SMTP_PASS
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-pass
            - name: PHP_SMTP_ENCRYPTION
              value: ssl
            - name: PHP_APP_ADMIN
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: app-admin
            - name: PHP_ZIPKIN_ENDPOINT
              value: http://tracing-analysis-dc-sh-internal.aliyuncs.com/adapt_hvfmcpk6dx@82da6cbc6ee1477_hvfmcpk6dx@53df7ad2afe8301/api/v2/spans
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
      containers:
        - name: main
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/core:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: PHP_NOTIFY_APPID
              value: wxf3347576c862bab6
            - name: PHP_NOTIFY_TEMPLATE
              value: HLZL-e-ikRWCWnY9x8tnD7ULZsM2-yEJSxBkz9J2d9w
            - name: PHP_CACHE_TYPE
              value: redis
            - name: PHP_DB_NAME
              value: topthink
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_CLOUD_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: cloud-client-id
            - name: PHP_CLOUD_CLIENT_SECRET
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: cloud-client-secret
            - name: PHP_PROXY
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: proxy
            - name: PHP_REDIS_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: redis-host
            - name: PHP_KAFKA_HOST
              value: kafka.thirdparty
            - name: PHP_CK_HOST
              value: clickhouse.thirdparty
            - name: PHP_CK_NAME
              value: topthink
            - name: PHP_API_HOST
              value: api-svc
            - name: PHP_AI_HOST
              value: ai-svc
            - name: PHP_QDRANT_HOST
              value: qdrant-svc.thirdparty
            - name: PHP_SMTP_HOST
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-host
            - name: PHP_SMTP_PORT
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-port
            - name: PHP_SMTP_USER
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-user
            - name: PHP_SMTP_PASS
              valueFrom:
                configMapKeyRef:
                  name: common-config
                  key: smtp-pass
            - name: PHP_SMTP_ENCRYPTION
              value: ssl
            - name: PHP_APP_ADMIN
              valueFrom:
                configMapKeyRef:
                  name: core-config
                  key: app-admin
            - name: PHP_ZIPKIN_ENDPOINT
              value: http://tracing-analysis-dc-sh-internal.aliyuncs.com/adapt_hvfmcpk6dx@82da6cbc6ee1477_hvfmcpk6dx@53df7ad2afe8301/api/v2/spans
            - name: PHP_SOHO_SP_ID
              value: "1760581032714727426"
            - name: PHP_SOHO_MERCHANT_ID
              value: "1760577971704426498"
            - name: PHP_SOHO_LEVY_ID
              value: "1600771492911640578"
            - name: PHP_SOHO_TASK_ID
              value: "1910207357423718402"
            - name: aliyun_logs_core
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_core_project
              value: topthink
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          ports:
            - name: main
              containerPort: 80
              protocol: TCP
            - name: console
              containerPort: 81
              protocol: TCP
          volumeMounts:
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log/
            - name: volume-storage
              mountPath: /opt/htdocs/storage/
              subPath: ./core
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-storage
          persistentVolumeClaim:
            claimName: topthink-pvc
      serviceAccountName: topthink
---
apiVersion: v1
kind: Service
metadata:
  name: core-svc
  namespace: topthink
spec:
  ports:
    - name: main
      port: 80
      protocol: TCP
      targetPort: 80
    - name: console
      port: 81
      protocol: TCP
      targetPort: 81
    - name: rpc
      port: 9000
      protocol: TCP
      targetPort: 9000
  selector:
    app: core
  type: ClusterIP
