<?php

use app\lib\oauth\middleware\CheckClientCredentials;
use app\lib\oauth\middleware\CheckScopes;
use think\facade\Route;
use yunwuxin\auth\middleware\Authentication;

Route::group('api', function () {

    Route::group(function () {

        Route::group('internal', function () {
            Route::group('sms', function () {
                Route::post('sign', 'sms/sign');
                Route::post('template', 'sms/template');
            });
            Route::get('api/:name', 'api/read');
            Route::get('api', 'api/index');
        })->prefix('api.internal.')->middleware(CheckScopes::class, 'internal');

        Route::group(function () {
            Route::post('charge/revoke', 'charge/revoke');
            Route::get('charge/query', 'charge/query');
            Route::post('charge', 'charge/save');
        })->middleware(CheckScopes::class, 'charge');

        Route::post('notification', 'notification/save')->middleware(CheckScopes::class, 'notification');

        Route::group('user', function () {
            Route::group(':user', function () {
                Route::put('/', 'index/update');
                Route::post('social', 'social/save');

                Route::group(function () {
                    Route::post('certification', 'certification/save');
                    Route::get('certification', 'certification/read');

                    Route::post('enterprise', 'enterprise/save');
                    Route::get('enterprise', 'enterprise/read');
                })->middleware(CheckScopes::class, 'verify');

                Route::group(function () {
                    Route::post('coin/dec', 'coin/dec');
                    Route::post('coin/inc', 'coin/inc');

                    //TODO deprecated
                    Route::post('dec_coin', 'coin/dec');
                    Route::post('inc_coin', 'coin/inc');
                })->middleware(CheckScopes::class, 'coin');

                Route::group(function () {
                    Route::post('money/dec', 'money/dec');
                    Route::post('money/inc', 'money/inc');
                })->middleware(CheckScopes::class, 'money');
            });
        })->prefix('api.user.');

        Route::get('weapp/:id/qrcode', 'weapp/qrcode');
        Route::get('weapp/:id', 'weapp/read');
        Route::put('weapp/:id', 'weapp/update');
        Route::post('weapp', 'weapp/save');

        Route::group('write', function () {
            Route::get('privatization/:name', 'write/privatization');
        })->middleware(CheckScopes::class, 'write');

        Route::group('license', function () {
            Route::get(':type/:id', 'license/read');
        })->middleware(CheckScopes::class, 'internal');

        Route::group('me', function () {
            Route::get('/', 'index/index');
            Route::post('logout', 'index/logout');

            Route::get('cert', 'cert/index');
            Route::get('cert/:id', 'cert/read');
        })->middleware(Authentication::class, 'api')->prefix('api.me.');
    })->middleware(CheckClientCredentials::class);

    Route::group('license', function () {
        Route::post('verify', 'license/verify');
    });

    Route::post('passport/login', 'passport/login');
    Route::get('passport/user', 'passport/user');

    Route::group('weaccount', function () {
        Route::resource('draft', 'draft');
        Route::resource('material', 'material');
        Route::post('media/image', 'media/image');
        Route::resource('media', 'media');
    })->prefix('api.weaccount.');
})->prefix('api.');
