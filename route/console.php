<?php

use app\lib\provider\Console;
use app\lib\provider\Notification;
use think\facade\Route;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authentication;

//控制台
Route::group('console', function () {
    Route::group(function () {
        Route::get('current', function (Auth $auth) {
            $user = $auth->user();

            return $user->withFieldAttr([
                'notification' => function () use ($user) {
                    return [
                        'url'   => (string) main_url('/notification/js'),
                        'token' => Notification::createToken($user),
                    ];
                },
                'token'        => function () use ($user) {
                    return Console::createToken($user);
                },
            ]);
        });

        Route::get('logout', function (Auth $auth) {
            $user = $auth->user();
            $user->resetToken();
        });

        Route::post('upload/:dir', 'upload/save');

        Route::post('order/check', 'order/check');

        Route::group('user', function () {
            Route::get('certification/check', 'certification/check');
            Route::get('certification', 'certification/index');
            Route::post('certification', 'certification/save');
            Route::delete('certification', 'certification/delete');

            Route::get('enterprise', 'enterprise/index');
            Route::post('enterprise', 'enterprise/save');

            Route::post('email/code', 'email/code');
            Route::post('email', 'email/save');

            Route::post('mobile/code', 'mobile/code');
            Route::post('mobile', 'mobile/save');

            Route::post('name', 'info/name');
            Route::post('avatar', 'info/avatar');

            Route::get('social', 'social/index');
            Route::post('social', 'social/save');
            Route::get('social/:channel', 'social/read');
            Route::delete('social/:channel', 'social/delete');
        })->prefix('console.user.');

        Route::group('finance', function () {
            Route::get('coin', 'coin.index/index');
            Route::get('coin/logs', 'coin.index/logs');
            Route::resource('coin/withdraw', 'coin.withdraw');

            Route::get('money', 'money.index/index');
            Route::get('money/logs', 'money.index/logs');
            Route::resource('money/withdraw', 'money.withdraw');

            Route::get('charge', 'charge/index');

            Route::get('invoice/invoiceable', 'invoice/invoiceable');
            Route::get('invoice', 'invoice/index');
            Route::get('invoice/:id/charge', 'invoice/charge');
            Route::post('invoice', 'invoice/save');
            Route::delete('invoice/:id', 'invoice/delete');

            Route::group('statistics', function () {
                Route::get('basic', 'statistics/basic');
            });
        })->prefix('console.finance.');

        Route::group('ssl', function () {
            Route::resource('cert', 'cert');

            Route::get('order/brands', 'order/brands');
            Route::post('order/:id/issue', 'order/issue');
            Route::get('order/:id/domains', 'order/domains');
            Route::post('order/:id/verify', 'order/verify');
            Route::resource('order', 'order');

            Route::resource('contact', 'contact');
            Route::resource('org', 'org');
        })->prefix('console.ssl.');

        Route::group('assistant', function () {
            Route::group('site/:site', function () {
                Route::put('appearance', 'index/appearance');
                Route::put('features', 'index/features');
                Route::put('customs', 'index/customs');
                Route::put('broadcast', 'index/broadcast');
                Route::put('robot', 'index/robot');
                Route::put('scripts', 'index/scripts');
                Route::post('buy', 'index/buy');

                Route::post('member/invite', 'member/invite');

                Route::get('tracer/active', 'tracer/active');
                Route::get('tracer/stats', 'tracer/stats');
                Route::get('tracer/pageviews', 'tracer/pageviews');
                Route::get('tracer/metrics', 'tracer/metrics');

                Route::resource('member', 'member');

                Route::post('feedback/:id/status', 'feedback/status');
                Route::post('feedback/:id/question', 'feedback/question');
                Route::post('feedback/:id/distribution', 'feedback/distribution');
                Route::get('feedback/:id/entry', 'feedback/entry');
                Route::get('feedback/:id/stats', 'feedback/stats');
                Route::resource('feedback', 'feedback');
            })->prefix('console.assistant.site.');

            Route::get('site/invite/:code', 'site.invite/read');
            Route::post('site/invite/:code', 'site.invite/save');
            Route::resource('site', 'site.index')->vars(['site' => 'site']);

            Route::group('weaccount', function () {
                Route::get('', 'index/index');
                Route::get('check', 'index/check');
                Route::post('qrcode', 'index/qrcode');
                Route::get(':id', 'index/read');

                Route::put(':weaccount/robot', 'index/robot');
                Route::get(':weaccount/token', 'token/index');
                Route::post(':weaccount/token', 'token/save');
                Route::delete(':weaccount/token/:id', 'token/delete');
            })->prefix('console.assistant.weaccount.');

            Route::get('hecong/login', 'hecong/login');
            Route::post('hecong/buy', 'hecong/buy');
            Route::resource('hecong', 'hecong');
        })->prefix('console.assistant.');

        Route::group('ai', function () {
            Route::group('dataset/:dataset', function () {
                Route::post('search', 'search/index');
                Route::resource('source', 'source');
                Route::get('source/:id/part', 'source/part');
                Route::post('source/:id/train', 'source/train');
            })->prefix('console.ai.dataset.');

            Route::post('dataset/import', 'dataset.import/save');
            Route::post('dataset/import/verify', 'dataset.import/verify');
            Route::get('dataset/import/books', 'dataset.import/books');
            Route::get('dataset/search', 'dataset.index/search');
            Route::post('dataset/amend', 'dataset.index/amend');
            Route::resource('dataset', 'dataset.index');
        })->prefix('console.ai.');

        Route::group('admin', function () {
            //统计
            Route::group('statistic', function () {
                Route::get('basic', 'statistic/basic');
                Route::get('charge', 'statistic/charge');
                Route::get('user', 'statistic/user');
                Route::get('order', 'statistic/order');
            });

            //应用
            Route::get('application/search', 'application/search');
            Route::resource('application', 'application');

            //用户
            Route::group('user', function () {

                Route::get('search', 'index/search');

                Route::group(':user', function () {
                    Route::get('', 'index/read');

                    Route::get('certification', 'certification/read');
                    Route::delete('certification', 'certification/delete');
                    Route::get('enterprise', 'enterprise/read');
                    Route::delete('enterprise', 'enterprise/delete');

                    Route::post('features', 'index/features');
                    Route::any('tags', 'index/tags');
                    Route::post('notify', 'index/notify');

                    Route::get('coin/logs', 'coin/logs');
                    Route::post('coin/sync', 'coin/sync');
                    Route::post('coin', 'coin/save');

                    Route::get('money/logs', 'money/logs');
                    Route::post('money/sync', 'money/sync');
                    Route::post('money', 'money/save');
                })->pattern(['user' => '\d+']);

                Route::get('', 'index/index');
            })->prefix('console.admin.user.');

            //财务
            Route::group('finance', function () {
                Route::get('charge', 'charge/index');
                Route::get('charge/invoicing', 'charge/invoicing');
                Route::post('charge/:id/invoiced', 'charge/invoiced');
                Route::post('charge/:id/paid', 'charge/paid');
                Route::post('charge/:id/retry', 'charge/retry');
                Route::post('charge/:id/revoke', 'charge/revoke');

                Route::get('refund', 'refund/index');
                Route::post('refund/:id/retry', 'refund/retry');

                Route::post('invoice', 'invoice/save');
                Route::get('invoice', 'invoice/index');
                Route::get('invoice/:id/order', 'invoice/order');
            })->prefix('console.admin.finance.');

            //产品
            Route::group('product', function () {
                Route::group('write', function () {
                    Route::resource('privatization', 'privatization');
                })->prefix('console.admin.product.write.');

                Route::group('assistant', function () {
                    Route::resource('site', 'site');
                    Route::post('site/:id/plan', 'site/plan');

                    Route::resource('hecong', 'hecong');
                })->prefix('console.admin.product.assistant.');

                Route::group('ssl', function () {
                    Route::post('brand/:id/cert', 'brand/cert');
                    Route::post('brand/:id/status', 'brand/status');
                    Route::resource('brand', 'brand');

                    Route::get('order', 'order/index');
                })->prefix('console.admin.product.ssl.');
            })->prefix('console.admin.product.');

            //审批
            Route::group('approval', function () {
                Route::get('enterprise', 'enterprise/index');
                Route::post('enterprise/:id/pass', 'enterprise/pass');
                Route::post('enterprise/:id/reject', 'enterprise/reject');

                Route::get('invoice', 'invoice/index');
                Route::post('invoice/:id/pass', 'invoice/pass');
                Route::post('invoice/:id/reject', 'invoice/reject');

                Route::get('withdraw/coin', 'withdraw.coin/index');
                Route::post('withdraw/coin/:id/confirm', 'withdraw.coin/confirm');
                Route::post('withdraw/coin/:id/cancel', 'withdraw.coin/cancel');

                Route::get('withdraw/money', 'withdraw.money/index');
                Route::post('withdraw/money/:id/confirm', 'withdraw.money/confirm');
                Route::post('withdraw/money/:id/cancel', 'withdraw.money/cancel');

                Route::get('settlement', 'settlement/index');
                Route::post('settlement/:id/confirm', 'settlement/confirm');
                Route::post('settlement/:id/cancel', 'settlement/cancel');
            })->prefix('console.admin.approval.');

            //微信开放平台
            Route::group('wxa', function () {

                Route::post('template/:id/release', 'template/release');
                Route::post('template/:id/retry', 'template/retry');
                Route::resource('template', 'template');

                Route::get('weapp', 'weapp/index');
                Route::get('weapp/:id/audit', 'weapp/audit');
                Route::post('weapp/:id/audit', 'weapp/audit');
                Route::get('weapp/:id/experience', 'weapp/experience');
                Route::get('weapp/:id/domain', 'weapp/domain');
                Route::post('weapp/:id/domain', 'weapp/domain');

                Route::get('weaccount', 'weaccount/index');
            })->prefix('console.admin.wxa.');

            //设置
            Route::get('setting', 'setting/index');
            Route::post('setting', 'setting/save');
        })->prefix('console.admin.');
    })->middleware(Authentication::class, 'console');

    Route::get('login', 'login/index');
    Route::post('login/token', 'login/token');

    Route::get('manifest', 'manifest/index');
})->prefix('console.');
