<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use app\controller\SocialController;
use app\middleware\AuthenticationSession;
use app\middleware\CheckCpsRequest;
use think\facade\Route;
use think\middleware\SessionInit;
use yunwuxin\auth\middleware\Authentication;

Route::group(function () {
    Route::group('login', function () {
        Route::get('/', 'auth/index');
        Route::post('/', 'auth/login');
        Route::get('mini', 'auth/mini');
        Route::get('social/:channel', 'auth/social');
        Route::get('complete', 'auth/complete');
        Route::post('captcha', 'auth/captcha');
    });

    Route::group(function () {
        Route::get('logout', 'auth/logout');
    })->middleware(Authentication::class);

    Route::group('product', function () {
        Route::get('/:name', 'product/read');
    });

    Route::group('cashier', function () {
        Route::get('/', 'cashier/index');
        Route::post('/', 'cashier/save');
        Route::post('paid', 'cashier/paid');
        Route::get('check', 'cashier/check');
        Route::post('transfer', 'cashier/transfer');
        Route::post('promo', 'cashier/promo');
    });

    Route::group('oauth', function () {
        Route::any('authorize', 'oauth/authorize');
        Route::post('token', 'oauth/token');
        Route::get('jwk', 'oauth/jwk');
    });

    Route::group('social', function () {
        Route::get(":channel/callback", '@handleSocialCallback')->name('SOCIAL_CALLBACK');
        Route::get(":channel", '@redirectToSocial')->name('SOCIAL');
    })->prefix(SocialController::class);

    Route::get('avatar/:hash', 'avatar/index');

    Route::get('/', 'index/index');
})->middleware([SessionInit::class, AuthenticationSession::class, CheckCpsRequest::class]);

//小程序登录
Route::group('mini', function () {
    Route::post('check', 'mini/check');
    Route::post('login', 'mini/login');
    Route::post('register', 'mini/register');
    Route::get('qrcode', 'mini/qrcode');

    Route::get('metadata', 'mini/metadata');
    Route::post('authorize', 'mini/authorize');
});

//passport
Route::group('passport', function () {
    Route::get('js', 'passport/js');
    Route::post('login', 'passport/login');
    Route::get('mini', 'passport/mini');
    Route::post('captcha', 'auth/captcha');
});

//网站助手
Route::group('assistant', function () {
    Route::get('js/:filename$', 'assistant/js')->pattern(['filename' => '\P{C}*']);
    Route::get('js', 'assistant/js');

    Route::get(':id', 'assistant/config');
    Route::post(':id/send', 'assistant/send');
    Route::post(':id/feedback', 'assistant/feedback');
});

//消息通知
Route::group('notification', function () {
    Route::get('js', 'notification/js');
    Route::group(function () {
        Route::get('/', 'notification/index');
        Route::put('/[:id]', 'notification/update');
        Route::delete('/', 'notification/delete');
        Route::get('count', 'notification/count');
        Route::get('qrcode', 'notification/qrcode');
    })->middleware(Authentication::class, 'notification');
});

Route::group('wxa', function () {
    Route::post('component', 'wxa/component');
    Route::post('biz/:appid', 'wxa/biz');
});

//支付回调路由
Route::any("pay/:channel/notify", '\\yunwuxin\\pay\\NotifyController@index')
    ->completeMatch()
    ->name('PAY_NOTIFY');
