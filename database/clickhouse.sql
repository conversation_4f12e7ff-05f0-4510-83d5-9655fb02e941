create table if not exists api_log
(
    api_id      Int32,
    user_id     Int32,
    package_id  Int32 DEFAULT 0,
    path        LowCardinality(String),
    success     Int8,
    create_time DateTime
)
    engine = MergeTree
        partition by toYYYYMMDD(create_time)
        order by (user_id, api_id, create_time)
        ttl create_time + interval 180 day;

create table if not exists api_log_queue
(
    api_id      Int32,
    user_id     Int32,
    package_id  Int32,
    path        LowCardinality(String),
    success     Int8,
    create_time DateTime
)
    engine = Kafka
        SETTINGS kafka_broker_list = 'topthink-kafka:9092',
            kafka_topic_list = 'api_log',
            kafka_group_name = 'api_log_consumer_group',
            kafka_format = 'JSONEachRow',
            kafka_max_block_size = 100,
            kafka_skip_broken_messages = 10;

create materialized view if not exists api_log_queue_mv to api_log as
select api_id,
       user_id,
       package_id,
       path,
       success,
       create_time
from api_log_queue;

-- Create Event
CREATE TABLE if not exists assistant_site_event
(
    site_id         Int32,
    session_id      UUID,
    event_id        UUID,
    --session
    browser         LowCardinality(String),
    os              LowCardinality(String),
    device          LowCardinality(String),
    screen          LowCardinality(String),
    language        LowCardinality(String),
    country         LowCardinality(String),
    country_code    LowCardinality(String),
    province        LowCardinality(String),
    province_code   LowCardinality(String),
    city            String,
    --pageview
    url             String,
    url_path        String,
    url_domain      String,
    referrer        String,
    referrer_path   String,
    referrer_domain String,
    page_title      String,
    --event
    event_type      UInt32,
    event_name      String,
    create_time     DateTime
)
    engine = MergeTree
        order by (site_id, session_id, create_time)
        partition by toYYYYMMDD(create_time)
        ttl create_time + interval 180 day;

CREATE TABLE if not exists assistant_site_event_queue
(
    site_id         Int32,
    session_id      UUID,
    event_id        UUID,
    --session
    browser         LowCardinality(String),
    os              LowCardinality(String),
    device          LowCardinality(String),
    screen          LowCardinality(String),
    language        LowCardinality(String),
    country         LowCardinality(String),
    country_code    LowCardinality(String),
    province        LowCardinality(String),
    province_code   LowCardinality(String),
    city            String,
    --pageview
    url             String,
    url_path        String,
    url_domain      String,
    referrer        String,
    referrer_path   String,
    referrer_domain String,
    page_title      String,
    --event
    event_type      UInt32,
    event_name      String,
    create_time     DateTime
)
    ENGINE = Kafka
        SETTINGS kafka_broker_list = 'topthink-kafka:9092',
            kafka_topic_list = 'assistant_site_event',
            kafka_group_name = 'assistant_site_event_consumer_group',
            kafka_format = 'JSONEachRow',
            kafka_max_block_size = 100,
            kafka_skip_broken_messages = 10;

create materialized view if not exists assistant_site_event_queue_mv TO assistant_site_event AS
SELECT site_id,
       session_id,
       event_id,
       --session
       browser,
       os,
       device,
       screen,
       language,
       country,
       country_code,
       province,
       province_code,
       city,
       --pageview
       url,
       url_path,
       url_domain,
       referrer,
       referrer_path,
       referrer_domain,
       page_title,
       --event
       event_type,
       event_name,
       create_time
FROM assistant_site_event_queue;

create table if not exists sms_log
(
    user_id     Int32,
    task_id     Int32 DEFAULT 0,
    sign_id     Int32 DEFAULT 0,
    template_id Int32 DEFAULT 0,
    package_id  Int32,
    send_id     String,
    phone       String,
    content     String,
    nums        Int32,
    send_time   DateTime,
    create_time DateTime,
    status      Int8,
    message     Nullable(String)
) engine = ReplacingMergeTree(create_time)
      partition by toYYYYMMDD(send_time)
      order by (user_id, send_id, send_time)
      ttl send_time + interval 180 day;

create table if not exists sms_log_queue
(
    user_id     Int32,
    task_id     Int32,
    sign_id     Int32,
    template_id Int32,
    package_id  Int32,
    send_id     String,
    phone       String,
    content     String,
    nums        Int32,
    send_time   DateTime,
    create_time DateTime,
    status      Int8,
    message     Nullable(String)
)
    engine = Kafka
        settings kafka_broker_list = 'topthink-kafka:9092',
            kafka_topic_list = 'sms_log',
            kafka_group_name = 'sms_log_consumer_group',
            kafka_format = 'JSONEachRow',
            kafka_max_block_size = 100,
            kafka_skip_broken_messages = 10;

create materialized view if not exists sms_log_queue_mv to sms_log as
select user_id,
       task_id,
       sign_id,
       template_id,
       package_id,
       send_id,
       phone,
       content,
       nums,
       send_time,
       create_time,
       status,
       message
from sms_log_queue;

create table if not exists ai_log
(
    user_id     Int32,
    user_type   Int32                  DEFAULT 0,
    type        LowCardinality(String) DEFAULT 'chat',
    prompt      Int32,
    completion  Int32,
    model       LowCardinality(String),
    create_time DateTime
)
    engine = MergeTree
        partition by toYYYYMMDD(create_time)
        order by (user_id, create_time)
        ttl create_time + interval 180 day;

create table if not exists ai_log_queue
(
    user_id     Int32,
    user_type   Int32,
    type        LowCardinality(String),
    prompt      Int32,
    completion  Int32,
    model       LowCardinality(String),
    create_time DateTime
)
    engine = Kafka
        SETTINGS kafka_broker_list = 'topthink-kafka:9092',
            kafka_topic_list = 'ai_log',
            kafka_group_name = 'ai_log_consumer_group',
            kafka_format = 'JSONEachRow',
            kafka_max_block_size = 100,
            kafka_skip_broken_messages = 10;

create materialized view if not exists ai_log_queue_mv to ai_log as
select user_id,
       user_type,
       type,
       prompt,
       completion,
       model,
       create_time
from ai_log_queue;

create table if not exists ai_robot_log
(
    robot_id    Int32,
    question    String,
    answer      String,
    create_time DateTime
)
    engine = MergeTree
        partition by toYYYYMMDD(create_time)
        order by (create_time)
        ttl create_time + interval 180 day;

create table if not exists ai_robot_log_queue
(
    robot_id    Int32,
    question    String,
    answer      String,
    create_time DateTime
)
    engine = Kafka
        SETTINGS kafka_broker_list = 'topthink-kafka:9092',
            kafka_topic_list = 'ai_robot_log',
            kafka_group_name = 'ai_robot_log_consumer_group',
            kafka_format = 'JSONEachRow',
            kafka_max_block_size = 100,
            kafka_skip_broken_messages = 10;

create materialized view if not exists ai_robot_log_queue_mv to ai_robot_log as
select robot_id,
       question,
       answer,
       create_time
from ai_robot_log_queue;
