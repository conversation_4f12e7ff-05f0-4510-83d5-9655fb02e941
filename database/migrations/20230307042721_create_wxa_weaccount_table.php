<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateWxaWeaccountTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('wxa_weaccount')
            ->addColumn(Column::string('app_id')->setUnique())
            ->addColumn(Column::string('refresh_token'))
            ->addColumn(Column::string('nickname'))
            ->addColumn(Column::string('avatar'))
            ->addColumn(Column::string('signature'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addTimestamps()
            ->create();

        $this->table('assistant_weaccount')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::integer('weaccount_id'))
            ->addColumn(Column::string('plan'))
            ->addColumn(Column::timestamp('expire_time'))
            ->addTimestamps()
            ->create();
    }
}
