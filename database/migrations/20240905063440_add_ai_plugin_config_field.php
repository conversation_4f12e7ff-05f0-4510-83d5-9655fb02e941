<?php

use app\lib\Date;
use app\model\SmsPackage;
use think\migration\Migrator;
use think\migration\db\Column;

class AddAiPluginConfigField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('ai_plugin')
            ->addColumn(Column::mediumText('config')->setNullable()->setAfter('api'))
            ->update();

        $cursor = \app\model\ai\Plugin::cursor();

        /** @var \app\model\ai\Plugin $plugin */
        foreach ($cursor as $plugin) {
            if (!empty($plugin->schema)) {
                $plugin->save([
                    'config' => [
                        'schema' => $plugin->schema,
                        'auth'   => $plugin->auth,
                    ],
                ]);
            } elseif (!empty($plugin->api)) {
                $plugin->save([
                    'config' => [
                        'api' => $plugin->api,
                    ],
                ]);
            }
        }
    }

    public function down()
    {
        $this->table('ai_plugin')
            ->removeColumn('config')
            ->update();
    }
}
