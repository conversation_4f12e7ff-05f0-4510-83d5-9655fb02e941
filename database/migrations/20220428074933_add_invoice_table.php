<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddInvoiceTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('invoice')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('title')->setComment('公司名称'))
            ->addColumn(Column::string('taxpayer_number')->setComment('纳税人识别号'))
            ->addColumn(Column::integer('amount')->setComment('发票金额'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addColumn(Column::string('remark')->setNullable())
            ->addTimestamps()
            ->addIndex('user_id')
            ->create();

        $this->table('charge')
            ->addColumn(Column::integer('invoice_id')->setComment('发票')->setDefault(0))
            ->update();
    }
}
