<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddSomeIndex extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('api_log')
            ->addIndex('create_time')
            ->update();

        $this->table('assistant_site_session')
            ->addIndex('create_time')
            ->update();

        $this->table('assistant_site_event')
            ->addIndex('create_time')
            ->addIndex(['site_id', 'create_time'])
            ->addIndex(['site_id', 'session_id', 'create_time'])
            ->update();
    }
}
