<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AiRobot extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('ai_robot')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name'))
            ->addTimestamps()
            ->addIndex(['user_id'])
            ->create();

        $this->table('ai_robot_dataset')
            ->setId(false)
            ->addColumn(Column::integer('robot_id'))
            ->addColumn(Column::integer('dataset_id'))
            ->addIndex(['robot_id', 'dataset_id'], ['unique' => true])
            ->create();
    }
}
