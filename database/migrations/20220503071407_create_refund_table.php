<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateRefundTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('refund')
            ->addColumn(Column::integer('charge_id'))
            ->addColumn(Column::string('refund_no')->setUnique())
            ->addColumn(Column::integer('status')->setDefault(0))
            ->addColumn(Column::text('extra')->setNullable())
            ->addColumn(Column::text('raw')->setNullable())
            ->addColumn(Column::timestamp('refund_time')->setNullable())
            ->addTimestamps()
            ->create();

        $this->table('charge')
            ->addColumn(Column::string('revoke_url')->setNullable()->setAfter('return_url'))
            ->addColumn(Column::timestamp('revoke_time')->setNullable()->setAfter('update_time'))
            ->update();
    }
}
