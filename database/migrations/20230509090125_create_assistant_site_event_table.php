<?php

use app\lib\Date;
use think\migration\Migrator;
use think\migration\db\Column;

class CreateAssistantSiteEventTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('assistant_site_session', [
            'primary_key' => ['id', 'create_time'],
        ])
            ->setId(false)
            ->addColumn(Column::string('id'))
            ->addColumn(Column::integer('site_id'))
            ->addColumn(Column::string('hostname')->setNullable())
            ->addColumn(Column::string('browser')->setNullable())
            ->addColumn(Column::string('os')->setNullable())
            ->addColumn(Column::string('device')->setNullable())
            ->addColumn(Column::string('screen')->setNullable())
            ->addColumn(Column::string('language')->setNullable())
            ->addColumn(Column::string('country')->setNullable())
            ->addColumn(Column::string('country_code')->setNullable())
            ->addColumn(Column::string('province')->setNullable())
            ->addColumn(Column::string('province_code')->setNullable())
            ->addColumn(Column::string('city')->setNullable())
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex('site_id')
            ->create();

        $this->table('assistant_site_event', [
            'primary_key' => ['id', 'create_time'],
        ])
            ->setId(false)
            ->addColumn(Column::integer('id')->setIdentity(true))
            ->addColumn(Column::integer('site_id'))
            ->addColumn(Column::string('session_id'))
            ->addColumn(Column::unsignedInteger('event_type')->setDefault(1))
            ->addColumn(Column::string('event_name')->setNullable())
            ->addColumn(Column::string('url_path', 500))
            ->addColumn(Column::string('url_query', 500)->setNullable())
            ->addColumn(Column::string('referrer_path', 500)->setNullable())
            ->addColumn(Column::string('referrer_query', 500)->setNullable())
            ->addColumn(Column::string('referrer_domain', 500)->setNullable())
            ->addColumn(Column::string('page_title', 500)->setNullable())
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex('site_id')
            ->addIndex('session_id')
            ->create();

        $this->table('assistant_site_event_data', [
            'primary_key' => ['id', 'create_time'],
        ])
            ->setId(false)
            ->addColumn(Column::integer('id')->setIdentity(true))
            ->addColumn(Column::integer('site_id'))
            ->addColumn(Column::integer('event_id'))
            ->addColumn(Column::string('event_key', 500))
            ->addColumn(Column::unsignedInteger('event_data_type'))
            ->addColumn(Column::string('event_string_value', 500)->setNullable())
            ->addColumn(Column::decimal('event_numeric_value', 19, 4)->setNullable())
            ->addColumn(Column::timestamp('event_date_value')->setNullable())
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex('site_id')
            ->addIndex('event_id')
            ->create();

        $date     = Date::today()->endOfDay();
        $dateTime = $date->getTimestamp();
        $dataDate = $date->format('Ymd');

        $this->execute("ALTER TABLE `assistant_site_session` PARTITION BY RANGE (unix_timestamp(create_time))(
            PARTITION p{$dataDate} VALUES LESS THAN ({$dateTime}) ENGINE = InnoDB
)");
        $this->execute("ALTER TABLE `assistant_site_event` PARTITION BY RANGE (unix_timestamp(create_time))(
            PARTITION p{$dataDate} VALUES LESS THAN ({$dateTime}) ENGINE = InnoDB
)");
        $this->execute("ALTER TABLE `assistant_site_event_data` PARTITION BY RANGE (unix_timestamp(create_time))(
            PARTITION p{$dataDate} VALUES LESS THAN ({$dateTime}) ENGINE = InnoDB
)");
    }

    public function down()
    {
        $this->dropTable('assistant_site_session');
        $this->dropTable('assistant_site_event');
        $this->dropTable('assistant_site_event_data');
    }
}
