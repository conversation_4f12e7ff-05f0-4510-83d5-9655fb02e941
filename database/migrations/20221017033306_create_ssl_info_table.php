<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSslInfoTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('ssl_contact')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('first_name'))
            ->addColumn(Column::string('last_name'))
            ->addColumn(Column::string('position'))
            ->addColumn(Column::string('email'))
            ->addColumn(Column::string('telephone'))
            ->addTimestamps()
            ->create();

        $this->table('ssl_org')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('credit_code'))
            ->addColumn(Column::string('country'))
            ->addColumn(Column::string('province'))
            ->addColumn(Column::string('locality'))
            ->addColumn(Column::string('address'))
            ->addColumn(Column::string('postal_code'))
            ->addColumn(Column::string('telephone'))
            ->addTimestamps()
            ->create();
    }
}
