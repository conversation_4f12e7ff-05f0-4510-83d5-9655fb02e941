<?php

use think\migration\db\Column;
use think\migration\Migrator;

class CreatePromoTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('promo')
            ->addColumn(Column::string('code'))
            ->addColumn(Column::string('title'))
            ->addColumn(Column::string('product'))
            ->addColumn(Column::integer('discount'))
            ->addColumn(Column::integer('partner_id')->setDefault(0))
            ->addColumn(Column::integer('rebate')->setDefault(0))
            ->addColumn(Column::timestamp('expire_time')->setNullable())
            ->addTimestamps()
            ->addIndex('partner_id')
            ->addIndex('code', ['unique' => true])
            ->create();
    }
}
