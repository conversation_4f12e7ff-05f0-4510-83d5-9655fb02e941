<?php

use think\migration\db\Column;
use think\migration\Migrator;

class CreateNotificationTable extends Migrator
{

    public function change()
    {
        $this->table('notification')
            ->addColumn(Column::integer('application_id')->setDefault(0))
            ->addColumn(Column::integer('to_id'))
            ->addColumn(Column::integer('from_id')->setDefault(0))
            ->addColumn(Column::text('content'))
            ->addColumn(Column::timestamp('read_time')->setNullable())
            ->addTimestamps()
            ->addIndex(['application_id'])
            ->addIndex(['to_id'])
            ->create();

        $this->table('user_weaccount')
            ->setId(false)
            ->addColumn(Column::integer('user_id')->setUnique())
            ->addColumn(Column::string('openid')->setNullable())
            ->addColumn(Column::string('ticket'))
            ->addColumn(Column::timestamp('ticket_time'))
            ->addTimestamps()
            ->addIndex('openid')
            ->create();
    }
}
