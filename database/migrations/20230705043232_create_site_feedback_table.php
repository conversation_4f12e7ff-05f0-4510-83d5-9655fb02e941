<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSiteFeedbackTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('assistant_site_feedback')
            ->addColumn(Column::integer('site_id'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::text('question')->setNullable())
            ->addColumn(Column::text('distribution')->setNullable())
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addTimestamps()
            ->addSoftDelete()
            ->create();

        $this->table('assistant_site_feedback_entry')
            ->addColumn(Column::integer('feedback_id'))
            ->addColumn(Column::string('session_id'))
            ->addColumn(Column::tinyInteger('star'))
            ->addColumn(Column::string('reason'))
            ->addColumn(Column::string('screenshot'))
            ->addColumn(Column::string('os'))
            ->addColumn(Column::string('browser'))
            ->addTimestamps()
            ->create();
    }
}
