<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSmsTaskTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('sms_task')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name')->setComment('任务名称'))
            ->addColumn(Column::integer('template_id')->setComment('短信模板ID'))
            ->addColumn(Column::string('type')->setComment('任务类型'))
            ->addColumn(Column::text('data')->setComment('号码数据'))
            ->addColumn(Column::integer('total')->setDefault(0)->setComment('号码总数'))
            ->addColumn(Column::timestamp('send_time')->setNullable()->setComment('定时发送时间'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0)->setComment('任务状态'))
            ->addTimestamps()
            ->create();
    }
}
