<?php

use think\migration\db\Column;
use think\migration\Migrator;

class AddChargePromoField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('charge')
            ->addColumn(Column::integer('coin')->setDefault(0)->setAfter('amount'))
            ->addColumn(Column::integer('money')->setDefault(0)->setAfter('coin'))
            ->addColumn(Column::integer('promo_id')->setNullable()->setAfter('money'))
            ->addColumn(Column::integer('discount')->setDefault(0)->setAfter('promo_id'))
            ->update();
    }
}
