<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateWxaWeappTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('wxa_weapp')
            ->addColumn(Column::integer('application_id')->setDefault(0))
            ->addColumn(Column::integer('user_id')->setDefault(0))
            ->addColumn(Column::string('app_id')->setNullable())
            ->addColumn(Column::string('refresh_token')->setNullable())
            ->addColumn(Column::string('nickname')->setNullable())
            ->addColumn(Column::string('avatar')->setNullable())
            ->addColumn(Column::string('signature')->setNullable())
            ->addColumn(Column::string('domain'))
            ->addColumn(Column::string('identity'))
            ->addColumn(Column::text('ext'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addTimestamps()
            ->addIndex('identity')
            ->addIndex('app_id')
            ->create();
    }
}
