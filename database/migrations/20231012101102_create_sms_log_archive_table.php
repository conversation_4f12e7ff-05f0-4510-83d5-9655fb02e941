<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSmsLogArchiveTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('sms_package_usage')
            ->setId(false)
            ->addColumn(Column::integer('package_id'))
            ->addColumn(Column::string('month'))
            ->addColumn(Column::integer('nums'))
            ->addIndex(['package_id', 'month'], ['unique' => true])
            ->create();

        $this->table('sms_package_amend')
            ->setId(false)
            ->addColumn(Column::integer('from_id'))
            ->addColumn(Column::integer('to_id'))
            ->addColumn(Column::integer('nums'))
            ->addIndex(['from_id', 'to_id'], ['unique' => true])
            ->create();
    }
}
