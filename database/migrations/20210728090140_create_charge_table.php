<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateChargeTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('charge')
             ->addColumn(Column::string('client_id')->setDefault(0))
             ->addColumn(Column::string('trade_no')->setUnique())
             ->addColumn(Column::string('order_no'))
             ->addColumn(Column::string('subject'))
             ->addColumn(Column::unsignedInteger('amount')->setDefault(0))
             ->addColumn(Column::text('extra')->setNullable())
             ->addColumn(Column::string('channel')->setNullable())
             ->addColumn(Column::string('out_trade_no')->setNullable())
             ->addColumn(Column::text('raw')->setNullable())
             ->addColumn(Column::boolean('is_paid')->setDefault(false))
             ->addColumn(Column::boolean('is_complete')->setDefault(false))
             ->addColumn(Column::timestamp('pay_time')->setNullable())
             ->addColumn(Column::string('notify_url')->setNullable())
             ->addColumn(Column::string('return_url')->setNullable())
             ->addTimestamps()
             ->create();
    }
}
