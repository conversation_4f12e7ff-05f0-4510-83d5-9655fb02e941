<?php

use app\lib\Date;
use think\migration\Migrator;
use think\migration\db\Column;

class CreateApiLogTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('api_log', [
            'primary_key' => ['id', 'create_time'],
        ])
            ->setId(false)
            ->addColumn(Column::integer('id')->setIdentity(true))
            ->addColumn(Column::integer('api_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('path'))
            ->addColumn(Column::boolean('success'))
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->addIndex('api_id')
            ->addIndex('user_id')
            ->create();

        $date     = Date::today()->endOfDay();
        $dateTime = $date->getTimestamp();
        $dataDate = $date->format('Ymd');

        $this->execute("ALTER TABLE `api_log` PARTITION BY RANGE (unix_timestamp(create_time))(
            PARTITION p{$dataDate} VALUES LESS THAN ({$dateTime}) ENGINE = InnoDB
)");
    }

    public function down()
    {
        $this->dropTable('api_log');
    }
}
