<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSmsPackageTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('sms_package')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::integer('total_nums'))
            ->addColumn(Column::integer('used_nums')->setDefault(0))
            ->addColumn(Column::integer('price')->setDefault(0))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->addTimestamps()
            ->addIndex(['user_id'])
            ->create();
    }
}
