<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSmsTemplateTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('sms_template')
            ->addColumn(Column::integer('user_id')->setDefault(0))
            ->addColumn(Column::integer('sign_id')->setDefault(0))
            ->addColumn(Column::string('out_id')->setNullable())
            ->addColumn(Column::string('name'))
            ->addColumn(Column::string('content'))
            ->addColumn(Column::string('remark')->setNullable())
            ->addColumn(Column::string('error')->setNullable())
            ->addColumn(Column::integer('status')->setDefault(0))
            ->addTimestamps()
            ->addSoftDelete()
            ->addIndex('user_id')
            ->addIndex('sign_id')
            ->create();
    }
}
