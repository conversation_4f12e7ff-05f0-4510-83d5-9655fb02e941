<?php

use think\migration\db\Column;
use think\migration\Migrator;

class CreatePartnerSettlementTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('partner_settlement')
            ->addColumn(Column::integer('partner_id'))
            ->addColumn(Column::tinyInteger('type'))
            ->addColumn(Column::unsignedInteger('amount'))
            ->addColumn(Column::string('invoice')->setNullable())
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addColumn(Column::string('remark')->setNullable())
            ->addTimestamps()
            ->create();
    }
}
