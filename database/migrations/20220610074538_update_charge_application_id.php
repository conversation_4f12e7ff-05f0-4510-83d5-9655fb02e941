<?php

use think\migration\Migrator;
use think\migration\db\Column;

class UpdateChargeApplicationId extends Mi<PERSON>tor
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        \app\model\Charge::chunk(10, function ($charges) {
            foreach ($charges as $charge) {
                if ($charge['client_id']) {
                    if (is_numeric($charge['client_id'])) {
                        $charge->save([
                            'application_id' => $charge['client_id'],
                        ]);
                    } else {
                        $application = \app\model\Application::where('client_id', $charge['client_id'])->find();
                        if ($application) {
                            $charge->save([
                                'application_id' => $application->id,
                            ]);
                        }
                    }
                }
            }
        });
    }
}
