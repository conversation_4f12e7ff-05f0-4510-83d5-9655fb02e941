<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSslOrderTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('ssl_order')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::integer('product_id'))
            ->addColumn(Column::string('brand'))
            ->addColumn(Column::string('type'))
            ->addColumn(Column::integer('one'))
            ->addColumn(Column::integer('wildcard'))
            ->addColumn(Column::integer('price'))
            ->addColumn(Column::integer('agency_price'))
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addColumn(Column::string('out_order_no')->setNullable())
            ->addTimestamps()
            ->create();
    }
}
