<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddChargeOrderIdField extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('charge')
            ->addColumn(Column::integer('order_id')->setAfter('order_no')->setNullable())
            ->addIndex('order_id')
            ->update();

        \app\model\Charge::where('application_id', 0)->chunk(50, function ($charges) {
            foreach ($charges as $charge) {
                $charge->order_id = \app\model\Order::where('order_no', $charge->order_no)->value('id');
                $charge->save();
            }
        });
    }

    public function down()
    {
        $this->table('charge')
            ->removeColumn('order_id')
            ->update();
    }
}
