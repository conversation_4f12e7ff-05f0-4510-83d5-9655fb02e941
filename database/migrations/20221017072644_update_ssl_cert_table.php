<?php

use think\migration\Migrator;
use think\migration\db\Column;

class UpdateSslCertTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('ssl_certificate')
            ->renameColumn('type', 'brand')
            ->changeColumn('certificate', Column::text('certificate')->setNullable())
            ->changeColumn('key', Column::text('key')->setNullable())
            ->changeColumn('domain', Column::string('domain')->setNullable())
            ->addColumn(Column::tinyInteger('status')->setDefault(1)->setAfter('domain'))
            ->update();

        $this->table('ssl_order')
            ->addColumn(Column::integer('cert_id')->setNullable()->setAfter('out_order_no'))
            ->update();
    }
}
