<?php

use think\migration\Migrator;
use think\migration\db\Column;

class UpdateAssistantSiteSession extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('assistant_site_session')
            ->removeColumn('hostname');

        $this->dropTable('assistant_site_event_data');

        $this->table('assistant_site_event')
            ->addColumn(Column::text('event_data')->setNullable()->setAfter('event_name'))
            ->addColumn(Column::string('url')->setAfter('event_data'))
            ->addColumn(Column::string('url_domain')->setAfter('url_path'))
            ->addColumn(Column::string('referrer')->setNullable()->setAfter('url_domain'))
            ->removeColumn('url_query')
            ->removeColumn('referrer_query')
            ->update();
    }
}
