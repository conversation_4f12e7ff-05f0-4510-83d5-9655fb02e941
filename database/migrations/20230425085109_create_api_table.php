<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateApiTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('api_category')
            ->addColumn(Column::string('name'))
            ->addColumn(Column::tinyInteger('sort')->setDefault(0))
            ->addTimestamps()
            ->create();

        $this->table('api')
            ->addColumn(Column::integer('category_id'))
            ->addColumn(Column::string('name')->setUnique())
            ->addColumn(Column::string('logo'))
            ->addColumn(Column::string('title'))
            ->addColumn(Column::string('description'))
            ->addColumn(Column::text('content'))
            ->addColumn(Column::tinyInteger('identify_type')->setDefault(0))
            ->addColumn(Column::tinyInteger('price_type')->setDefault(0))
            ->addColumn(Column::integer('trial_nums')->setDefault(0))
            ->addColumn(Column::timestamp('recommend_time')->setNullable())
            ->addColumn(Column::tinyInteger('status')->setDefault(0))
            ->addIndex(['category_id'])
            ->addIndex(['status'])
            ->addTimestamps()
            ->create();

        $this->table('api_package')
            ->addColumn(Column::integer('api_id'))
            ->addColumn(Column::integer('nums'))
            ->addColumn(Column::integer('price'))
            ->addColumn(Column::integer('original_price'))
            ->addColumn(Column::integer('agency_price'))
            ->addIndex(['api_id'])
            ->create();

        $this->table('api_user')
            ->setId(false)
            ->addColumn(Column::integer('api_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::integer('warn_nums')->setDefault(0))
            ->addIndex(['api_id', 'user_id'], ['unique' => true])
            ->addColumn(Column::timestamp('create_time')->setDefault('CURRENT_TIMESTAMP'))
            ->create();

        $this->table('api_vip')
            ->setId(false)
            ->addColumn(Column::integer('user_id')->setUnique())
            ->addColumn(Column::string('plan'))
            ->addColumn(Column::timestamp('expire_time')->setNullable())
            ->addTimestamps()
            ->create();

        $this->table('api_user_package')
            ->addColumn(Column::integer('api_id'))
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::string('name'))
            ->addColumn(Column::integer('total_nums'))
            ->addColumn(Column::integer('used_nums')->setDefault(0))
            ->addColumn(Column::integer('price')->setDefault(0))
            ->addColumn(Column::integer('agency_price')->setDefault(0))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->addTimestamps()
            ->addIndex(['api_id'])
            ->addIndex(['user_id'])
            ->create();
    }
}
