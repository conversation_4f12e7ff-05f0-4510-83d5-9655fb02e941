<?php

use think\migration\Migrator;
use think\migration\db\Column;

class AddOrderGoodsType extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function up()
    {
        $this->table('order')
            ->addColumn(Column::string('goods_type')->setAfter('amount'))
            ->update();

        \app\model\Order::chunk(10, function ($orders) {
            /** @var \app\model\Order $order */
            foreach ($orders as $order) {
                $order->isAutoWriteTimestamp(false)->save([
                    'goods_type' => get_class($order->goods),
                ]);
            }
        });
    }

    public function down()
    {
        $this->table('order')->removeColumn('goods_type');
    }
}
