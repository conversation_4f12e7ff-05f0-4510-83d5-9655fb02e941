<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateSmsTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('sms')
            ->addColumn(Column::integer('user_id'))
            ->addColumn(Column::integer('minute_limit')->setDefault(1))
            ->addColumn(Column::integer('hour_limit')->setDefault(5))
            ->addColumn(Column::integer('day_limit')->setDefault(10))
            ->addColumn(Column::integer('warn_nums')->setDefault(500))
            ->addColumn(Column::string('webhook')->setNullable())
            ->create();
    }
}
