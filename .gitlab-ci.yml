stages:
  - install
  - build
  - deploy

variables:
  KUBECONFIG: /etc/deploy/config

js:
  stage: install
  cache:
    key: "$CI_COMMIT_REF_NAME-asset"
    paths:
      - asset/.pnpm-store
      - asset/node_modules/
      - asset/main/node_modules/
      - asset/console/node_modules/
      - asset/notification/node_modules/
      - asset/assistant/node_modules/
      - asset/passport/node_modules/
      - asset/main/dist/
      - asset/console/dist/
      - asset/notification/dist/
      - asset/assistant/dist/
      - asset/passport/dist/
      - view/base.twig
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/node:18.18.2-buster
  before_script:
    - npm config set registry https://registry.npmmirror.com
    - npm install -g pnpm@8
  script:
    - cd asset
    - pnpm config set store-dir .pnpm-store
    - pnpm install
    - pnpm build
  only:
    changes:
      - asset/**/*
      - .gitlab-ci.yml

php:
  stage: install
  cache:
    key: "$CI_COMMIT_REF_NAME-php"
    paths:
      - vendor/
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/composer:latest
  script: composer install --optimize-autoloader --prefer-dist --no-dev --ignore-platform-reqs
  only:
    changes:
      - composer.json
      - composer.lock
      - .gitlab-ci.yml

build:
  stage: build
  image:
    name: registry-vpc.cn-shanghai.aliyuncs.com/topthink/executor:debug
    entrypoint: [""]
  before_script:
    - echo "{\"auths\":{\"${CI_REGISTRY_URL}\":{\"auth\":\"$(printf "%s:%s" "${CI_REGISTRY_USER}" "${CI_REGISTRY_PASSWORD}" | base64 | tr -d '\n')\"},\"$CI_DEPENDENCY_PROXY_SERVER\":{\"auth\":\"$(printf "%s:%s" ${CI_DEPENDENCY_PROXY_USER} "${CI_DEPENDENCY_PROXY_PASSWORD}" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - sed -i 's/registry.cn-shanghai.aliyuncs.com/registry-vpc.cn-shanghai.aliyuncs.com/g' ${CI_PROJECT_DIR}/Dockerfile
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "registry-vpc.cn-shanghai.aliyuncs.com/topthink/core:latest"
      --destination "registry-vpc.cn-shanghai.aliyuncs.com/topthink/core:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}"
  cache:
    - key: "$CI_COMMIT_REF_NAME-php"
      policy: pull
      paths:
        - vendor/
    - key: "$CI_COMMIT_REF_NAME-asset"
      policy: pull
      paths:
        - asset/main/dist/
        - asset/console/dist/
        - asset/notification/dist/
        - asset/assistant/dist/
        - asset/passport/dist/
        - view/base.twig

deploy:
  image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/helm-kubectl-docker:latest
  stage: deploy
  environment:
    name: production
    url: https://www.topthink.com
  script:
    - mkdir -p /etc/deploy
    - echo $CI_KUBE_CONFIG | base64 -d > $KUBECONFIG
    - sed -i "s/IMAGE_TAG/$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA/g" deployment.yaml
    - cat deployment.yaml
    - kubectl apply -f deployment.yaml
