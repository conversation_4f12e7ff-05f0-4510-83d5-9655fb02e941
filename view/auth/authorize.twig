{% extends "layout.twig" %}
{% block header %}
    {% embed "header.twig" %}
        {% block nav %}
            <ul class='me-auto'></ul>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block main %}
    {% embed "./components/page.twig" %}
        {% block title %}
            应用授权
        {% endblock %}
        {% block body %}
            <div class="row justify-content-center">
                <div class="col-lg-6">
                    <form data-bs-form method='post'>
                        {% embed "./components/card.twig" %}
                            {% block body %}
                                <h5 class='mb-3'>
                                    Hi {{ user.name }}，<strong>{{ app }}</strong> 将会获得以下权限
                                </h5>
                                <p class='d-flex justify-content-end'>
                                    <a href='' class='btn btn-light'><i class="bi bi-arrow-repeat"></i></a>
                                </p>
                                <ul class="list-group mb-5">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        用户基本信息
                                        <i class="bi bi-check-circle-fill text-success"></i>
                                    </li>
                                    {% set passed = true %}
                                    {% for scope in scopes %}
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            {% if scope.resolved(user) %}
                                                <span>{{ scope.getTitle() }}</span>
                                                <i class="bi bi-check-circle-fill text-success"></i>
                                            {% else %}
                                                {% set passed = false %}
                                                <span class='text-muted'>{{ scope.getTitle() }}</span>
                                                <span class='d-flex align-items-center'>
                                                    <a class='me-2' target='_blank'
                                                       href='{{ scope.resolveUrl() }}'>去设置</a>
                                                    <i class="bi bi-exclamation-circle-fill text-danger"></i>
                                                </span>
                                            {% endif %}
                                        </li>
                                    {% endfor %}
                                </ul>
                                <button type='submit' {{ not passed?'disabled' }} class='btn btn-primary'>
                                    确认授权
                                </button>
                            {% endblock %}
                        {% endembed %}
                    </form>
                </div>
            </div>
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block footer %}
{% endblock %}
{% block scripts %}

{% endblock %}
