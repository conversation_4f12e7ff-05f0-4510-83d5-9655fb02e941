{% extends "layout.twig" %}
{% block page_title %}
    {{ api.title }} - ThinkAPI统一接口服务
{% endblock %}
{% block main %}
    {% embed "./components/page.twig" %}
        {% block body %}
            {% embed "./components/card.twig" %}
                {% block body %}
                    <div class='row'>
                        <div class='col-md-3'>
                            <div class='border rounded border-1 p-4 d-flex flex-column align-items-center'>
                                <img class='p-3' src='{{ api.logo }}' width='210' height='210' />
                            </div>
                        </div>
                        <div class='col-md-9 '>
                            <h3>{{ api.title }}</h3>
                            <div class='mb-3'>{{ api.description }}</div>
                            <div class='mb-4 d-flex gap-2'>
                                {% if api.price_type == 1 %}
                                    <span class="badge rounded fw-normal text-primary bg-primary bg-opacity-25">
                                        <i class="bi bi-info-circle me-1"></i>限会员用户使用
                                    </span>
                                {% endif %}
                                {% if api.identify_type == 1 %}
                                    <span class="badge rounded fw-normal text-primary bg-primary bg-opacity-25">
                                        <i class="bi bi-info-circle me-1"></i>限个人实名用户使用
                                    </span>
                                {% elseif api.identify_type == 2 %}
                                    <span class="badge rounded fw-normal text-primary bg-primary bg-opacity-25">
                                        <i class="bi bi-info-circle me-1"></i>限企业实名用户使用
                                    </span>
                                {% endif %}
                            </div>
                            {% if api.status == 1 %}
                                <div class='mb-4 d-flex gap-2' data-bs-packages>
                                    {% if api.price_type == 2 %}
                                        {% for package in api.packages %}
                                            <div class="border rounded px-4 py-2" role='button' data-nums='{{ package.nums }}' data-price='{{ package.price }}' data-url='{{ package.buy_url }}'>{{ package.nums }}次</div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="border rounded px-4 py-2" role='button'>50次/天</div>
                                        <div class="border rounded px-4 py-2 position-relative" role='button'>
                                            100次/分钟
                                            <span class="position-absolute top-0 start-0 translate-middle-y badge bg-danger">
                                                高级会员
                                            </span>
                                        </div>
                                        <div class="border rounded px-4 py-2 position-relative" role='button'>
                                            1000次/分钟
                                            <span class="position-absolute top-0 start-0 translate-middle-y badge bg-danger">
                                                黄金会员
                                            </span>
                                        </div>
                                    {% endif %}
                                </div>
                                {% if api.price_type == 2 %}
                                    <div class='mb-3 d-flex align-items-center' data-bs-price-target></div>
                                {% endif %}
                                {% if applied %}
                                    {% if api.price_type == 2 %}
                                        <div class='mt-4'>
                                            <a class='btn rounded-pill btn-lg btn-primary px-4' data-bs-url-target>购买套餐</a>
                                        </div>
                                    {% else %}
                                        <div class='mt-4'>
                                            <a class='btn rounded-pill btn-lg btn-primary px-4' href='{{ console_url('/api/vip') }}'>升级会员</a>
                                        </div>
                                    {% endif %}
                                {% else %}
                                    <div class='mt-4'>
                                        <a class='btn rounded-pill btn-lg btn-primary px-4' href='{{ api.apply_url }}'>申请接口</a>
                                    </div>
                                {% endif %}
                            {% else %}
                                <div>
                                    <button class='btn rounded-pill btn-lg btn-secondary px-5' disabled>未上架</button>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endblock %}
            {% endembed %}
            {% embed "./components/card.twig" %}
                {% block body %}
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" data-bs-target='#content' role='button'>产品功能</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" data-bs-target='#doc' role='button'>API文档</a>
                        </li>
                    </ul>
                    <div class="tab-content pt-3">
                        <div class="tab-pane active" id='content'>
                            <div class='markdown-content'>
                                {{ api.content|markdown|raw }}
                            </div>
                        </div>
                        <div class="tab-pane" id='doc'>
                            <div class="row">
                                <div class='col-2 border-end'>
                                    <ul class="nav flex-column">
                                        {% for item in info %}
                                            <li class="nav-item w-100">
                                                <a class="nav-link px-0 d-flex align-items-center justify-content-between{{ loop.first?' active' }}" data-bs-toggle="tab" data-bs-target="#api-item-{{ loop.index }}" role="button">
                                                    <span class='text-truncate'>{{ item.title }}</span>
                                                </a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                <div class="col-9">
                                    <div class='tab-content'>
                                        {% for item in info %}
                                            <div class="tab-pane{{ loop.first?' active' }}" id="api-item-{{ loop.index }}">
                                                <div class='d-flex align-items-center mb-4'>
                                                    <h5 class='mb-0'>{{ item.desc }}</h5>
                                                    {% if not item.charge %}
                                                        <span class="badge bg-success ms-2">不计次</span>
                                                    {% endif %}
                                                </div>
                                                <p>
                                                    <strong>调用地址：</strong>
                                                    <span class='text-primary'>{{ item.url }}</span>
                                                </p>
                                                <p>
                                                    <strong>请求方式：</strong>{{ item.method }}
                                                </p>
                                                <p>
                                                    <strong>返回类型：</strong>JSON
                                                </p>
                                                <p>
                                                    <strong>API 调用：</strong>
                                                    <a class='link-primary' href='https://doc.topthink.com/think-api/APIdiaoyong.html' target='_blank'>调用说明</a>
                                                </p>
                                                <div class="accordion">
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header">
                                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#api-item-{{ loop.index }}-arg">
                                                                请求参数说明
                                                            </button>
                                                        </h2>
                                                        <div id="api-item-{{ loop.index }}-arg" class="accordion-collapse collapse show">
                                                            <div class="accordion-body">
                                                                {% if item.arg is not empty %}
                                                                    <table class='table mb-0'>
                                                                        <thead>
                                                                        <tr>
                                                                            <th>名称</th>
                                                                            <th>必填</th>
                                                                            <th>类型</th>
                                                                            <th>说明</th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        {% for arg in item.arg %}
                                                                            <tr>
                                                                                <td>{{ arg.name }}</td>
                                                                                <td>{{ arg.required?'是':'否' }}</td>
                                                                                <td>{{ arg.type }}</td>
                                                                                <td>{{ arg.desc }}</td>
                                                                            </tr>
                                                                        {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                {% else %}
                                                                    无参数
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="accordion-item">
                                                        <h2 class="accordion-header">
                                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#api-item-{{ loop.index }}-sdk">
                                                                SDK调用
                                                            </button>
                                                        </h2>
                                                        <div id="api-item-{{ loop.index }}-sdk" class="accordion-collapse collapse show">
                                                            <div class="accordion-body">
                                                                <pre class='mb-0 rounded bg-light'><code class="language-php">{{ item.sdk }}</code></pre>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% if item.res is not empty %}
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#api-item-{{ loop.index }}-res">
                                                                    返回参数说明
                                                                </button>
                                                            </h2>
                                                            <div id="api-item-{{ loop.index }}-res" class="accordion-collapse collapse show">
                                                                <div class="accordion-body">
                                                                    <table class='table mb-0'>
                                                                        <thead>
                                                                        <tr>
                                                                            <th>名称</th>
                                                                            <th>类型</th>
                                                                            <th>说明</th>
                                                                        </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                        {% for res in item.res %}
                                                                            <tr>
                                                                                <td>{{ res.name }}</td>
                                                                                <td>{{ res.type }}</td>
                                                                                <td>{{ res.desc|nl2br }}</td>
                                                                            </tr>
                                                                        {% endfor %}
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                    {% if item.success is not empty %}
                                                        <div class="accordion-item">
                                                            <h2 class="accordion-header">
                                                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#api-item-{{ loop.index }}-example">
                                                                    返回参数示例
                                                                </button>
                                                            </h2>
                                                            <div id="api-item-{{ loop.index }}-example" class="accordion-collapse collapse show">
                                                                <div class="accordion-body">
                                                                    <pre class='mb-0 rounded bg-light'><code class="language-json">{{ item.success }}</code></pre>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endblock %}
            {% endembed %}
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <link rel="stylesheet" href="https://jsdelivr.topthink.com/npm/prismjs@1.29.0/themes/prism-solarizedlight.min.css" />
    <script src="https://jsdelivr.topthink.com/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://jsdelivr.topthink.com/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script type='text/javascript'>
        window.import("api");
    </script>
{% endblock %}
