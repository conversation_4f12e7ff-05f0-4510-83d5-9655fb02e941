{% extends "layout.twig" %}
{% set page_class = 'cashier-page' %}
{% block header %}
    {% embed "header.twig" %}
        {% block title %}
            <span class='navbar-brand me-auto'>收银台</span>
        {% endblock %}
        {% block navbar %}
        {% endblock %}
        {% block user %}
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block main %}
    {% embed "./components/page.twig" %}
        {% block title %}
            支付
        {% endblock %}
        {% block body %}
            {% if charge.status == 1 %}
                {% embed "./components/result.twig" only %}
                    {% block icon %}
                        <i class="bi bi-check-circle-fill text-success"></i>
                    {% endblock %}
                    {% block message %}
                        已支付完成
                    {% endblock %}
                {% endembed %}
            {% elseif charge.status == 2 %}
                {% embed "./components/result.twig" only %}
                    {% block icon %}
                        <i class="bi bi-info-circle text-info"></i>
                    {% endblock %}
                    {% block message %}
                        订单处理中，请稍后
                    {% endblock %}
                {% endembed %}
            {% elseif charge.channel == 'transfer' and charge.raw %}
                {% embed "./components/result.twig" %}
                    {% block icon %}
                        <i class="bi bi-info-circle-fill text-primary"></i>
                    {% endblock %}
                    {% block message %}
                        对公转账确认中
                    {% endblock %}
                    {% block extra %}
                        <div class='row mt-3'>
                            <div class='col-4 offset-4'>
                                <div class="alert alert-primary">
                                    <h5 class='mb-3'>转账信息</h5>
                                    <dl class='row mb-0'>
                                        <dt class='col-3'>公司名</dt>
                                        <dd class='col-9'>{{ charge.raw.company }}</dd>
                                        <dt class='col-3'>开户行</dt>
                                        <dd class='col-9'>{{ charge.raw.bank }}</dd>
                                        <dt class='col-3'>账号</dt>
                                        <dd class='col-9'>{{ charge.raw.account }}</dd>
                                        <dt class='col-3'>订单号</dt>
                                        <dd class='col-9'>{{ charge.raw.trade_no }}（转账时备注）</dd>
                                    </dl>
                                </div>
                                <div class='alert alert-warning d-flex align-items-center mb-0'>
                                    <i class="bi bi-exclamation-circle-fill me-2"></i> 转账完成后可联系客服确认
                                </div>
                            </div>
                        </div>
                    {% endblock %}
                {% endembed %}
            {% else %}
                <form data-bs-cashier-form method='post' action='/cashier' novalidate>
                    <input type='hidden' name='id' value='{{ charge.hash_id }}' />
                    {% embed "./components/card.twig" %}
                        {% block title %}
                            订单信息
                        {% endblock %}
                        {% block body %}
                            <dl class='row'>
                                <dt class='col-4 col-md-2 col-lg-1'>支付单号</dt>
                                <dd class='col-8 col-md-10 col-lg-11 fs-5'>{{ charge.trade_no }}</dd>
                            </dl>
                            <dl class='row'>
                                <dt class='col-4 col-md-2 col-lg-1'>商品单号</dt>
                                <dd class='col-8 col-md-10 col-lg-11'>{{ charge.order_no }}</dd>
                            </dl>
                            <dl class='row'>
                                <dt class='col-4 col-md-2 col-lg-1'>商品名称</dt>
                                <dd class='col-8 col-md-10 col-lg-11'>{{ charge.subject }}</dd>
                            </dl>
                            <dl class='row mb-0'>
                                <dt class='col-4 col-md-2 col-lg-1'>商品金额</dt>
                                <dd class='col-8 col-md-10 col-lg-11 text-success h3'>{{ (charge.amount/100)|format_currency('CNY', locale='zh') }}</dd>
                            </dl>
                        {% endblock %}
                    {% endembed %}
                    {% embed "./components/card.twig" %}
                        {% block title %}
                            支付明细
                        {% endblock %}
                        {% block body %}
                            {% if charge.channel %}
                                {% if charge.discount>0 %}
                                    <dl class='row'>
                                        <dt class='col-4 col-md-2 col-lg-1'>优惠码</dt>
                                        <dd class='col-8 col-md-10 col-lg-11'>
                                            使用优惠码抵扣 <span class='text-orange'>{{ (charge.discount/100)|format_currency('CNY', locale='zh') }}</span> 元
                                        </dd>
                                    </dl>
                                {% endif %}
                                {% if charge.coin>0 %}
                                    <dl class='row'>
                                        <dt class='col-4 col-md-2 col-lg-1'>云币支付</dt>
                                        <dd class='col-8 col-md-10 col-lg-11'>
                                            使用云币抵扣 <span class='text-orange'>{{ (charge.coin/100)|format_currency('CNY', locale='zh') }}</span> 元
                                        </dd>
                                    </dl>
                                {% endif %}
                                {% if charge.money>0 %}
                                    <dl class='row'>
                                        <dt class='col-4 col-md-2 col-lg-1'>余额支付</dt>
                                        <dd class='col-8 col-md-10 col-lg-11'>
                                            使用余额抵扣 <span class='text-orange'>{{ (charge.money/100)|format_currency('CNY', locale='zh') }}</span> 元
                                        </dd>
                                    </dl>
                                {% endif %}
                            {% else %}
                                <dl class='row' data-bs-promo>
                                    <dt class='col-4 col-md-2 col-lg-1'>优惠码</dt>
                                    <dd class='col-8 col-md-10 col-lg-11'></dd>
                                </dl>
                                {% if user %}
                                    {% if user.coin>0 %}
                                        <dl class='row' data-bs-coin='{{ user.coin }}'>
                                            <dt class='col-4 col-md-2 col-lg-1'>云币支付</dt>
                                            <dd class='col-8 col-md-10 col-lg-11'>
                                                <div class="form-check mb-0">
                                                    <input class="form-check-input" type="checkbox" value="" id="use-coin" />
                                                    <label class="form-check-label" for="use-coin">
                                                        使用云币抵扣（当前云币 <span class='text-orange'>{{ user.coin/100 }}</span>）
                                                        <input disabled class='money-input' type='number' />元
                                                    </label>
                                                </div>
                                            </dd>
                                        </dl>
                                    {% endif %}
                                    {% if user.money>0 %}
                                        <dl class='row' data-bs-money='{{ user.money }}'>
                                            <dt class='col-4 col-md-2 col-lg-1'>余额支付</dt>
                                            <dd class='col-8 col-md-10 col-lg-11'>
                                                <div class="form-check mb-0">
                                                    <input class="form-check-input" type="checkbox" value="" id="use-money" />
                                                    <label class="form-check-label" for="use-money">
                                                        使用余额抵扣（当前余额 <span class='text-orange'>{{ (user.money/100)|format_currency('CNY', locale='zh') }}</span>）
                                                        <input disabled class='money-input' type='number' />元
                                                    </label>
                                                </div>
                                            </dd>
                                        </dl>
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                            <dl class='row mb-0'>
                                <dt class='col-4 col-md-2 col-lg-1'>还需支付</dt>
                                <dd class='col-8 col-md-10 col-lg-11 text-danger h3' data-bs-amount>
                                    {{ (charge.amount/100)|format_currency('CNY', locale='zh') }}
                                </dd>
                            </dl>
                        {% endblock %}
                    {% endembed %}
                    <div data-bs-channel>
                        {% embed "./components/card.twig" %}
                            {% block title %}
                                支付方式
                            {% endblock %}
                            {% block body %}
                                <div class='d-flex'>
                                    {% if 'alipay' in channels %}
                                        <div class='d-flex align-items-center me-3'>
                                            <div class="form-check">
                                                <input name='channel' class="form-check-input" type="radio" id="alipay" value="alipay" />
                                            </div>
                                            <label class="form-check-label" for="alipay">
                                                <i class="iconfont icon-zhifubao text-primary h3"></i>
                                                支付宝
                                            </label>
                                        </div>
                                    {% endif %}
                                    {% if 'wechat' in channels %}
                                        <div class='d-flex align-items-center me-3'>
                                            <div class="form-check">
                                                <input name='channel' class="form-check-input" type="radio" id="wechat" value="wechat" />
                                            </div>
                                            <label class="form-check-label" for="wechat">
                                                <i class="iconfont icon-weixinzhifu text-success h3"></i>
                                                微信支付
                                            </label>
                                        </div>
                                    {% endif %}
                                    {% if 'transfer' in channels %}
                                        <div class='d-flex align-items-center'>
                                            <div class="form-check">
                                                <input name='channel' class="form-check-input" type="radio" id="transfer" value="transfer" />
                                            </div>
                                            <label class="form-check-label" for="transfer">
                                                <i class="iconfont icon-zhuanzhangshoukuan text-warning h3"></i>
                                                对公转账
                                            </label>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endblock %}
                        {% endembed %}
                    </div>
                    {% embed "./components/card.twig" only %}
                        {% block body %}
                            <button type='submit' class='btn btn-primary'>确认支付</button>
                        {% endblock %}
                    {% endembed %}
                </form>
            {% endif %}
        {% endblock %}
    {% endembed %}
{% endblock %}
{% block scripts %}
    <script type='text/javascript'>
        window.import("cashier", {
            id             : "{{ charge.hash_id }}",
            channel        : "{{ charge.channel }}",
            amount         : {{ charge.amount }},
            coin           : {{ charge.coin }},
            money          : {{ charge.money }},
            discount       : {{ charge.discount }},
            status         : {{ charge.status }},
            waitingTransfer: {{ charge.channel == 'transfer' and charge.raw ? 'true' : 'false' }},
        });
    </script>
{% endblock %}
