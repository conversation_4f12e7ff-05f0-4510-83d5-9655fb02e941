{% extends "product/layout.twig" %}
{% block page_title %}
    ThinkAPI统一接口服务
{% endblock %}
{% block banner %}
    <div class='banner-content'>
        <h1>ThinkAPI统一接口服务</h1>
        <p>官方一站式接口服务，涵盖实名认证、人工智能、电子商务、新闻资讯和生活服务等，提供优雅SDK和调用统计</p>
        <ul>
            <li><a data-tas-doc='center' href='https://doc.topthink.com/think-api/default.html'>快速了解</a></li>
            <li><a href='https://console.topthink.com/api/'> 申请接口 <i class="bi bi-arrow-right"></i></a></li>
        </ul>
    </div>
    <div class='banner-image'>
        <div class='shape shape-1'>
            {{ source('svg/product/dotted-shape.svg') }}
        </div>
        <div class='shape shape-2'>
            {{ source('svg/product/dotted-shape.svg') }}
        </div>
        <div class='image'>
            <img src='https://www.thinkphp.cn/uploads/images/20230519/a174ffd05f98f8bbd46d09063fa5bfe7.png' width='845px' />
        </div>
    </div>    
{% endblock %}
{% block features %}
    <div class='features-title'>
        <span>Features</span>
        <h2>服务优势</h2>
        <p></p>
    </div>
    <div class='row gx-0 gx-md-5'>
        <div class='col-12 col-md-3'>
            <div class='single-feature'>
                <div class='feature-icon'>
                    <i class="bi bi-clouds"></i>
                </div>
                <div class='feature-content'>
                    <h3>一站式接口服务</h3>
                    <p>ThinkAPI提供了一站式的接口服务，从接口购买、调用到SDK封装都只需要一个账号即可完成，无需注册各种接口服务账号，除了涵盖常用API服务之外，还设计了可转换接口服务更好的保障接口的稳定性，支持IP白名单和接口预警功能，让你省心省力。</p>
                </div>
            </div>
        </div>
        <div class='col-12 col-md-3'>
            <div class='single-feature'>
                <div class='feature-icon'>
                    <i class="bi bi-layout-text-window-reverse"></i>
                </div>
                <div class='feature-content'>
                    <h3>极低的调用成本</h3>
                    <p>ThinkAPI提供了极低的使用门槛和接口价格，付费接口提供了免费试用次数，单次成本非常低廉，低于市场同类价格，购买的接口数量没有时间限制。免费接口则提供了每天不超过100次的免费调用方式，很好的解决了开发者接入成本高的问题。会员在有效期内可以支持所有免费接口和会员接口的不限次调用。</p>
                </div>
            </div>
        </div>
        <div class='col-12 col-md-3'>
            <div class='single-feature'>
                <div class='feature-icon'>
                    <i class="bi bi-people"></i>
                </div>
                <div class='feature-content'>
                    <h3>统一的数据格式</h3>
                    <p>统一API服务接入了各大API厂商的接口，但采用统一的返回数据格式和统一的接口请求域名（支持HTTPS），更有利于开发者对接口的统一调用。</p>
                </div>
            </div>
        </div>
        <div class='col-12 col-md-3'>
            <div class='single-feature'>
                <div class='feature-icon'>
                    <i class="bi bi-layers"></i>
                </div>
                <div class='feature-content'>
                    <h3>提供优雅的SDK</h3>
                    <p>官方提供了一套统一的SDK for PHP调用服务，通过composer安装一个轻量级的依赖包即可调用，采用简洁和现代化的调用方式，简化你的API调用开发工作。</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
{% block about %}
    <span class="tag">关于我们</span>
    <h3>稳定可靠的接口服务</h3>
    <p>官方甄选稳定可靠的服务商并致力于提供可靠的接口服务，目前ThinkAPI的主力供应商为国内知名数据提供商聚合数据、创蓝（互联网百强企业），有足够的能力提供稳定的接口服务。</p>
    <p>目前已经接入包括实名认证、人工智能、电子商务、新闻资讯和生活服务及短信在内的常用API接口共270个，其中会员接口127个，并且还在陆续扩充中。</p>
    <a class='btn btn-primary btn-lg' href='https://doc.topthink.com/think-api'>查看更多</a>
{% endblock %}
