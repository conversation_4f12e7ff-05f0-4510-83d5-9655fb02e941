{% extends "layout.twig" %}
{% set page_class = 'product-page' %}
{% block page_body %}
    <div class='banner'>
        <div class='container'>
            {% block banner %}{% endblock %}
        </div>
    </div>
    <div class='features'>
        <div class='container'>
            {% block features %}{% endblock %}
        </div>
    </div>
    <div class='about'>
        <div class='container'>
            <div class='about-wrap'>
                <div class='about-content'>
                    {% block about %}{% endblock %}
                </div>
                <div class='about-image'>
                    {{ source('svg/home/<USER>') }}
                </div>
            </div>
        </div>
    </div>
    {% set block_pricing = block('pricing')??null %}
    {% if block_pricing is not same as null %}
        <div class='pricing'>
            <div class='container'>
                {{ block_pricing|raw }}
            </div>
        </div>
    {% endif %}
{% endblock %}
