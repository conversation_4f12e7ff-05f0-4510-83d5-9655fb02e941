{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "topthink/framework": "8.x-dev", "topthink/think-orm": "3.0.x-dev", "topthink/think-swoole": "4.0.x-dev", "topthink/think-migration": "^3.1.x-dev", "firebase/php-jwt": "^6.1", "yunwuxin/think-auth": "4.0.x-dev", "yunwuxin/think-twig": "^3.0", "nesbot/carbon": "^2.46", "yunwuxin/think-notification": "^3.1", "yunwuxin/think-mail": "^4.0", "tencentcloud/captcha": "^3.0", "topthink/think-api": "^1.0", "yunwuxin/think-throttle": "^1.0", "yunwuxin/think-pay": "^3.2", "yunwuxin/think-social": "^3.1", "hashids/hashids": "^4.1", "yunwuxin/think-oauth-server": "^1.0", "twig/intl-extra": "^3.3", "spatie/ssl-certificate": "^2.1", "thenorthmemory/xml": "^1.0", "avram/robohash": "^1.0", "spatie/url": "^2.2", "topthink/think-filesystem": "^1.0", "cebe/markdown": "^1.2", "league/commonmark": "^2.4", "topthink/think-annotation": "^2.0", "ramsey/uuid": "^3.5", "yunwuxin/think-cron": "^3.0", "topthink/think-tracing": "^1.0", "jcchavezs/zipkin-opentracing": "^2.0", "maxmind-db/reader": "^1.11", "jenssegers/agent": "^2.6", "longlang/phpkafka": "^1.2", "smi2/phpclickhouse": "^1.5", "hkulekci/qdrant": "^0.4.1", "topthink/think-queue": "^3.0.x-dev", "league/html-to-markdown": "^5.1", "phpoffice/phpspreadsheet": "^1.29", "topthink/think-cors": "^1.0"}, "require-dev": {"topthink/think-ide-helper": "^1.0", "topthink/think-dumper": "^1.0", "pestphp/pest": "^1.23"}, "autoload": {"psr-4": {"app\\": "app"}}, "config": {"preferred-install": "dist", "platform-check": false, "platform": {"ext-swoole": "5.0.0", "ext-fileinfo": "1.0.4", "ext-intl": "1", "ext-sodium": "1", "ext-zip": "1", "ext-bcmath": "1", "ext-gd": "1"}, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}