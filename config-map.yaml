apiVersion: v1
kind: ConfigMap
metadata:
  name: common-config
  namespace: topthink
data:
  redis-host: ""
  smtp-host: ""
  smtp-port: ""
  smtp-user: ""
  smtp-pass: ""
  proxy: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: db-config
  namespace: topthink
data:
  host: ""
  password: ""
  user: ""
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: core-config
  namespace: topthink
data:
  app-admin: ''
  cloud-client-id: ''
  cloud-client-secret: ''
