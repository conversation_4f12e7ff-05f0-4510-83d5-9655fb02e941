# main
server {
    listen  80;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;
    client_max_body_size 20m;

    location /uploads/ {
        root  /opt/htdocs/storage;
        try_files $uri =404;
    }

    location / {
        root  /opt/htdocs/asset/main/dist;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Headers X-Requested-With;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS;
        try_files $uri @swoole;
    }

    location /console/ {
        return 404;
    }

    location /ai/ {
        return 404;
    }

    location @swoole {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# console
server {
    listen  81;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;

    client_max_body_size 20m;

    root  /opt/htdocs/asset/console/dist;

    location /asset/ {
        try_files $uri =404;
    }

    location /uploads/ {
        root  /opt/htdocs/storage;
        try_files $uri =404;
    }

    location / {
        try_files $uri /index.html;
    }

    location /data/ {
        proxy_pass http://127.0.0.1:8080/console/;
        proxy_http_version 1.1;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
