<?php
// +----------------------------------------------------------------------
// | ThinkPay
// +----------------------------------------------------------------------
// | Copyright (c) yunwuxin All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'sandbox'    => false,//沙箱模式
    'charge'     => 'app\\model\\Charge',
    'channels'   => [
        'alipay' => [
            'type'                  => 'alipay',
            'app_id'                => '2021002156615734',
            'app_private_key'       => 'MIIEowIBAAKCAQEA2IjAVt2BYr0jeNrKb9F/uDD5W29LQBvCvrawY4nM311pVKJvIHQ5PyChoOQJ7CUO3SVrzFd1YYg8Se33OWHHvGCryAtxfqw7vOtT8Rqox6YHJDiH55e3VSJriKhHD3hyHLkai50jqH+WnY8JdzpbcWAfzbo2bhZelMVtzhXuBpCqFUO66Ep+6rsIVKI1GWErI3dbCG+D1EHWmg+593X3ktNTxgcK6Dt4C/XrGwT4Hf5Nrs4+HJKziXdB7eJbPoPIqxgLxuGjTB6BAPmHVgvuAeVsXxQJkkXyNaAPJJnJU1j1/bbUm9gqc90vkeFs6Dt0kTJVFqp9kMK0jweO4Pe6+wIDAQABAoIBAECfd3+1NdMpDXgZHAeJJ6vp+dF/Ap9Maa+PSE88pgcXksLug2nVJeygKXyh8hAm8yuu6mAezepgxVVvDrRyaTDgTQQz3hhnAKEePnwbb7m95C28LVtB3TYsQpmTVznXwX6vA6wdJrI3+dRmBvYtVXMdtAArDemHBY0y6tVxmAse/uNxWKVF6iZhE+vYw2vPZH4pt+4ZmyMCjQArPT3U++2E5U/fkmLe+J7ZwenqsBkeOdnuZ+k8uD3f2X99UYb/vtuartmiumrSyWJH6LlDTD0mTDtxOufXb2iRXDDlqWaNIjjMdpsIs7oXeX6qlWxLPeK0pkyQdh6TGKZ7X9WKUSECgYEA9sTRR9hlecXgTPuCnoZWJJc8ATur+BCTpX1OPTQuZ/h7RNVmpiIhhC8bVqpx0dh0EBZe63qXSv5zw8NJ8aNTY7b3JaMz3Z+7MEZf6in6oNDyJvjb9jfRm+hQDKJ0vjmRpSorfpJbSBht4EGip+LpOFk2jv8HlT/79o/ckDRJvzECgYEA4KJkSVPX4v3wY7iOeKjP1b8GpEobPItJJc60kEOO2DmYZ3YQ/dN/2qFVwoC34DywF989p1IYFEit7YG3K6dYQ5zEoafXBtjwWv1PD6tDKZxLxW93dYsI7cNmpKWJyE6IIH2zQ51pRHwxZJ6vJa6Fob9nfjGe0JyR0m7/JNQ0iesCgYAEQTyCip6lTN4jNar45MQTeXAVbVv3uQFytwhkUcQDEuu9MhjFAI2nY9CK3AF+ZscJh/k3AjQdnOmOFI3v1qsXaw4z3e6FAb4OhQgeV370yuWB+oSAaFH66DSJIzv0E7YJLQL2+bsNMS6j0T3muyjeSduA97C/yAymIbDh46Tp4QKBgH6SIc3LZ0ZobU3TDLvh6pF2fEiP1P7tQH8EFGuBFXiyIzSQqwxKYq0O4OKiYnj/2PzqdbPUpuqu9QlxYINBXJNkPssTZi5eiDfMqQKcLlDA11Tc9MOPXq2Rv0EgYR0zWrJsAVkk1c6X58DZRLkHuBYmYisRzHmg7u1SS4M16pCXAoGBAOC+M+KLJ4+KhXM63LFcjuKT5jcfL2HVCwGEA2F9RS1D7slamnn/gXz3996JXMfsmwEuPUQNKYptD1TeoArThI1lqWpPIFknULzpS8ypXQer21xTcWZE0N77ntaknD8fHMgwNeXdyRjMZcStfvokabmZ6yqZ8Q7BgvjuDWDLeGob',//应用私钥
            //以下为公钥模式参数
            'alipay_public_key'     => '', //支付宝公钥
            //以下为公钥证书模式参数
            'app_cert_path'         => config_path('certs') . 'appCertPublicKey_2021002156615734.crt',//硬要公钥证书路径
            'alipay_cert_path'      => config_path('certs') . 'alipayCertPublicKey_RSA2.crt',//支付宝公钥证书路径
            'alipay_root_cert_path' => config_path('certs') . 'alipayRootCert.crt',//支付宝根证书路径
        ],
        'wechat' => [
            'type'    => 'wechat',
            'key'     => 'kanwx8d25684c7afe6ef7cloud7m8din',
            'app_id'  => 'wxf3347576c862bab6',
            'mch_id'  => '1235972102',
            'cert'    => 'MIID8DCCAtigAwIBAgIUSn0nXrSVw1xSB2/r3OIgOH75U4cwDQYJKoZIhvcNAQELBQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsTFFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3QgQ0EwHhcNMjEwMTE1MDUyNDMwWhcNMjYwMTE0MDUyNDMwWjCBgTETMBEGA1UEAwwKMTIzNTk3MjEwMjEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMS0wKwYDVQQLDCTkuIrmtbfpobbmg7Pkv6Hmga/np5HmioDmnInpmZDlhazlj7gxCzAJBgNVBAYMAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALhP0kPbh8PoR66izHnnWkKdpFw0R6NsGg4hhcmnVtCLAjEMsBKRJay82RqWQFWDLvGeCsUY6aPSMgO1vrAU3yK4PGC9BKseyfTyeFfGoMpUcZaF32EanYr778yMiB6CchE2fv3mcTXNbbr9PURZYr9Qmb/ACuFXeMxmWgUIJSw9u2+jNwddR1B3w28IOTMkAgsAWcBt+IDFIvm/9uFpjWSQhP1cpWOVvjDNzDnvSdfEWzPEOfiSUJyJWUu2ZQJoM52e+ZJLMI9EGrdvL8AzW4psI/dxp5IqDquiXZuLLG69QQu6rYm3/ZvA/wIry/cPNQqXXjux//8FRKdtiq12QUECAwEAAaOBgTB/MAkGA1UdEwQCMAAwCwYDVR0PBAQDAgTwMGUGA1UdHwReMFwwWqBYoFaGVGh0dHA6Ly9ldmNhLml0cnVzLmNvbS5jbi9wdWJsaWMvaXRydXNjcmw/Q0E9MUJENDIyMEU1MERCQzA0QjA2QUQzOTc1NDk4NDZDMDFDM0U4RUJEMjANBgkqhkiG9w0BAQsFAAOCAQEAEKAgsJ78I5ZDxXYBhlpmevflxMUMB1GwL7cQ8SOin2qbWJfzBa6ldPOfaxq0s8VCeIGYLNwrk0Sp4H+Xuoj8+kKjHHnULCgxX46nVckNPZms7Yt8+nOAWwsFMCWxetGOgW3RdaIDtWA9JDxsXgPoQ1nX82LvcU0zFb5MHMV6En9sTdcSXGr9yO3icy1mKLItnt94CN+d4QXNaXX1I/YkYZoE0ocTfBWXpvRRTmTAuOZTC+lljKEHWKbuzvxhbivXbuNvE8b94mdvDADHQJ/lRKMi/8hiJ67CKS5LdnoPyNmghSRB0hb2AeNgc457nmTeggjtCVIAKEivNxqhGiAHvA==',//证书
            'ssl_key' => 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC4T9JD24fD6Eeuosx551pCnaRcNEejbBoOIYXJp1bQiwIxDLASkSWsvNkalkBVgy7xngrFGOmj0jIDtb6wFN8iuDxgvQSrHsn08nhXxqDKVHGWhd9hGp2K++/MjIgegnIRNn795nE1zW26/T1EWWK/UJm/wArhV3jMZloFCCUsPbtvozcHXUdQd8NvCDkzJAILAFnAbfiAxSL5v/bhaY1kkIT9XKVjlb4wzcw570nXxFszxDn4klCciVlLtmUCaDOdnvmSSzCPRBq3by/AM1uKbCP3caeSKg6rol2biyxuvUELuq2Jt/2bwP8CK8v3DzUKl147sf//BUSnbYqtdkFBAgMBAAECggEAF8aEMyiwfL8L8CUkB12msQ0w1IwR6azaN5Rvc7HYaxJjd/xRAjQjDgNWZi2XovBHL/fZH8+04+MLfJsHPCOVHR9y+m+nmINiPezBRWFkAHWgKXCffiE2zWTubA58yKK/ICVNgGcnl28mhoaATr7RFCjLjIazYJouBP/3AMsqMGiWASnyEq67HhWyV19NTh0BbjGlfuMjAw4Duv7Cd9NvWQcAycF2FES8Ud0MwL5lFiM8+II8guzxfyAxsyqVHlcYKLr7enPZrS1i/lAVNKbJ4e37srg6hH3IdG8bOzy6aBNiXQzPIM3L4RdrVCPhzG7jt7vj2SNHYBCT0biIX8UZ+QKBgQDp7t/MDY+DlOrW98AMhTUEcqZwhLj1Lj4IBkUY+cEo82DklA2CtLsfleJnhy0o/p1WQlh+cCN3fKJ/tAojwXvvzlOcU7/j/QC4rxhmC12e1NYT/sFzQ4eTCBpv6tf1JelzjR2d8Bi0AndP1ZoYasa7yO+shmE711MOf9NuNxTw+wKBgQDJsqs3PGYWMd2zr53KSD0heQHsH8SzrRlXXMjEY+AiqBspcUu/uy8l0wUdLfgn24EqA+qkTVcIuZ7BQ/dYLdbPeTMh+F80jqhlnWmjOgNisN3Hwk4OAm/x2e4MQ6CSxVnvwpce0ClZvgKhj42Vvc2Grr2w80xE3xUdUJevj5cZ8wKBgCsPA/SMPnFK1NsSqS/kyPhznf0yANVoWJSjis5pEkvWWGxacA4x9AuBTDYgJIjaLN22wpErVOvmbgaxffhM4eInCS7KH5T4ecyEtin6R5Y3uNvfVX5A3NCMeklFYdDG1KynOd9bVUC0/38bYwzBgqkH+E26QmWAYdJWZvVvgBRHAoGARHfBRUvcf7ZHwyJfVvZ0wfMY4vXZyQB+tLhLctdQk5MrPUM3aot5MsbSWphrP+R0kTWpJkMWw2B3crXGtglBfUZwRBzS/L1cjroDzSP5WDwf3RqpcnoDVD8dXs5qKMiTgh81O4GAN/WXYQ6hCm7W4NxaikHc5xgvS7c69p6wtV8CgYBHzOPbptHLMZhf8TFDWjnWF8FMKVycpwOxHN3+AFrzgkQmh9Sx7I+T72YIzsikBc3o3KMQFghZ6jmd3efz9WdQNr/jwPzCZaP9fMFvhqcT+L1NQgEYR76OgnMlSrGWJH6q1fFepgAzPb4uPzMAMc6MrW+9WsUe9n/Upe7CY0bTVw==',//证书秘钥
        ],
    ],
    'notify_url' => '',//留空则设为PAY_NOTIFY对应的路由
    'route'      => false,//是否注册路由
];
