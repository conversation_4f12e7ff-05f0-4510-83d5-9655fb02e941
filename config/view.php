<?php
// +----------------------------------------------------------------------
// | 模板设置
// +----------------------------------------------------------------------

use Twig\Extra\Intl\IntlExtension;

return [
    // 模板引擎类型使用Think
    'type'              => 'Twig',
    // 模板目录名
    'view_path'         => app()->getRootPath() . 'view',
    // 模板后缀
    'view_suffix'       => 'twig',
    // 模板文件名分隔符
    'view_depr'         => DIRECTORY_SEPARATOR,
    'auto_add_function' => true,
    'filters'           => [
        'lang'        => 'lang',
        'markdown'    => 'markdown',
        'json_encode' => function ($value) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        },
    ],
    'extensions'        => [
        IntlExtension::class,
    ],
];
