<?php

use app\middleware\AuthenticationSession;
use app\middleware\CheckCpsRequest;
use think\middleware\SessionInit;
use yunwuxin\auth\middleware\Authentication;

return [
    'inject' => [
        'enable'     => true,
        'namespaces' => [],
    ],
    'route'  => [
        'enable'      => true,
        'controllers' => [
            app_path('controller/console/admin') => [
                'name'       => 'console/admin',
                'middleware' => [
                    [Authentication::class, ['console']],
                ],
            ],
            app_path('controller/console')       => [
                'name'       => 'console',
                'middleware' => [
                    [Authentication::class, ['console']],
                ],
            ],
            app_path('controller')               => [
                'middleware' => [
                    SessionInit::class,
                    AuthenticationSession::class,
                    CheckCpsRequest::class,
                ],
            ],
        ],
    ],
    'model'  => [
        'enable' => true,
    ],
    'ignore' => [],
];
