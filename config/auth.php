<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

//think-auth 配置文件
use app\lib\provider\Ai;
use app\lib\provider\Api;
use app\lib\provider\Console;
use app\lib\provider\Notification;
use app\model\User;

return [
    'default'          => 'web',
    'guards'           => [
        'web'          => [
            'type'     => 'session',
            'provider' => [
                'type'   => 'model',
                'model'  => User::class,
                'fields' => [
                    'username' => 'mobile',
                ],
            ],
        ],
        'api'          => [
            'type'     => 'request',
            'provider' => [
                'type' => Api::class,
            ],
        ],
        'ai'           => [
            'type'     => 'request',
            'provider' => [
                'type' => Ai::class,
            ],
        ],
        'console'      => [
            'type'     => 'token',
            'provider' => [
                'type' => Console::class,
            ],
        ],
        'notification' => [
            'type'     => 'request',
            'provider' => [
                'type' => Notification::class,
            ],
        ],
    ],
    'policy_namespace' => '\\app\\policy\\',
    'policies'         => [],
];
