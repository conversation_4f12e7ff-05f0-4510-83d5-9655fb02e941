.product-page {
  background: #FFFFFF;

  .page-body {
    margin-top: 0 !important;
  }

  .banner {
    background: var(--bs-primary);
    padding-top: 120px;
    min-height: 450px;

    .container {
      text-align: center;

      .banner-content {
        max-width: 780px;
        margin: 0 auto;
        padding-bottom: 60px;

        h1 {
          color: var(--bs-white);
          font-weight: 700;
          font-size: 45px;
          line-height: 60px;
          text-align: center;
          margin-bottom: 30px;
        }

        p {
          color: var(--bs-white);
          opacity: .8;
          font-weight: 400;
          font-size: 20px;
          line-height: 35px;
          text-align: center;
          max-width: 600px;
          margin: auto auto 30px;
        }

        ul {
          display: flex;
          align-items: center;
          justify-content: center;
          list-style: none;
          margin: 0;
          padding: 0;
          gap: 16px;

          li {
            a {
              display: inline-block;
              text-align: center;
              white-space: nowrap;
              vertical-align: middle;
              font-weight: 500;
              font-size: 16px;
              border-radius: 5px;
              padding: 15px 25px;
              border: 1px solid transparent;
              color: var(--bs-white);
              cursor: pointer;
              z-index: 5;
              background: var(--bs-primary);

              &:hover {
                color: var(--bs-white);
                background: 0 0;
                opacity: .5;
              }
            }

            &:first-child {
              a {
                background: var(--bs-white);
                color: var(--bs-dark);

                &:hover {
                  box-shadow: 0 0 25px rgb(0 0 0 / 20%);
                  background: #fefefe;
                  color: var(--bs-primary);
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      .banner-image {
        position: relative;
        max-width: 845px;
        margin: auto;

        @media (max-width: 576px) {
          display: none;
        }

        .image {
          z-index: 2;
          position: relative;
          border-top-left-radius: 5px;
          border-top-right-radius: 5px;
          overflow: hidden;
        }

        .shape {
          position: absolute;

          &.shape-1 {
            bottom: 0;
            left: -30px;
          }

          &.shape-2 {
            top: -20px;
            right: -20px;
          }
        }
      }
    }
  }

  .features {
    padding-top: 80px;
    padding-bottom: 80px;
    background: var(--bs-white);

    @media (max-width: 576px) {
      padding-top: 40px;
      padding-bottom: 0px;
    }

    .container {
      .features-title {
        max-width: 620px;
        margin-bottom: 70px;

        span {
          font-weight: 600;
          font-size: 18px;
          color: var(--bs-primary);
          margin-bottom: 10px;
          display: inline-block;
        }

        h2 {
          font-weight: 700;
          font-size: 36px;
          line-height: 48px;
          text-transform: capitalize;
          color: var(--bs-dark);
          margin-bottom: 20px;
        }

        p {
          font-size: 16px;
          line-height: 30px;
          color: #637381;
          margin: 0;
        }
      }

      .single-feature {
        @media (max-width: 576px) {
          margin-bottom: 24px;
        }

        .feature-icon {
          width: 70px;
          height: 70px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 14px;
          background: var(--bs-primary);
          font-size: 32px;
          color: var(--bs-white);
          position: relative;
          z-index: 1;
          text-align: center;
          margin-bottom: 40px;

          @media (max-width: 576px) {
            float: left;
          }

          &:before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            border-radius: 14px;
            background: var(--bs-primary);
            opacity: .2;
            z-index: -1;
            transform: rotate(23deg);
            transition: all .3s ease-out 0s;
          }
        }

        .feature-content {
          @media (max-width: 576px) {
            margin-left: 86px;
          }

          h3 {
            font-weight: 600;
            font-size: 20px;
            line-height: 28px;
            margin-bottom: 12px;
          }

          p {
            font-size: 14px;
            line-height: 24px;
            color: #637381;
            margin: 0;
          }
        }

        &:hover {
          .feature-icon:before {
            transform: rotate(45deg);
          }
        }
      }
    }
  }

  .about {
    background: rgba(74, 108, 247, 0.03);
    padding: 80px 0;

    .container {
      .about-wrap {
        background: var(--bs-white);
        display: flex;
        justify-content: space-between;
        border: 1px solid rgba(0, 0, 0, .08);

        .about-content {
          padding: 70px;
          max-width: 640px;

          @media (max-width: 576px) {
            padding: 20px 16px;
          }

          .tag {
            font-weight: 500;
            font-size: 14px;
            color: var(--bs-white);
            background: var(--bs-primary);
            padding: 5px 20px;
            display: inline-block;
            margin-bottom: 20px;
          }

          h2 {
            font-weight: 700;
            font-size: 36px;
            line-height: 45px;
            margin-bottom: 30px;
          }

          p {
            font-size: 16px;
            line-height: 28px;
            margin-bottom: 40px;
            color: #637381;
            @media (max-width: 576px) {
              margin-bottom: 20px;
            }
          }
        }

        .about-image {
          @media (max-width: 576px) {
            display: none;
          }
        }
      }
    }
  }

  .pricing {
    padding-top: 80px;
    padding-bottom: 80px;

    @media (max-width: 576px) {
      display: none;
    }

    .container {
      .pricing-title {
        text-align: center;
        margin-bottom: 70px;

        span {
          display: inline-block;
          font-weight: 600;
          font-size: 18px;
          color: var(--bs-primary);
          margin-bottom: 10px;
        }

        h2 {
          font-weight: 700;
          font-size: 36px;
          line-height: 48px;
          text-transform: capitalize;
          color: var(--bs-dark);
          margin-bottom: 20px;
        }
      }

      .pricing-table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        vertical-align: top;
        border-color: #dee2e6;

        table {
          & > :not(caption) > * > * {
            padding: 25px 10px;
            border-right: 1px solid #dee2e6;
          }

          tbody {
            border-top: 2px solid #dee2e6;
          }

          tr {
            th {
              text-align: center;

              h6 {
                margin-bottom: 14px;
                color: var(--bs-gray-dark);
                font-weight: 700;
                font-size: 16px;
              }

              h3 {
                font-size: 30px;
                color: var(--bs-primary);
                letter-spacing: .4px;
              }

              p {
                color: var(--bs-gray-dark);
                font-weight: 500;
              }
            }

            td {
              min-width: 200px;
              vertical-align: middle;

              p.icon {
                text-align: center;
                color: var(--bs-gray-dark);
                font-weight: 700;

                &.active {
                  color: var(--bs-primary);
                }
              }
            }
          }

          p {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
