.markdown-content {
  color: #525252;
  font-size: 15px;
  line-height: 1.2;
  word-wrap: break-word;

  & > *:first-child {
    margin-top: 0 !important;
  }

  & > *:last-child {
    margin-bottom: 0 !important;
  }

  h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table {
    margin: 14px 0;
    padding: 0;
    line-height: 1.2;

    &:last-child {
      margin-bottom: 0;
    }

    &:first-child {
      margin-top: 0;
    }
  }

  p {
    margin: 0;
    line-height: 2;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: normal;
  }

  h1 {
    font-size: 1.8em;
    line-height: 1.2;
  }

  h2 {
    padding-bottom: .3em;
    font-size: 1.3em;
    line-height: 1.225;
    border-bottom: 1px solid #eee;
  }

  h3 {
    font-size: 1.2em;
    line-height: 1.43
  }

  h4 {
    font-size: 1.1em
  }

  h5 {
    font-size: 1em
  }

  hr {
    margin-bottom: 0.5em;
    border: 0;
    border-bottom: 1px solid #ddd;
  }

  ul {
    padding-left: 28px;
    list-style: initial;

    ul {
      margin: 0;
    }
  }

  ol {
    padding-left: 28px;
    list-style: initial;
  }

  li {
    line-height: 2;
  }

  code {
    margin: -.2em .4em;
    padding: .2em .4em;
    color: #dcdcdc;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    line-height: 1.3;
    white-space: pre;
    word-break: break-all;
    background: #3f3f3f;
    border-radius: 4px;
  }

  pre {
    padding: 16px;
    overflow: auto;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    line-height: 1.45;
    background: #3f3f3f;
    border: 0;
    border-radius: 3px;
  }

  pre > code {
    display: inline-block;
    max-width: 100%;
    margin: 0;
    padding: 0;
    overflow: initial;
    line-height: inherit;
    tab-size: 4;
    background-color: rgba(0, 0, 0, 0);
    border: 0;
  }

  table {
    width: 100%;
    line-height: 1.8;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;

    th {
      text-align: left;
    }

    th, td {
      padding: 3px 12px;
      border: 1px solid #e0e0e0;
    }

    tr:hover td {
      background-color: #f1f1f1;
    }
  }

  img {
    display: inline-block;
    max-width: 100%;
    padding: 3px;
    vertical-align: middle;
  }

  blockquote {
    padding: 5px 5px 5px 15px;
    color: #777;
    line-height: 2;
    border-left: 4px solid #ddd;
  }
}
