.write-page {
  .write-body {
    .banner {
      background-color: #3b5fff;
      background-image: url("../images/write/homepage_01.png");
      background-repeat: no-repeat;
      background-position: top center;

      .container {
        height: 1000px;
        padding-top: 75px;
        color: #fff;
        text-align: center;

        h2 {
          font-size: 32px;
        }

        h3 {
          font-size: 20px;
          margin-top: 20px;
        }

        .preview {
          height: 511px;
          background-image: url("../images/write/homepage_02.png");
          background-repeat: no-repeat;
          background-position: top center;
        }

        h4 {
          margin-top: 40px;
        }

        .types {
          display: flex;
          justify-content: space-between;
          padding: 50px 250px 0;

          div {
            padding-top: 80px;
            background-repeat: no-repeat;
            background-position: top center;
            width: 75px;

            &:nth-child(1) {
              background-image: url("../images/write/homepage_03.png");
            }

            &:nth-child(2) {
              background-image: url("../images/write/homepage_04.png");
            }

            &:nth-child(3) {
              background-image: url("../images/write/homepage_05.png");
            }

            &:nth-child(4) {
              background-image: url("../images/write/homepage_06.png");
            }
          }
        }
      }
    }

    .login-area {
      padding: 60px 0;

      h3 {
        color: #233373;
        text-align: center;
        font-size: 20px;
        margin-bottom: 40px;
      }

      .login-items {
        display: flex;
        justify-content: space-between;
        padding: 0 250px;

        a {
          text-decoration: none;

          .iconfont {
            font-size: 60px;
          }
        }
      }
    }

    .features {
      border-radius: 20px;
      padding: 60px 0 0;
      margin: 50px 0;
      background-color: #f4f9ff;

      h3 {
        color: #233373;
        text-align: center;
        font-size: 20px;
        margin-bottom: 30px;
      }

      .theme {
        height: 298px;
        background-image: linear-gradient(180deg, #f4f9ff, #ffffff);
        position: relative;

        &:after {
          content: "";
          background-image: url("../images/write/homepage_07.png");
          background-repeat: no-repeat;
          background-position: top center;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
        }
      }

      .items {
        background-color: #ffffff;
        padding: 20px 158px;

        dl {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 40px;

          dt {
            width: 432px;
            height: 325px;
            background-repeat: no-repeat;
            background-position: 0 0;
          }

          dd {
            width: 432px;

            h4 {
              color: #233373;
              margin-bottom: 20px;
            }

            p {
              color: #666666;
              margin-bottom: 0;
            }
          }

          &:nth-child(2n) {
            direction: rtl;
          }

          &:nth-child(1) {
            dt {
              background-image: url("../images/write/homepage_08.png");
            }
          }

          &:nth-child(2) {
            dt {
              background-image: url("../images/write/homepage_09.png");
            }
          }

          &:nth-child(3) {
            dt {
              background-image: url("../images/write/homepage_10.png");
            }
          }

          &:nth-child(4) {
            dt {
              background-image: url("../images/write/homepage_11.png");
            }
          }

          &:nth-child(5) {
            dt {
              background-image: url("../images/write/homepage_12.png");
            }
          }

          &:nth-child(6) {
            dt {
              background-image: url("../images/write/homepage_13.png");
            }
          }
        }
      }
    }

    .welcome {
      height: 300px;
      background: #3b5fff url("../images/write/homepage_15.png") no-repeat center center;
      position: relative;
      border-radius: 20px;
      margin: 50px 0;

      &:before {
        content: "";
        width: 225px;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        background: url("../images/write/homepage_14.png") no-repeat left center;
      }

      &:after {
        content: "";
        width: 225px;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        background: url("../images/write/homepage_16.png") no-repeat left center;
      }

      .login {
        display: block;
        width: 200px;
        height: 40px;
        position: absolute;
        top: 205px;
        left: 537px;
        background: #FFF;
        border-radius: 6px;
        color: #3b5fff;
        text-decoration: none;
        text-align: center;
        line-height: 40px;
        font-size: 20px;
      }
    }
  }
}
