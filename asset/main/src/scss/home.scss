.home-page {
  background: #FFFFFF;

  .page-body {
    margin-top: 0 !important;
  }

  .banner {
    position: relative;
    padding-top: 160px;
    padding-bottom: 200px;
    overflow: hidden;

    .container {
      text-align: center;
      z-index: 1;
      position: relative;

      h1 {
        line-height: 60px;
        font-size: 45px;
        margin-bottom: 20px;
        color: rgb(9, 14, 52);
        font-weight: 500;
      }

      p {
        color: rgb(149, 156, 177);
        font-size: 20px;
        line-height: 32.5px;
        font-weight: 500;
        margin-bottom: 48px;
      }
    }

    .bg-top {
      right: 0;
      top: 0;
      position: absolute;
      @media (max-width: 576px) {
        right: -200px;
      }
    }

    .bg-bottom {
      bottom: 0;
      left: 0;
      position: absolute;
      @media (max-width: 576px) {
        left: -100px;
      }
    }
  }

  .features {
    padding-top: 120px;
    padding-bottom: 50px;
    background-color: rgba(74, 108, 247, 0.03);

    .header {
      text-align: center;
      max-width: 570px;
      margin-bottom: 100px;
      margin-left: auto;
      margin-right: auto;

      h2 {
        font-size: 35px;
        line-height: 40px;
        margin-bottom: 16px;
        color: rgb(9, 14, 52);
      }

      p {
        color: rgb(149, 156, 177);
        font-size: 18px;
      }
    }

    .row {
      .col-12 {
        margin-bottom: 70px;
        padding-right: 16px;
        padding-left: 16px;

        .icon {
          color: rgb(74, 108, 247);
          width: 70px;
          height: 70px;
          background-color: rgba(74, 108, 247, 0.1);
          display: flex;
          align-items: center;
          border-radius: 6px;
          justify-content: center;
          margin-bottom: 40px;

          svg {
            fill: currentColor;
          }
        }

        h3 {
          font-size: 24px;
          line-height: 32px;
          color: rgb(9, 14, 52);
          margin-bottom: 20px;
        }

        p {
          font-size: 16px;
          line-height: 26px;
          padding-right: 10px;
          color: rgb(149, 156, 177);
        }

        @media (max-width: 576px) {
          margin-bottom: 20px;
          .icon {
            float: left;
          }
          h3 {
            margin-bottom: 10px;
            margin-left: 86px;
          }
          p {
            margin-left: 86px;
          }
        }
      }
    }
  }

  .advantage {
    padding-top: 120px;
    @media (max-width: 576px) {
      padding-top: 60px;
    }

    .container {
      padding-bottom: 100px;

      & > .row {
        & > .col-12 {
          &:first-child > * {
            max-width: 570px;
          }

          &:last-child {
            text-align: right;
            @media (max-width: 576px) {
              display: none;
            }
          }

          h2 {
            color: rgb(9, 14, 52);
            font-size: 45px;
            line-height: 56.25px;
            margin-bottom: 24px;
          }

          p {
            color: rgb(149, 156, 177);
            font-size: 18px;
            line-height: 29.25px;
            margin-bottom: 44px
          }

          & > .row {
            & > .col-12 {
              color: rgb(149, 156, 177);
              display: flex;
              font-size: 18px;
              align-items: center;
              line-height: 28px;
              margin-bottom: 20px;

              .icon {
                color: rgb(74, 108, 247);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
                background-color: rgba(74, 108, 247, 0.1);
                border-radius: 6px;
                width: 30px;
                height: 30px;

                svg {
                  fill: currentColor;
                }
              }
            }
          }

        }
      }
    }
  }
}
