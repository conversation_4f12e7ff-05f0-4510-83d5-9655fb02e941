.page-wrap {
  min-height: var(--100vh, 100vh);
  display: flex;
  flex-direction: column;
  padding-top: 54px;
  background: #eee;

  &.headless {
    padding-top: 0;

    .page-content {
      min-height: var(--100vh, 100vh);
    }
  }

  header {
    @media (min-width: 576px) {
      nav {
        height: 54px;
      }
    }

    @media (max-width: 576px) {
      .dropdown-menu {
        box-shadow: none !important;
      }
    }
  }

  .page-content {
    display: flex;
    flex-direction: row;
    min-height: calc(var(--100vh, 100vh) - 54px);

    & > aside {
      width: 231px;
      background-color: #f5f5f5;
      border-right: 1px solid #e3e3e3;

    }

    & > main {
      flex: 1;
      display: flex;
      flex-direction: column;

      .page-header {
        background-color: #fff;
        display: flex;
        margin-bottom: 20px;

        .container {
          padding-right: var(--bs-gutter-x, 0.75rem);
          padding-left: var(--bs-gutter-x, 0.75rem);
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .title {
          font-size: 22px;
          line-height: 64px;
          height: 64px;
        }

        .nav {
          .nav-link {
            position: relative;

            &.active {
              &:after {
                content: '';
                height: 2px;
                background: var(--bs-primary);
                display: block;
                position: absolute;
                left: 1rem;
                right: 1rem;
                bottom: -2px;
              }
            }
          }
        }

        .action {
          display: flex;
          flex-wrap: nowrap;
          gap: 5px;
        }
      }

      .page-body {
        flex: 1;

        &:first-child {
          margin-top: .75rem;
        }
      }

      .page-footer {
        text-align: center;
        padding: 16px 0;
        opacity: .8;
      }
    }
  }
}
