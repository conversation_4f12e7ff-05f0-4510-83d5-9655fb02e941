import $ from 'jquery';

const $button = $('[data-bs-captcha]');
const $mobile = $('input[name="mobile"]');

new window.TencentCaptcha($button[0], '2035759212', async (res: any) => {
    if (res.ret === 0) {
        $button.api({
            on: 'now',
            data: function() {
                return {
                    mobile: $mobile.val(),
                    captcha: {
                        ticket: res.ticket,
                        randstr: res.randstr
                    }
                };
            },
            onSuccess: function() {
                const $element = $(this);
                requestAnimationFrame(() => {
                    let seconds = 60;
                    $element.prop('disabled', true);
                    $element.text(`${seconds}秒后重发`);

                    const countDown = () => {

                        --seconds;

                        if (seconds <= 0) {
                            $element.prop('disabled', null);
                            $element.text('发送验证码');
                        } else {
                            $element.text(`${seconds}秒后重发`);
                            setTimeout(countDown, 1000);
                        }
                    };

                    setTimeout(countDown, 1000);
                });
            }
        });
    }
}, { needFeedBack: false });
