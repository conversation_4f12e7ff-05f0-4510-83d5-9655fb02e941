import $ from 'jquery';

const formatter = new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
});

$('[data-bs-packages] [role=button]')
.on('click', function(e) {
    e.preventDefault();
    const $this = $(this);

    $this.siblings('[role=button]').removeClass('border-primary text-primary bg-primary bg-opacity-10 fw-bold');
    $this.addClass('border-primary text-primary bg-primary bg-opacity-10 fw-bold');

    if ($this.data('price')) {
        const price = formatter.format($this.data('price'));
        const unitPrice = ($this.data('price') / $this.data('nums')).toFixed(4);

        const priceHtml = `<span class='fs-2 text-danger'>${price}</span><span class='fs-6 text-muted'>（约${unitPrice}元/次）</span>`;

        $('[data-bs-price-target]').html(priceHtml);
    }
    if ($this.data('url')) {
        $('[data-bs-url-target]').attr('href', $this.data('url'));
    }
})
.first().trigger('click');

