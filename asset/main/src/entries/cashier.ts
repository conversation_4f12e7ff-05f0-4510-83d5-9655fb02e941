import { createModal, request, Response } from '@topthink/core';
import QRCode from 'qrcode';
import $ from 'jquery';

type Result = { type: 'scan', data: string; } | {
    type: 'transfer';
    data: {
        company: string;
        bank: string;
        account: string;
        trade_no: string;
        amount: number;
    };
} | {
    type: 'redirect',
    data: string
} | {
    type: 'params',
    data: object
} | {
    type: 'none'
};

interface Options {
    id: string;
    status: number;
    channel: string;
    amount: number;
    coin: number;
    money: number;
    discount: number;
    waitingTransfer?: boolean;
    mode?: 'redirect' | 'popup';
}

export default function({ id, channel, status, amount, coin, money, discount, waitingTransfer = false }: Options) {

    //选中第一个
    $('[name=channel]').eq(0).prop('checked', true);

    let checking = false;
    const check = (id: any) => {
        if (!checking) {
            checking = true;
            request.get(`/cashier/check`, { id, status }, {
                raxConfig: {
                    shouldRetry(err) {
                        return checking && err.response?.status === 449;
                    }
                },
            });
        }
    };

    if (status == 1) {
        if (window.opener) {
            setTimeout(function() {
                window.opener.focus();
                window.close();
            }, 1000);
        }
    } else if (status == 2 || waitingTransfer) {
        check(id);
    } else {
        const formatter = new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY'
        });

        let promo: string | null = null;

        const updateAmount = () => {
            const balance = amount - discount - coin - money;
            $('[data-bs-amount]').html(formatter.format(balance / 100));

            if (balance > 0) {
                $('[data-bs-channel]').show();
            } else {
                $('[data-bs-channel]').hide();
            }
        };

        //云币
        const $coin = $('[data-bs-coin]');
        if ($coin.length > 0) {
            const $coinInput = $coin.find<HTMLInputElement>('input[type=number]');
            const getMaxCoin = () => {
                return Math.min(parseInt($coin.attr('data-bs-coin') || '0'), amount - discount - money);
            };

            $coinInput.on('input', function(e) {
                let value = e.target.value;
                if (!/^\d*\.?\d{0,2}$/.test(value)) {
                    value = value.slice(0, -1);
                }
                coin = Math.floor(parseFloat(value) * 100);

                if (isNaN(coin)) coin = 0;

                const min = 0;
                const max = getMaxCoin();

                if (coin > max) {
                    coin = max;
                    value = String(max / 100);
                }
                if (coin < min) {
                    coin = min;
                    value = String(min / 100);
                }

                updateAmount();
                if (value != e.target.value) {
                    e.target.value = value;
                }
            });
            $coin.find('.form-check-input').on('change', function() {
                if ($(this).is(':checked')) {
                    coin = getMaxCoin();
                    $coinInput.removeAttr('disabled');
                    $coinInput.val(coin / 100);
                } else {
                    coin = 0;
                    $coinInput.attr('disabled', '');
                    $coinInput.val('');
                }
                updateAmount();
            });
        }

        //余额
        const $money = $('[data-bs-money]');
        if ($money.length > 0) {
            const $moneyInput = $money.find<HTMLInputElement>('input[type=number]');
            const getMaxMoney = () => {
                return Math.min(parseInt($money.attr('data-bs-money') || '0'), amount - discount - coin);
            };
            $moneyInput.on('input', function(e) {
                let value = e.target.value;
                if (!/^\d*\.?\d{0,2}$/.test(value)) {
                    value = value.slice(0, -1);
                }
                money = Math.floor(parseFloat(value) * 100);

                if (isNaN(money)) money = 0;

                const min = 0;
                const max = getMaxMoney();

                if (money > max) {
                    money = max;
                    value = String(max / 100);
                }
                if (money < min) {
                    money = min;
                    value = String(min / 100);
                }

                updateAmount();
                if (value != e.target.value) {
                    e.target.value = value;
                }
            });
            $money.find('.form-check-input').on('change', function() {
                if ($(this).is(':checked')) {
                    money = getMaxMoney();
                    $moneyInput.removeAttr('disabled');
                    $moneyInput.val(money / 100);
                } else {
                    money = 0;
                    $moneyInput.attr('disabled', '');
                    $moneyInput.val('');
                }
                updateAmount();
            });
        }

        //优惠码
        const $promo = $('[data-bs-promo]');

        const $addPromo = $('<a class="link-primary" role="button">使用优惠码</a>');

        $addPromo.on('click', function() {
            const $promoForm = $(`<form  method='post' action='/cashier/promo'>
<div class='alert alert-danger' hidden></div>
<input type='hidden' name='id' value='${id}' />
<input type='text' class='form-control' name='code' placeholder='输入优惠码' />
</form>`);

            let onComplete: ((res: boolean | void) => void) | undefined = undefined;

            $promoForm.form({
                onSuccess({ data }) {
                    $addPromo.replaceWith(`使用优惠码抵扣 <span class='text-orange'>${formatter.format(data.discount / 100)}</span> 元`);
                    promo = data.code;
                    discount = data.discount;
                    $coin.find('.form-check-input').prop('checked', false).trigger('change');
                    $money.find('.form-check-input').prop('checked', false).trigger('change');

                    updateAmount();
                    onComplete?.(true);
                },
                onError() {
                    onComplete?.(false);
                }
            });

            createModal({
                title: '使用优惠码',
                content: $promoForm,
                onOk: () => {
                    return new Promise<boolean | void>((resolve) => {
                        onComplete = resolve;
                        $promoForm.trigger('submit');
                    });
                }
            });
        });

        $promo.children('dd').append($addPromo);

        $('[data-bs-cashier-form]').form({
            beforeSend(config) {
                if (!channel) {
                    if (promo) {
                        config.data.set('promo', promo);
                    }
                    if (coin > 0) {
                        config.data.set('coin', String(coin));
                    }
                    if (money > 0) {
                        config.data.set('money', String(money));
                    }
                }
                return config;
            },
            async onSuccess({ config: { data }, data: result }: Response<Result>) {
                if (!channel) {
                    //更新页面支付明细
                    if (!promo) {
                        $promo.remove();
                    }
                    if (coin > 0) {
                        $coin.children('dd')
                             .html(`使用云币抵扣 <span class='text-orange'>${formatter.format(coin / 100)}</span> 元`);
                    } else {
                        $coin.remove();
                    }
                    if (money > 0) {
                        $money.children('dd')
                              .html(`使用余额抵扣 <span class='text-orange'>${formatter.format(money / 100)}</span> 元`);
                    } else {
                        $money.remove();
                    }
                }
                channel = data.get('channel');

                const paid = () => {
                    request.post(`/cashier/paid`, {
                        id,
                        channel
                    });
                };

                switch (result.type) {
                    case 'scan':
                        check(id);

                        const okText = '已完成支付';
                        const onOk = () => {
                            paid();
                            return false;
                        };

                        const dataURL = await QRCode.toDataURL(result.data, { width: 250 });
                        createModal({
                            title: '扫码支付',
                            content: `<img class='mx-auto d-block' src="${dataURL}"/>`,
                            okText,
                            onOk,
                            onHide() {
                                checking = false;
                            }
                        });
                        break;
                    case 'redirect':
                        location.href = result.data;
                        break;
                    case 'params':
                        WeixinJSBridge.invoke('getBrandWCPayRequest', result.data,
                            function(res) {
                                if (res.err_msg == 'get_brand_wcpay_request:ok') {
                                    check(id);
                                }
                            }
                        );
                        break;
                    case 'transfer':
                        createModal({
                            title: '对公转账',
                            content: `
<dl class='row'>
    <dt class='col-4 text-end'>公司名</dt>
    <dd class='col-8'>${result.data.company}</dd>
</dl>
<dl class='row'>
    <dt class='col-4 text-end'>开户行</dt>
    <dd class='col-8'>${result.data.bank}</dd>
</dl>
<dl class='row'>
    <dt class='col-4 text-end'>账号</dt>
    <dd class='col-8'>${result.data.account}</dd>
</dl>
<dl class='row'>
    <dt class='col-4 text-end'>订单号</dt>
    <dd class='col-8'>${result.data.trade_no}（转账时备注）</dd>
</dl>`,
                            okText: '确认支付',
                            onOk: () => {
                                request.post(`/cashier/transfer`, {
                                    id
                                });
                                return false;
                            }
                        });
                        break;
                    case 'none':
                        //无需支付
                        check(id);
                        break;
                }
            }
        });

        updateAmount();
    }
}
