import $ from 'jquery';
import { request } from '@topthink/core';

declare global {
    interface Window {
        TencentCaptcha: any;
    }
}

export default function(defaultState: string = 'wechat') {

    let qrcodeInit = false;
    const $qrcode = $('[data-bs-qrcode]');

    const checkMiniLogin = (token: string) => {
        request({
            url: '/login/mini',
            params: { token }
        }).catch(() => {
            //过期
            const $refresh = $('<div class="qr-mask"><i class="bi bi-arrow-repeat"></i></div>');
            $refresh.on('click', () => {
                $qrcode.html('<div class="spinner-border text-primary"></div>');
                getMiniQrcode();
            });
            $qrcode.append($refresh);
            $qrcode.next('h5').html('小程序码已过期，请点击刷新');
        });
    };

    const getMiniQrcode = async () => {
        const result = await request('/mini/qrcode');

        $qrcode.html(`<img src="${result.image}" />`);
        $qrcode.next('h5').html('请使用微信，扫码登录');
        checkMiniLogin(result.token);
    };

    let state;

    window.onhashchange = function() {
        changeState();
    };

    const changeState = () => {

        state = location.hash.substr(1) || defaultState;

        $('[data-bs-target]').attr('hidden', '');
        $(`[data-bs-target="${state}"]`).removeAttr('hidden');

        $('[data-bs-selector]').removeAttr('hidden');
        $(`[data-bs-selector="${state}"]`).attr('hidden', '');

        if (state === 'wechat' && !qrcodeInit) {
            qrcodeInit = true;
            getMiniQrcode();
        }
    };

    changeState();

}
