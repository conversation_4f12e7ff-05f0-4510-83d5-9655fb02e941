{"name": "@topthink/main", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "dependencies": {"@topthink/core": "^1.0.53", "bootstrap": "^5.2.2", "qrcode": "^1.4.4"}, "devDependencies": {"@topthink/webpack-config-plugin": "^1.0.16", "@types/bootstrap": "^5.0.16", "@types/jquery": "^3.5.6", "@types/qrcode": "^1.4.1", "copy-webpack-plugin": "^9.0.1", "typescript": "^4.0.3", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0", "webpack-manifest-plugin": "^5.0.0"}}