const path                    = require("path");
const WebpackConfigPlugin     = require("@topthink/webpack-config-plugin");
const {WebpackManifestPlugin} = require("webpack-manifest-plugin");
const CopyPlugin              = require("copy-webpack-plugin");
const Server                  = require("webpack-dev-server");

module.exports = async (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const port = await Server.getFreePort();

    const publicPath = isServer ? `http://192.168.0.72:${port}/` : "/asset/";

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/app.ts",
        cache    : {
            type: "filesystem"
        },
        output   : {
            filename     : isServer ? "app.js" : "app.[contenthash:6].js",
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            clean        : true,
            publicPath
        },
        resolve  : {
            fallback: {
                "path": "path-browserify"
            }
        },
        externals: {
            jquery: "jQuery"
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : {
                    filename     : "../../../../view/base.twig",
                    template     : "public/index.html",
                    inject       : false,
                    minify       : false,
                    scriptLoading: "blocking",
                    favicon      : "public/favicon.ico"
                },
                react: false
            }),
            new WebpackManifestPlugin({
                writeToFileEmit: true,
                filter(file) {
                    return file.isModuleAsset;
                }
            }),
            new CopyPlugin({
                patterns: [
                    {from: "public/images", to: "images/[path][name].[contenthash:6][ext]"}
                ]
            })
        ],
        devServer: {
            hot          : true,
            port,
            headers      : {
                "Access-Control-Allow-Origin" : "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
            },
            allowedHosts : "all",
            client       : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            devMiddleware: {
                writeToDisk(filePath) {
                    return /\.twig$/.test(filePath);
                }
            }
        }
    };
};
