{"name": "@topthink/passport", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "devDependencies": {"@restart/ui": "^1.6.1", "@topthink/webpack-config-plugin": "^1.0.20", "@types/loadjs": "^4.0.1", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-transition-group": "^4.4.5", "@types/styled-components": "^5.1.11", "axios": "^1.3.4", "loadjs": "4.3.0-rc1", "react": "^18.0.0", "react-async-hook": "^3.6.2", "react-dom": "^18.0.0", "react-redux": "^8.0.4", "react-transition-group": "^4.4.5", "redux": "^4.2.1", "retry-axios": "^2.6.0", "styled-components": "^5.3.6", "typescript": "^4.0.2", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}, "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "MIT"}