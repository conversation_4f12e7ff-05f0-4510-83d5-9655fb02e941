import styled, { keyframes } from 'styled-components';

const rotate = keyframes`
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
`;

const Loader = styled.div`
  color: #3c60ff;
  border: 0.25em solid currentcolor;
  border-right-color: transparent;
  display: inline-block;
  width: 2em;
  height: 2em;
  vertical-align: -0.125em;
  border-radius: 50%;
  animation: 0.75s linear infinite ${rotate};
`;

export default Loader;
