import BaseModal, { ModalProps } from '@restart/ui/Modal';
import styled from 'styled-components';
import Transition, { TransitionStatus } from 'react-transition-group/Transition';
import { cloneElement } from 'react';

const fadeStyles: Record<TransitionStatus, string> = {
    entering: 'show',
    entered: 'show',
    exited: '',
    exiting: '',
    unmounted: ''
};

const Fade: ModalProps['transition'] = ({ children, ...props }) => <Transition {...props} timeout={150}>
    {(status) => cloneElement(children, {
        className: `fade ${fadeStyles[status]}`,
    })}
</Transition>;


export default function Modal({ children, onHide, ...props }: ModalProps) {
    const renderDialog: ModalProps['renderDialog'] = (props) => <Container
        {...props}
    >
        <InnerBackdrop onClick={onHide} />
        <Dialog><Body>{children}</Body></Dialog>
    </Container>;

    const renderBackdrop: ModalProps['renderBackdrop'] = (props) => <Backdrop {...props} />;

    return <BaseModal
        {...props}
        transition={Fade}
        backdropTransition={Fade}
        renderBackdrop={renderBackdrop}
        renderDialog={renderDialog}
    />;
}

const InnerBackdrop = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
`;

const Backdrop = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;

  &.fade {
    transition: opacity .15s linear;
    opacity: 0;
  }

  &.show {
    opacity: .45;
  }
`;

const Body = styled.div`
  position: relative;
  background-color: #FFFFFF;
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  display: flex;
  width: 100%;
  height: 500px;
  font-family: Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #262626;
`;

const Dialog = styled.div`
  --modal-margin: .5em;

  margin: .5em;
  pointer-events: none;
  position: relative;
  width: auto;
  align-items: center;
  display: flex;
  min-height: calc(100% - var(--modal-margin) * 2);

  @media (min-width: 576px) {
    --modal-margin: 1.75em;
    margin-left: auto;
    margin-right: auto;
    max-width: 380px;
  }
`;

const Container = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  overflow-x: hidden;
  overflow-y: auto;

  *, :after, :before {
    box-sizing: border-box;
  }

  &:focus-visible {
    outline: none;
  }

  &.fade {
    transition: opacity .15s linear;

    ${Dialog} {
      transition: transform .3s ease-out;
      transform: translateY(-50px);
    }
  }

  &.show {
    ${Dialog} {
      transform: none;
    }
  }
`;
