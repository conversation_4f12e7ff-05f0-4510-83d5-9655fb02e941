import Modal from './modal';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components';
import Qrcode from './qrcode';
import { useState } from 'react';
import Form from './form';
import { ReactComponent as WechatIcon } from '../images/wechat.svg';
import { ReactComponent as MobileIcon } from '../images/mobile.svg';

interface Props {
    target: HTMLElement;
}

export default function App({ target }: Props) {

    const open = useSelector<{ open: boolean }, boolean>(state => state.open);

    const dispatch = useDispatch();
    const [channel, setChannel] = useState('wechat');

    return <Modal
        show={open}
        onHide={() => dispatch({ type: 'close' })}
        container={target}
    >
        <Container>
            {channel === 'wechat' && <Qrcode />}
            {channel === 'mobile' && <Form />}
            <h6>其他登录方式</h6>
            <Channels>
                {channel === 'mobile' && <a onClick={() => setChannel('wechat')}><WechatIcon /></a>}
                {channel === 'wechat' && <a onClick={() => setChannel('mobile')}><MobileIcon /></a>}
            </Channels>
        </Container>
    </Modal>;
}

const Channels = styled.div`
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;

  a {
    text-decoration: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7f8;
    border-radius: 50%;
    cursor: pointer;

    svg {
      width: 20px;
      height: 20px;
    }
  }
`;

const Container = styled.div`
    font-size: 14px;
    width: 300px;
    margin: 20px auto;
    padding: 1em 0;

    h4, h5, h6 {
        font-weight: 500;
        line-height: 1.2;
        margin-bottom: .5em;
        margin-top: 0;
        text-align: center;
    }

    h4 {
        margin-bottom: 2em;
        font-size: calc(1.275em + .3vw);
    }

    h5 {
        text-align: center;
        font-size: 1.25em;
    }

    h6 {
        margin-top: 3em;
        color: #bbbbbb;
        margin-bottom: 1em;
        font-size: 1em;
    }
`;
