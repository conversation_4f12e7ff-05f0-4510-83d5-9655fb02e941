import { FormEvent, useEffect, useRef, useState } from 'react';
import styled, { css } from 'styled-components';
import loadjs from 'loadjs';
import axios from 'axios';
import useCountdown from '../hooks/use-countdown';
import { useDispatch, useSelector } from 'react-redux';

declare global {
    interface Window {
        TencentCaptcha: any;
    }
}

let Loaded = false;

const CaptchaButton = function({ mobile, onError }: { mobile: string, onError: (e: string | null) => void }) {
    const ref = useRef<HTMLButtonElement>(null);
    const mobileRef = useRef<string>('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        mobileRef.current = mobile;
    }, [mobile]);

    const [seconds, run] = useCountdown();

    useEffect(() => {
        if (!Loaded) {
            Loaded = true;
            loadjs('https://turing.captcha.gtimg.com/TCaptcha.js', 'tcaptcha');
        }
        loadjs.ready('tcaptcha', () => {
            new window.TencentCaptcha(ref.current, '2035759212', async (res: any) => {
                if (res.ret === 0) {
                    setLoading(true);
                    onError(null);
                    //发送验证码
                    try {
                        await axios.post('/passport/captcha', {
                            mobile: mobileRef.current,
                            captcha: {
                                ticket: res.ticket,
                                randstr: res.randstr
                            }
                        });
                        run(60);
                    } catch (e: any) {
                        onError(e.message);
                    } finally {
                        setLoading(false);
                    }
                }
            }, { needFeedBack: false });
        });
    }, []);

    return <Button type='button' disabled={loading || seconds > 0} ref={ref}>{seconds > 0 ? `${seconds}秒后重发` : `发送验证码`}</Button>;
};

export default function Form() {

    const [params, onSuccess] = useSelector<any, any>(state => [state.options?.params, state.options?.onSuccess]);
    const dispatch = useDispatch();

    const [mobile, setMobile] = useState('');
    const [code, setCode] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);

    const onSubmit = async (e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setLoading(true);
        setError(null);
        try {
            const { data } = await axios.post('/passport/login', {
                mobile,
                code,
            }, { params });

            //登录成功
            const { data: { token } } = await axios.get(data.location);

            dispatch({ type: 'close' });
            onSuccess(token);
        } catch (e: any) {
            setError(e.message);
        } finally {
            setLoading(false);
        }
    };

    return <>
        <h4>手机短信登录</h4>
        <form onSubmit={onSubmit}>
            {error && <Message dangerouslySetInnerHTML={{ __html: error }} />}
            <Row>
                <Input value={mobile} onChange={(e) => setMobile(e.target.value)} type='text' placeholder='手机号' />
            </Row>
            <Row $group>
                <Input value={code} onChange={e => setCode(e.target.value)} type='text' placeholder='验证码' />
                <CaptchaButton mobile={mobile} onError={(e) => setError(e)} />
            </Row>
            <Row>
                <Button type='submit' disabled={loading}>登录</Button>
            </Row>
        </form>
    </>;
}

const Message = styled.div`
  position: relative;
  padding: 1em;
  margin-bottom: 1em;
  color: #58151c;
  background-color: #f8d7da;
  border: 1px solid #f1aeb5;
  border-radius: 0.375em;
`;

const Row = styled.div<{ $group?: boolean }>`
  margin-bottom: 1em;

  ${({ $group }) => $group ? css`
    align-items: stretch;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    width: 100%;

    ${Input} {
      flex: 1 1 auto;
      min-width: 0;
      position: relative;
      width: 1%;
      border-bottom-right-radius: 0;
      border-top-right-radius: 0;
    }

    ${Button} {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
      margin-left: -1px;
    }

  ` : css`
    display: block;

    ${Button} {
      width: 100%;
    }
  `}
`;

const Input = styled.input`
  appearance: none;
  background-clip: padding-box;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: .375em;
  color: #212529;
  display: block;
  font-size: 1em;
  font-weight: 400;
  line-height: 1.5;
  padding: .375em .75em;
  width: 100%;
  margin: 0;

  &:focus {
    background-color: #fff;
    border-color: #9eb0ff;
    box-shadow: 0 0 0 0.25em rgba(60, 96, 255, .25);
    color: #212529;
    outline: 0;
  }
`;

const Button = styled.button`
  background-color: #3c60ff;
  border: 1px solid #3c60ff;
  border-radius: 0.375em;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-size: 1em;
  font-weight: 400;
  line-height: 1.5;
  padding: 0.375em 0.75em;
  text-align: center;
  text-decoration: none;
  user-select: none;
  vertical-align: middle;

  &:hover {
    background-color: #3352d9;
    border-color: #304dcc;
    color: #fff;
  }

  &:disabled {
    background-color: #3c60ff;
    border-color: #3c60ff;
    color: #fff;
    opacity: 0.65;
    pointer-events: none;
  }
`;
