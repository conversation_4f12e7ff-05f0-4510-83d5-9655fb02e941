import styled from 'styled-components';
import Loader from './loader';
import { useAsync, useAsyncCallback } from 'react-async-hook';
import axios from 'axios';
import { useEffect, useRef, useState } from 'react';
import { ReactComponent as RefreshIcon } from '../images/refresh.svg';
import { useDispatch, useSelector } from 'react-redux';

export default function Qrcode() {
    const unmountedRef = useRef(false);
    useEffect(() => {
        unmountedRef.current = false;
        return () => {
            unmountedRef.current = true;
        };
    }, []);

    const [params, onSuccess] = useSelector<any, any>(state => [state.options?.params, state.options?.onSuccess]);
    const dispatch = useDispatch();

    const [text, setText] = useState('请使用微信，扫码登录');
    const [expired, setExpired] = useState(false);

    const { execute: check } = useAsyncCallback(async (token) => {
        try {
            const { data } = await axios.get(`/passport/mini`, {
                params: { token, ...params },
                raxConfig: {
                    retryDelay: 2000,
                    backoffType: 'static',
                    shouldRetry(err) {
                        return !unmountedRef.current && err.response?.status === 449;
                    }
                }
            });

            //登录成功
            const { data: { token: loginToken } } = await axios.get(data.location);

            dispatch({ type: 'close' });
            onSuccess(loginToken);

        } catch {
            setText('小程序码已过期，请点击刷新');
            setExpired(true);
        }
    });

    //申请小程序码
    const { result, execute, loading } = useAsync(async () => {
        const response = await axios.get(`/mini/qrcode`);

        return response.data;
    }, [], {
        onSuccess(result) {
            setText('请使用微信，扫码登录');
            check(result.token);
        }
    });

    return <>
        <h4>微信登录</h4>
        <Container>
            {!result || loading ? <Loader /> : <img src={result.image} />}
            {expired && <Mask onClick={() => {
                setExpired(false);
                execute();
            }}><RefreshIcon /></Mask>}
        </Container>
        <h5>{text}</h5>
    </>;
}

const Mask = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, .5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #FFFFFF;

  svg {
    width: 90px;
    height: 90px;
  }
`;

const Container = styled.div`
  width: 198px;
  height: 198px;
  border-radius: 50%;
  border: 1px solid #e9ecef;
  margin: 0 auto 2em;
  overflow: hidden;
  padding: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
  }
`;
