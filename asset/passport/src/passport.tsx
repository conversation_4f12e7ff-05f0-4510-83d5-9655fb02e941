import axios from 'axios';
import { StyleSheetManager } from 'styled-components';
import ReactDOM from 'react-dom';
import App from './components/app';
import { createStore, Store } from 'redux';
import { Provider } from 'react-redux';
import * as rax from 'retry-axios';

rax.attach();

const isRecord = (data: any): data is Record<string, string> => {
    return data && (typeof data === 'object');
};

export default class Passport {

    readonly #store: Store;

    constructor(script: HTMLScriptElement) {
        axios.defaults.baseURL = new URL(script.src).origin;

        axios.interceptors.response.use(response => response, e => {
            if (axios.isAxiosError(e)) {
                if (e.response) {
                    const { data, status } = e.response;
                    let errors = data as string;
                    if (isRecord(data)) {
                        if (status === 422) {
                            errors = Object.values(data).join('<br />');
                        } else if ('message' in data) {
                            errors = data['message'];
                        }
                    }
                    return Promise.reject(new Error(errors));
                }
            }
            return Promise.reject(e);
        });

        const root = document.createElement('div');
        document.body.appendChild(root);

        const shadow = root.attachShadow({ mode: 'closed' });

        const app = document.createElement('div');
        shadow.appendChild(app);

        this.#store = createStore((state = { open: false }, action) => {
            switch (action.type) {
                case 'open':
                    return { open: true, options: action.options };
                case 'close':
                    return { open: false };
                default:
                    return state;
            }
        });

        const element = <StyleSheetManager target={shadow}>
            <Provider store={this.#store}>
                <App target={app} />
            </Provider>
        </StyleSheetManager>;

        ReactDOM.render(element, app);
    }


    login(options: { params: object, onSuccess: (data: any) => void }) {
        this.#store.dispatch({ type: 'open', options });
    }
}
