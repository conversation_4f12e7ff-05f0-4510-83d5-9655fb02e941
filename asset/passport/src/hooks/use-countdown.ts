import { useCallback, useEffect, useRef, useState } from 'react';

export default function useCountdown(): [number, (seconds: number) => void] {
    const [timeLeft, setTimeLeft] = useState(0);
    const timer = useRef<number>();

    useEffect(() => {
        return () => {
            if (timer.current) clearTimeout(timer.current);
        };
    }, []);

    const run = useCallback((seconds: number) => {
        if (timer.current) clearTimeout(timer.current);

        const countDown = () => {
            --seconds;

            if (seconds <= 0) {
                setTimeLeft(0);
            } else {
                setTimeLeft(seconds);
                timer.current = setTimeout(countDown, 1000);
            }
        };

        timer.current = setTimeout(countDown, 1000);
    }, []);

    return [timeLeft, run];
}
