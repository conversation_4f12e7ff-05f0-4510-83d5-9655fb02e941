{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["dom", "es2018"], "jsx": "react-jsx", "declaration": false, "sourceMap": true, "removeComments": false, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": false, "moduleResolution": "node", "baseUrl": "./", "esModuleInterop": true, "experimentalDecorators": true, "skipLibCheck": true, "strictNullChecks": true, "resolveJsonModule": true}, "include": ["./src/**/*"]}