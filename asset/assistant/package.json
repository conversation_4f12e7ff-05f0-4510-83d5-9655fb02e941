{"name": "@topthink/assistant", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev", "prepack": "npm run build"}, "main": "dist/assistant.min.js", "files": ["dist"], "devDependencies": {"@fingerprintjs/fingerprintjs": "^3.4.1", "@fortaine/fetch-event-source": "^3.0.6", "@react-hook/window-scroll": "^1.3.0", "@react-hook/window-size": "^3.1.1", "@reduxjs/toolkit": "^1.8.5", "@topthink/webpack-config-plugin": "^1.0.16", "@types/loadjs": "^4.0.1", "@types/lodash": "^4.14.161", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.11", "easyqrcodejs": "^4.4.13", "js-web-screen-shot": "1.9.8-rc.3", "katex": "^0.15.0", "loadjs": "^4.2.0", "lodash": "^4.17.21", "rc-rate": "^2.12.0", "rc-tooltip": "^5.3.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-draggable": "^4.4.5", "react-markdown": "^8.0.7", "react-redux": "^8.0.4", "react-textarea-autosize": "^8.5.3", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.2", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "styled-components": "^5.3.6", "typescript": "^4.0.2", "use-debounce": "^9.0.4", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}, "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "MIT", "dependencies": {"react-use-storage-state": "^1.0.6"}}