const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");
const Server              = require("webpack-dev-server");

module.exports = async (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const port = await Server.getFreePort();

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/index.ts",
        cache    : {
            type: "filesystem"
        },
        output   : {
            path         : path.resolve(__dirname, "dist"),
            filename     : "assistant.js",
            chunkFilename: "[id]-[contenthash:6].js",
            clean        : true
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve            : isServer,
                html             : false,
                react            : true,
                extractCss       : false,
                externalizeLodash: false
            })
        ],
        devServer: {
            hot         : true,
            port,
            headers     : {
                "Access-Control-Allow-Origin" : "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
            },
            allowedHosts: "all",
            client      : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            proxy       : {
                "/assistant": {
                    target : "http://www.topthink.org",
                    changeOrigin: true,
                }
            },
            compress    : false
        }
    };
};
