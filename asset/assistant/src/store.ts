import { configureStore } from '@reduxjs/toolkit';
import docReducer from './slice/doc';
import contextReducer from './slice/context';
import windowReducer from './slice/window';

export function createStore(preloadedState: any = {}) {
    return configureStore({
        reducer: {
            context: contextReducer,
            window: windowReducer,
            doc: docReducer
        },
        preloadedState
    });
}

export type StoreType = ReturnType<typeof createStore>

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<StoreType['getState']>
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = StoreType['dispatch']

