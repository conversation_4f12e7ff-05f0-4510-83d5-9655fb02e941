declare module '*.svg' {
    import { ReactElement, SVGProps } from 'react';
    export const ReactComponent: (props: SVGProps<SVGElement>) => ReactElement;
    const src: string;
    export default src;
}
declare module '*.gif';

interface BaseItem {
    icon: string;
    title: string;
}

interface UrlItem extends BaseItem {
    type: 'url';
    url: string;
}

interface ImageItem extends BaseItem {
    type: 'image';
    image: string;
    text?: string;
}

type CustomItem = UrlItem | ImageItem;

interface Broadcast {
    image: string;
    url?: string;
    position: 'bottom' | 'bottom-right';
    expire_time: string;
    height: number;
}

interface Feedback {
    id: string;
    question: {
        title: string;
        reason: {
            enable: false;
        } | {
            enable: true;
            text: string;
            screenshot: boolean;
        };
        complete: string;
    };
    distribution: {
        timing: {
            handle: 'visit' | 'click';
        } | {
            handle: 'delay';
            delay: number;
        };
        strategy: {
            enable: false;
        } | {
            enable: true;
            rate: 'mild' | 'moderate' | 'radical';
        };
    };
}

interface Robot {
    id: string;
    avatar: string;
    nickname: string;
    signature: string;
    welcome: string;
    url: string;
    distribution: {
        id: string;
        matching: string;
        type: string;
        value: string;
    }[];
    manual: {
        enable: false;
    } | {
        enable: true;
        type: 'text';
        text: string;
    } | {
        enable: true;
        type: 'image';
        image: string;
    };
    questions: {
        label: string;
        question: string;
    }[];
}

interface Config {
    appearance: {
        window: {
            tone: string;
            sideMargin: number;
            bottomMargin: number;
        },
        button: {
            color: string;
            background: string;
            size: 'small' | 'normal' | 'large';
            sideMargin: number;
            bottomMargin: number;
            hidden: boolean;
        };
    };
    chat: {
        enable: false;
    } | {
        enable: true;
        channel: 'hecong';
        channelId: string;
    } | {
        enable: true;
        channel: 'wechat';
        url: string;
    };
    doc: {
        enable: false;
    } | {
        enable: true;
        url: string;
    };
    broadcast: {
        enable: false;
    } | {
        enable: true;
    } & Broadcast;
    gotop: {
        enable: boolean;
    };
    robot: {
        enable: false;
    } | {
        enable: true;
    } & Robot;
    customs: CustomItem[];
    scripts: string;
    feedback?: Feedback;
}

interface Context {
    baseUrl: string;
    sessionId: string;
    config: Config;
}
