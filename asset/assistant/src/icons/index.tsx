import { ReactComponent as calendar } from './calendar.svg';
import { ReactComponent as chart } from './chart.svg';
import { ReactComponent as chat } from './chat.svg';
import { ReactComponent as doc } from './doc.svg';
import { ReactComponent as download } from './download.svg';
import { ReactComponent as edit } from './edit.svg';
import { ReactComponent as equipment } from './equipment.svg';
import { ReactComponent as exchange } from './exchange.svg';
import { ReactComponent as gotop } from './gotop.svg';
import { ReactComponent as group } from './group.svg';
import { ReactComponent as help } from './help.svg';
import { ReactComponent as message } from './message.svg';
import { ReactComponent as mobile } from './mobile.svg';
import { ReactComponent as mini } from './mini.svg';
import { ReactComponent as notice } from './notice.svg';
import { ReactComponent as qrcode } from './qrcode.svg';
import { ReactComponent as search } from './search.svg';
import { ReactComponent as send } from './send.svg';
import { ReactComponent as setting } from './setting.svg';
import { ReactComponent as share } from './share.svg';
import { ReactComponent as star } from './star.svg';
import { ReactComponent as task } from './task.svg';
import { ReactComponent as up } from './up.svg';
import { ReactComponent as wechat } from './wechat.svg';
import { ReactComponent as feedback } from './feedback.svg';


export default [
    {
        name: 'calendar',
        Component: calendar
    },
    {
        name: 'chart',
        Component: chart
    },
    {
        name: 'chat',
        Component: chat
    },
    {
        name: 'doc',
        Component: doc
    },
    {
        name: 'download',
        Component: download
    },
    {
        name: 'edit',
        Component: edit
    },
    {
        name: 'equipment',
        Component: equipment
    },
    {
        name: 'exchange',
        Component: exchange
    },
    {
        name: 'gotop',
        Component: gotop
    },
    {
        name: 'group',
        Component: group
    },
    {
        name: 'help',
        Component: help
    },
    {
        name: 'message',
        Component: message
    },
    {
        name: 'mobile',
        Component: mobile
    },
    {
        name: 'notice',
        Component: notice
    },
    {
        name: 'qrcode',
        Component: qrcode
    },
    {
        name: 'search',
        Component: search
    },
    {
        name: 'send',
        Component: send
    },
    {
        name: 'setting',
        Component: setting
    },
    {
        name: 'share',
        Component: share
    },
    {
        name: 'star',
        Component: star
    },
    {
        name: 'task',
        Component: task
    },
    {
        name: 'up',
        Component: up
    },
    {
        name: 'mini',
        Component: mini
    },
    {
        name: 'wechat',
        Component: wechat
    },
    {
        name: 'feedback',
        Component: feedback
    }
];
