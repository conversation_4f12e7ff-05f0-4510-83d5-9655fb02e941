import type Tas from './tas';

declare global {
    interface Window {
        tas: Tas | object;
    }
}

declare var __webpack_public_path__: string;

((window) => {
    const { document, } = window;

    const currentScript = document.currentScript as HTMLScriptElement;

    if (!currentScript) {
        return;
    }

    if (!currentScript.src.endsWith('.js')) {
        __webpack_public_path__ = currentScript.src + '/';
    }

    const list: [string, any[]][] = [];

    window.tas = new Proxy({}, {
        get: (_, property: string) => {
            return function (...args: any[]) {
                list.push([property, args]);
            };
        }
    });

    import('./tas').then(({ default: Tas }) => {
        const tas = new Tas(currentScript);

        list.forEach(([property, args]) => {
            // @ts-ignore
            tas[property](...args);
        });

        window.tas = tas;

        document.dispatchEvent(new CustomEvent('tas:ready'));
    });
})(window);
