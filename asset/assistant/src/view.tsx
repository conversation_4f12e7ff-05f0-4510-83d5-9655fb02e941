import { createStore, StoreType } from './store';
import { StyleSheetManager } from 'styled-components';
import { Provider } from 'react-redux';
import ReactDOM from 'react-dom';
import App from './components/app';
import { setMode, setUrl } from './slice/doc';
import { show as showWindow } from './slice/window';

export default class View {

    readonly #store: StoreType;

    constructor(context: Context) {

        const root = document.createElement('div');

        document.body.appendChild(root);

        const shadow = root.attachShadow({ mode: 'closed' });

        const app = document.createElement('div');
        shadow.appendChild(app);

        this.#store = createStore({ context });

        const element = <StyleSheetManager target={shadow}>
            <Provider store={this.#store}>
                <App />
            </Provider>
        </StyleSheetManager>;

        ReactDOM.render(element, app);

        //注册事件
        const { doc } = context.config;
        if (doc.enable) {
            document.addEventListener('click', (e) => {
                let ele: HTMLElement | null = e.target as HTMLElement;
                if (!ele.hasAttribute('data-tas-doc')) {
                    ele = ele.closest('[data-tas-doc]');
                }

                if (ele) {
                    const url = ele.getAttribute('href') || ele.getAttribute('data-url');
                    const mode = ele.getAttribute('data-tas-doc') || undefined;

                    if (url && url.startsWith(doc.url)) {
                        e.preventDefault();
                        this.openDoc(url, mode);
                    }
                }
            });
        }
    }

    public openDoc(url?: string, mode?: string) {
        if (url) {
            this.#store?.dispatch(setUrl(url));
        }
        if (mode) {
            this.#store?.dispatch(setMode(mode));
        }
        this.#store?.dispatch(showWindow('doc'));
    }

    public closeDoc() {
        this.#store?.dispatch(showWindow(undefined));
    }

    public toggleDoc() {
        if (this.#store) {
            const name = this.#store.getState().window.name;
            this.#store?.dispatch(showWindow(name === 'doc' ? undefined : 'doc'));
        }
    }

    public toggleChat() {
        if (this.#store) {
            const name = this.#store.getState().window.name;
            this.#store?.dispatch(showWindow(name === 'chat' ? undefined : 'chat'));
        }
    }

    public toggleFeedback() {
        if (this.#store) {
            const name = this.#store.getState().window.name;
            this.#store?.dispatch(showWindow(name === 'feedback' ? undefined : 'feedback'));
        }
    }

    public toggleCustom(index: number) {
        if (this.#store) {
            const name = this.#store.getState().window.name;
            this.#store?.dispatch(showWindow(name === `custom-${index}` ? undefined : `custom-${index}`));
        }
    }
}
