import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type WindowState = {
    name?: string
}

const initialState: WindowState = {};

export const windowSlice = createSlice({
    name: 'window',
    initialState,
    reducers: {
        show(state, action: PayloadAction<string | undefined>) {
            state.name = action.payload;
        },
        toggle(state, action: PayloadAction<string | undefined>) {
            state.name = state.name === action.payload ? undefined : action.payload;
        }
    }
});

export const { show, toggle } = windowSlice.actions;

export default windowSlice.reducer;
