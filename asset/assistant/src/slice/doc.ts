import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { show } from './window';

export type DocState = {
    mode: string
    url?: string
}

const initialState: DocState = {
    mode: 'normal'
};

export const docSlice = createSlice({
    name: 'doc',
    initialState,
    reducers: {
        setUrl(state, action: PayloadAction<string>) {
            state.url = action.payload;
        },
        setMode(state, action: PayloadAction<string>) {
            state.mode = action.payload;
        }
    },
    extraReducers(builder) {
        builder.addCase(show, (state, action) => {
            if (action.payload !== 'doc') {
                state.mode = 'normal';
            }
        });
    }
});

export const { setUrl, setMode } = docSlice.actions;

export default docSlice.reducer;
