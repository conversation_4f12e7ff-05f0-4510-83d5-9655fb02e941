import { createSlice } from '@reduxjs/toolkit';

const initialState: Context = {
    baseUrl: '',
    sessionId: '',
    config: {
        appearance: {
            window: {
                tone: '#3c60ff',
                sideMargin: 24,
                bottomMargin: 48
            },
            button: {
                color: '#585a5a',
                background: '#FFFFFF',
                size: 'normal',
                sideMargin: 24,
                bottomMargin: 48,
                hidden: false
            }
        },
        chat: {
            enable: false
        },
        doc: {
            enable: false
        },
        gotop: {
            enable: false
        },
        broadcast: {
            enable: false
        },
        robot: {
            enable: false
        },
        customs: [],
        scripts: ''
    }
};

export const contextSlice = createSlice({
    name: 'context',
    initialState,
    reducers: {},
});

export default contextSlice.reducer;
