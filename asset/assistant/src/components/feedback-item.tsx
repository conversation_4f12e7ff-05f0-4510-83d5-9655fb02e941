import { useAppSelector } from '../hooks';
import { useDispatch } from 'react-redux';
import { MenuItem } from './menu';
import { show } from '../slice/window';
import { ReactComponent as FeedbackIcon } from '../icons/feedback.svg';

export default function FeedbackItem() {
    const [visible] = useAppSelector(state => [state.window.name === 'feedback']);
    const dispatch = useDispatch();

    return <MenuItem tooltip={'反馈建议'} onClick={() => dispatch(show(visible ? undefined : 'feedback'))}><FeedbackIcon /></MenuItem>;
}
