import { MenuItem } from './menu';
import icons from '../icons';
import { useMemo } from 'react';
import { useAppDispatch } from '../hooks';
import { toggle } from '../slice/window';

interface Props {
    item: CustomItem;
    index: number;
}

const getIcon = (name: string) => {
    return icons.find(icon => icon.name === name);
};

export default function CustomItem({ item, index }: Props) {
    const Icon = useMemo(() => {
        return getIcon(item.icon)?.Component;
    }, [item.icon]);

    const dispatch = useAppDispatch();
    const name = `custom-${index}`;

    if (!Icon) {
        return null;
    }

    const icon = <Icon />;

    return <MenuItem
        tooltip={item.title}
        onClick={() => dispatch(toggle(name))}>
        {icon}
    </MenuItem>;
}
