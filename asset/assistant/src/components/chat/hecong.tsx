import { useEffect } from 'react';
import loadjs from 'loadjs';
import { useAppDispatch, useAppSelector } from '../../hooks';
import { show } from '../../slice/window';

declare global {
    interface Window {
        _AIHECONG: Function & { z?: Array<any>; };
    }
}

interface Props {
    channelId: string;
}

export default function Hecong({ channelId }: Props) {

    const dispatch = useAppDispatch();
    const [appearance, visible] = useAppSelector(({
        context: { config },
        window
    }) => [config.appearance, window.name === 'chat']);

    useEffect(() => {
        window['_AIHECONG'] = window['_AIHECONG'] || function () {
            (window['_AIHECONG'].z = window['_AIHECONG'].z || []).push(arguments);
        };

        window['_AIHECONG'].z = window['_AIHECONG'].z || [];
        window['_AIHECONG'].z.unshift(['ini', {
            channelId,
            button: false,
            appearance: {
                //电脑端聊天窗口样式
                panel: {
                    tone: appearance.window.tone, //主题颜色
                    position: 'right', //位置，left right
                    sideMargin: appearance.window.sideMargin, //侧边距
                    bottomMargin: appearance.window.bottomMargin, //底边距
                    radius: 5, //窗口圆角度
                    width: 370, //聊天窗宽度
                    height: 590, //聊天窗高度
                    headHeight: 75, //聊天窗口头部高度(客服头像、昵称区域)
                },
                panelMobile: {
                    tone: appearance.window.tone, //主题颜色
                    ratio: 'part', //是否占满屏幕 part 部分 whole 全部占满
                    headHeight: 75, //聊天窗口头部高度(客服头像、昵称区域)
                }
            }
        }]);

        window['_AIHECONG'].z.push(['visibleCallback', (v: 'show' | 'hide') => {
            if (v === 'show') {
                dispatch(show('chat'));
            }
            if (v === 'hide') {
                dispatch(show(undefined));
            }
        }]);

        loadjs('https://static.ahc.ink/hecong.js', { async: true, });
    }, []);

    useEffect(() => {
        if (visible) {
            window['_AIHECONG']('showChat');
        } else {
            window['_AIHECONG']('hideChat');
        }
    }, [visible]);

    return null;
}
