import styled from 'styled-components';
import { useAppSelector } from '../../hooks';
import QRCode from 'easyqrcodejs';
import { useEffect, useRef } from 'react';
import WechatIcon from '../../images/wechat.svg';

interface Props {
    url: string;
}

function QRCodeSVG({ url }: Props) {
    const dom = useRef(null);

    useEffect(() => {
        if (dom.current) {
            const options = {
                text: url,
                width: 160,
                height: 160,
                dotScale: 0.6,
                dotScaleTiming: 0.6,
                correctLevel: QRCode.CorrectLevel.M,
                logo: WechatIcon,
                logoWidth: 43,
                logoHeight: 43,
            };
            new QRCode(dom.current, options);
        }
    }, [url]);

    return <Container ref={dom}>
        <p>微信扫一扫</p>
    </Container>;
}

export default function Wechat({ url }: Props) {

    const [visible] = useAppSelector(state => [state.window.name === 'chat']);

    if (!visible) {
        return null;
    }

    return <QRCodeSVG url={url} />;
}

const Container = styled.div`
  position: fixed;
  right: var(--tas-window-side-margin, 80px);
  bottom: var(--tas-window-bottom-margin, 48px);
  z-index: 4000;
  background: #ffffff;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);
  padding: 16px;
  display: flex;
  flex-direction: column-reverse;

  p {
    margin: 10px 0 0;
    text-align: center;
  }
`;
