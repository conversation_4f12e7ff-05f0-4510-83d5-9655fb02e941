import styled, { css } from 'styled-components';
import TextareaAutosize from 'react-textarea-autosize';
import { ReactComponent as SendIcon } from '../../images/send.svg';
import { ReactComponent as DownIcon } from '../../images/down.svg';
import { ReactComponent as RobotIcon } from '../../images/robot.svg';
import { Fragment, ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useAppDispatch, useAppSelector } from '../../hooks';
import { show } from '../../slice/window';
import { EventStreamContentType, fetchEventSource, } from '@fortaine/fetch-event-source';
import Markdown from '../markdown';
import Qrcode from './manual/qrcode';
import Hecong from './manual/hecong';
import Image from './manual/image';
import { useStorageState } from 'react-use-storage-state';
import formatTime from '../../utils/format-time';

interface Message {
    role: 'user' | 'assistant';
    content: string;
    timestamp?: number;
}

interface Props {
    robot: Robot;
}

export default function Chat({ robot }: Props) {
    const dispatch = useAppDispatch();

    const [chat] = useAppSelector(({ context: { config } }) => [config.chat]);
    const [question, setQuestion] = useState('');
    const [messages, setMessages] = useStorageState<Message[]>('_tas_robot_messages', []);

    useEffect(() => {
        const last = messages.filter(m => m.timestamp).at(-1)?.timestamp;
        //超过24小时插入一条新消息
        if (!last || Date.now() - last > 1000 * 60 * 60 * 24) {
            setMessages((messages) => {
                return [
                    ...messages,
                    {
                        role: 'assistant',
                        content: robot.welcome || '您好，有什么可以帮您的吗？',
                        timestamp: Date.now(),
                    }
                ];
            });
        }
    }, []);

    const url = useMemo(() => {
        const matched = robot.distribution.find((item) => {
            const text = location.href;
            switch (item.matching) {
                case 'contain':
                    return text.includes(item.value);
                case 'not':
                    return !text.includes(item.value);
                case 'start':
                    return text.startsWith(item.value);
                case 'end':
                    return text.endsWith(item.value);
            }
        });
        if (matched) {
            return robot.url.replace('%id%', matched.id);
        }
        return robot.url.replace('%id%', robot.id);
    }, [location.href]);

    const [loading, setLoading] = useState(false);

    const ask = async (question: string) => {
        setLoading(true);

        setMessages((messages) => {
            const last = messages.filter(m => m.timestamp).at(-1)?.timestamp;

            const show = !last || Date.now() - last > 1000 * 60 * 5;

            return [
                ...messages,
                {
                    role: 'user',
                    content: question,
                    timestamp: show ? Date.now() : undefined,
                },
                {
                    role: 'assistant',
                    content: '',
                }
            ];
        });

        const headers: Record<string, string> = {
            'content-type': 'application/json',
            'accept': 'text/event-stream,application/json',
        };

        let finished = false;

        await fetchEventSource(url, {
            method: 'POST',
            body: JSON.stringify({
                question,
                plugins: robot.manual.enable || chat.enable ? ['manual'] : undefined
            }),
            headers,
            async onopen(res) {
                if (
                    !res.ok ||
                    !res.headers
                        .get('content-type')
                        ?.startsWith(EventStreamContentType) ||
                    res.status !== 200
                ) {
                    finished = true;

                    let errorMessage = '网络错误，请重试';

                    try {
                        const resJson = await res.clone().json();
                        errorMessage = resJson.message || '';
                    } catch {
                    }

                    setMessages((messages) => {
                        messages[messages.length - 1].content = `[${errorMessage}]`;
                        return messages.concat();
                    });
                }
            },
            async onmessage(msg) {
                if (msg.data === '[DONE]' || finished) {
                    finished = true;
                    return;
                }

                try {
                    const text = JSON.parse(msg.data);
                    setMessages((messages) => {
                        messages[messages.length - 1].content += text;
                        return messages.concat();
                    });
                } catch {

                }
            },
            async onclose() {
                finished = true;
                //完成
                setLoading(false);
            },
            openWhenHidden: true,
        });
    };

    const onSend = () => {
        if (question) {
            setQuestion('');
            ask(question);
        }
    };

    const scrollRef = useRef<HTMLDivElement>(null);

    const scrollToBottom = useCallback(() => {
        const dom = scrollRef.current;
        if (dom) {
            requestAnimationFrame(() => dom.scrollTo(0, dom.scrollHeight));
        }
    }, []);

    // auto scroll
    useEffect(() => {
        scrollToBottom();
    });

    let manual: ReactNode = null;
    if (robot.manual.enable) {
        switch (robot.manual.type) {
            case 'text':
                manual = <Markdown content={robot.manual.text} parentRef={scrollRef} />;
                break;
            case 'image':
                manual = <Image image={robot.manual.image} />;
                break;
        }
    } else if (chat.enable) {
        switch (chat.channel) {
            case 'hecong':
                manual = <Hecong />;
                break;
            case 'wechat':
                manual = <Qrcode url={chat.url} />;
                break;
        }
    } else {
        manual = '[暂不支持人工客服]';
    }

    return <>
        <Header>
            <Avatar>
                {robot.avatar ? <img src={robot.avatar} /> : <RobotIcon />}
            </Avatar>
            <Info>
                <Nickname>{robot.nickname || '小智'}</Nickname>
                <Signature>{robot.signature || '我是智能客服呢'}</Signature>
            </Info>
            <Close onClick={() => dispatch(show(undefined))}>
                <DownIcon />
            </Close>
        </Header>
        <Body>
            <MessageBox style={{ paddingBottom: robot.questions.length > 0 ? '45px' : 0 }} ref={scrollRef}>
                {messages.map((message, index) => {
                    return <Fragment key={`index-${index}`}>
                        {message.timestamp && <Timestamp>
                            <span>{formatTime(message.timestamp)}</span>
                        </Timestamp>}
                        <Message $reverse={message.role === 'user'}>
                            {message.role === 'assistant' && <MessageAvatar>
                                {robot.avatar ? <img src={robot.avatar} /> : <RobotIcon />}
                            </MessageAvatar>}
                            <MessageBody>
                                {message.content === '%manual%' ? manual : <Markdown
                                    content={message.content.replace(/%manual%/g, '')}
                                    loading={message.content.length === 0 && message.role === 'assistant'}
                                    parentRef={scrollRef}
                                />}
                            </MessageBody>
                        </Message>
                    </Fragment>;
                })}
            </MessageBox>
        </Body>
        <Footer>
            <SendArea>
                <InputArea>
                    <TextareaAutosize
                        disabled={loading}
                        value={question}
                        onChange={e => setQuestion(e.target.value)}
                        onKeyDown={e => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                onSend();
                            }
                        }}
                        placeholder='请输入'
                        autoFocus
                    />
                </InputArea>
                {question && <SendButton onClick={e => {
                    e.preventDefault();
                    onSend();
                }}>
                    <SendIcon />
                </SendButton>}
            </SendArea>
            {robot.questions.length > 0 && <Questions>
                {robot.questions.map((item, index) => {
                    return <QuestionsItem onClick={() => {
                        if (!loading) {
                            ask(item.question);
                        }
                    }} key={index}>{item.label}</QuestionsItem>;
                })}
            </Questions>}
        </Footer>
    </>;
}

const QuestionsItem = styled.a`
    font-size: 13px;
    height: 28px;
    padding: 0px 12px;
    color: #363636;
    box-shadow: 0 0px 1px 0 rgba(0,0,0,0.2);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    line-height: 1.5;
    background-color: #FFFFFF;
    border-radius: 290486px;
    cursor: pointer;
    white-space: nowrap;
    
    &:hover {
        background-color: #f9f9f9;
        border-color: transparent;
        color: #0a0a0a;
    }
`;

const Questions = styled.div`
    position: absolute;
    top: -45px;
    left: 0px;
    z-index: 100;
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    gap: 10px;
    overflow-x: auto;
`;

const Timestamp = styled.div`
  display: flex;
  justify-content: center;
  padding: 15px 0px 0px;

  span {
    font-size: 12px;
    color: #b2b2b2;
    white-space: nowrap;
  }
`;

const MessageBody = styled.div`
  padding: 8px 15px;
  background-color: #f3f3f3;
  border-radius: 4px;
  max-width: 80%;
  margin-left: 10px;
  position: relative;

  &:before {
    position: absolute;
    top: 10px;
    left: -10px;
    width: 0;
    height: 0;
    content: "";
    border: 5px solid transparent;
    border-right-color: #f3f3f3;
  }
`;

const MessageAvatar = styled.div`
  width: 35px;
  height: 35px;

  img, svg {
    width: 100%;
    height: 100%;
  }
`;

const Message = styled.div<{ $reverse: boolean; }>`
  display: flex;
  margin-top: 16px;
  margin-bottom: 16px;
  padding-left: 15px;
  padding-right: 15px;
  justify-content: flex-start;

  ${props => props.$reverse && css`
    justify-content: flex-end;

    ${MessageBody} {
      background: var(--tas-window-tone, #3c60ff);
      color: #FFFFFF;
      margin-left: 0;
    }
  `};
`;

const MessageBox = styled.div`
  flex: 1;
  overflow: auto;
`;

const SendButton = styled.div`
  color: #ccc;
  cursor: pointer;
  display: flex;
  height: 55px;
  padding-right: 20px;
  align-items: center;

  svg {
    width: 19px;
    height: 19px;
  }

  &:hover {
    color: var(--tas-window-tone, #3c60ff);
  }
`;

const InputArea = styled.div`
  flex: 1;

  textarea {
    width: 100%;
    resize: none;
    background: transparent;
    font-size: 14px;
    padding: 17px 17px 17px 25px;
    height: 55px;
    box-shadow: none;
    overflow: hidden;
    max-height: 150px;
    min-height: 100%;
    line-height: 1.5;
    outline: none;
    border: none;
    vertical-align: bottom;

    &::placeholder {
      color: rgba(54, 54, 54, .3);
    }
  }
`;

const SendArea = styled.div`
  display: flex;
`;

const Footer = styled.div`
  min-height: 55px;
  background: #fff;
  position: relative;

  &:after {
    content: "";
    pointer-events: none;
    box-sizing: border-box;
    position: absolute;
    width: 200%;
    height: 200%;
    top: 0;
    border-top: 1px solid #ddd;
    transform: scale(0.5);
    transform-origin: left top;
  }
`;

const Body = styled.div`
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #fafafa;
  display: flex;
`;

const Close = styled.div`
  height: 75px;
  padding-top: 16px;
  width: 24px;
  display: flex;
  justify-content: center;
  cursor: pointer;

  svg {
    width: 18px;
    height: 18px;
  }
`;

const Signature = styled.div`
  margin-top: 3px;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  font-size: 13px;
`;

const Nickname = styled.div`
  font-size: 16px;
  height: 16px;
  line-height: 1;
`;

const Info = styled.div`
  flex: 1;
  font-weight: normal;
`;

const Avatar = styled.div`
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--tas-window-tone, #3c60ff);

  img, svg {
    border-radius: 50%;
    width: 100%;
    height: 100%;
  }

`;

const Header = styled.div`
  height: 75px;
  background: var(--tas-window-tone, #3c60ff);
  overflow: hidden;
  padding: 0 15px 0 20px;
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.95);
`;
