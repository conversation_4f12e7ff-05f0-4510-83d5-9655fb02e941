import { useEffect, useRef } from 'react';
import QRCode from 'easyqrcodejs';
import WechatIcon from '../../../images/wechat.svg';
import styled from 'styled-components';

export default function Qrcode({ url }: { url: string; }) {
    const dom = useRef(null);

    useEffect(() => {
        if (dom.current) {
            const options = {
                text: url,
                width: 160,
                height: 160,
                dotScale: 0.6,
                dotScaleTiming: 0.6,
                correctLevel: QRCode.CorrectLevel.M,
                logo: WechatIcon,
                logoWidth: 43,
                logoHeight: 43,
                quietZone: 8,
            };
            new QRCode(dom.current, options);
        }
    }, [url]);

    return <Container ref={dom}>
        <p>微信扫一扫</p>
    </Container>;
}

const Container = styled.div`
  display: flex;
  flex-direction: column-reverse;
  padding: 7px 0 0;

  p {
    margin: 10px 0 0;
    text-align: center;
  }
`;
