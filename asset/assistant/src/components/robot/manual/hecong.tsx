import styled from 'styled-components';
import { ReactComponent as ManualIcon } from '../../../images/manual.svg';
import { useAppDispatch } from '../../../hooks';
import { show } from '../../../slice/window';

export default function Hecong() {
    const dispatch = useAppDispatch();

    return <Container onClick={() => dispatch(show('chat'))}><ManualIcon />联系人工客服</Container>;
}

const Container = styled.div`
  background: var(--tas-window-tone, #3c60ff);
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 3px 10px;
  border-radius: 12px;
  line-height: 20px;
  cursor: pointer;
`;
