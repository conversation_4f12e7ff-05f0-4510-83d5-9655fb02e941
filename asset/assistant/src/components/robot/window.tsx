import styled from 'styled-components';
import Draggable, { DraggableEventHandler } from 'react-draggable';
import { useWindowSize } from '@react-hook/window-size';
import { useCallback, useEffect, useState } from 'react';
import { useAppSelector } from '../../hooks';
import Chat from './chat';

interface Props {
    robot: Robot;
}

export default function Window({ robot }: Props) {
    const [appearance] = useAppSelector(({
        context: { config }
    }) => [config.appearance]);

    const [winWidth, winHeight] = useWindowSize();

    const getPosition = useCallback((winWidth: number, winHeight: number) => {
        return {
            x: winWidth - 370 - appearance.window.sideMargin,
            y: winHeight - 590 - appearance.window.bottomMargin
        };
    }, [appearance]);

    const [position, setPosition] = useState(() => {
        return getPosition(winWidth, winHeight);
    });

    useEffect(() => {
        setPosition(getPosition(winWidth, winHeight));
    }, [winWidth, winHeight]);

    const onStop = useCallback<DraggableEventHandler>((_, data) => {
        setPosition({ x: data.x, y: data.y });
    }, []);

    return <Root>
        <Draggable
            handle='.handle'
            position={position}
            onStop={onStop}
        >
            <Wrap>
                <Handle className='handle' />
                <Chat robot={robot} />
            </Wrap>
        </Draggable>
    </Root>;
}

const Handle = styled.div`
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 15px;
`;

const Wrap = styled.div`
  position: absolute;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);
  display: flex;
  flex-direction: column;
  width: 370px;
  height: 590px;
  color: #333;
  font-size: 14px;
  background: #fafafa;
`;

const Root = styled.div`
  position: fixed;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  z-index: 5000;

  * {
    box-sizing: border-box;
  }
`;
