import ReactMarkdown from 'react-markdown';
import 'katex/dist/katex.min.css';
import RemarkMath from 'remark-math';
import RemarkBreaks from 'remark-breaks';
import RehypeKatex from 'rehype-katex';
import RemarkGfm from 'remark-gfm';
import RehypeHighlight from 'rehype-highlight';
import React, { RefObject, useEffect, useRef, useState } from 'react';
import { useThrottledCallback } from 'use-debounce';
import styled from 'styled-components';
import { ReactComponent as LoadingIcon } from '../images/three-dots.svg';

function _MarkDownContent(props: { content: string }) {
    return <ReactMarkdown
        remarkPlugins={[[RemarkMath, { singleDollarTextMath: false }], RemarkGfm, RemarkBreaks]}
        rehypePlugins={[
            RehypeKatex,
            [
                RehypeHighlight,
                {
                    detect: false,
                    ignoreMissing: true,
                },
            ],
        ]}
        components={{
            a: (aProps) => {
                return <a {...aProps} target='_blank' />;
            },
        }}
    >
        {props.content}
    </ReactMarkdown>;
}

export const MarkdownContent = React.memo(_MarkDownContent);

export default function Markdown(
    props: {
        content: string;
        loading?: boolean;
        fontSize?: number;
        parentRef?: RefObject<HTMLDivElement>;
        defaultShow?: boolean;
    } & React.DOMAttributes<HTMLDivElement>,
) {
    const mdRef = useRef<HTMLDivElement>(null);
    const renderedHeight = useRef(0);
    const renderedWidth = useRef(0);
    const inView = useRef(!!props.defaultShow);
    const [_, triggerRender] = useState(0);
    const checkInView = useThrottledCallback(
        () => {
            const parent = props.parentRef?.current;
            const md = mdRef.current;
            if (parent && md && !props.defaultShow) {
                const parentBounds = parent.getBoundingClientRect();
                const twoScreenHeight = Math.max(500, parentBounds.height * 2);
                const mdBounds = md.getBoundingClientRect();
                const parentTop = parentBounds.top - twoScreenHeight;
                const parentBottom = parentBounds.bottom + twoScreenHeight;
                const isOverlap =
                    Math.max(parentTop, mdBounds.top) <=
                    Math.min(parentBottom, mdBounds.bottom);
                inView.current = isOverlap;
                triggerRender(Date.now());
            }

            if (inView.current && md) {
                const rect = md.getBoundingClientRect();
                renderedHeight.current = Math.max(renderedHeight.current, rect.height);
                renderedWidth.current = Math.max(renderedWidth.current, rect.width);
            }
        },
        300,
        {
            leading: true,
            trailing: true,
        },
    );

    useEffect(() => {
        props.parentRef?.current?.addEventListener('scroll', checkInView);
        checkInView();
        return () =>
            props.parentRef?.current?.removeEventListener('scroll', checkInView);
    }, []);

    const getSize = (x: number) => (!inView.current && x > 0 ? x : 'auto');

    return <Container
        style={{
            fontSize: `${props.fontSize ?? 14}px`,
            height: getSize(renderedHeight.current),
            width: getSize(renderedWidth.current),
        }}
        ref={mdRef}
        onContextMenu={props.onContextMenu}
        onDoubleClickCapture={props.onDoubleClickCapture}
    >
        {inView.current &&
            (props.loading ? (
                <LoadingIcon />
            ) : (
                <MarkdownContent content={props.content} />
            ))}
    </Container>;

}

const Container = styled.div`
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  line-height: 1.2;
  word-wrap: break-word;

  & > *:first-child {
    margin-top: 0 !important;
  }

  & > *:last-child {
    margin-bottom: 0 !important;
  }

  h1, h2, h3, h4, h5, h6, p, ul, ol, blockquote, pre, table, audio, video {
    margin: 1em 0;
    line-height: 1.2;
    padding: 0;

    &:last-child {
      margin-bottom: 0 !important;
    }

    &:first-child {
      margin-top: 0 !important;
    }
  }

  p {
    margin: 0;
    line-height: 2;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: normal;

    &.align-right {
      text-align: right;
    }

    &.align-center {
      text-align: center;
    }
  }

  h1 {
    font-size: 2em;
    line-height: 1.2;
    border-bottom: 1px solid #eeeeee;
    padding-bottom: .5em;
  }

  h2 {
    padding-bottom: .4em;
    font-size: 1.3em;
    line-height: 1.2;
    border-bottom: 1px solid #eeeeee
  }

  h3 {
    font-size: 1.2em;
    line-height: 1.2;
  }

  h4 {
    font-size: 1.1em;
    line-height: 1.2;
  }

  h5 {
    font-size: 1em;
    line-height: 1.2;
  }

  hr {
    border: 0;
    border-bottom: 1px solid #eeeeee;
    margin-bottom: 0.5em;
  }

  ul {
    padding-left: 2em;

    ul {
      margin: 0;
    }
  }

  ol {
    padding-left: 2em;
  }

  li {
    line-height: 2;

    &.task-list-item {
      list-style: none;
      position: relative;

      input[type=checkbox] {
        position: absolute;
        left: -1.5em;
        top: 0.5em;
        width: 1em;
        height: 1em;
      }
    }

    &.align-right {
      text-align: right;
    }

    &.align-center {
      text-align: center;
    }
  }

  code {
    display: inline-block;
    border-radius: 4px;
    padding: .2em .4em;
    background-color: #f7f7f7;
    word-break: break-all;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    white-space: pre;
    line-height: 1.3;
    color: inherit;
    font-size: inherit;
    margin: -.2em .4em;
  }

  pre {
    padding: 1.05em;
    overflow: auto;
    line-height: 1.45;
    background-color: #f7f7f7;
    border: 0;
    border-radius: 3px;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    font-size: inherit;
  }


  pre > code {
    display: inline;
    max-width: 100%;
    padding: 0;
    margin: 0;
    overflow: initial;
    line-height: inherit;
    background-color: rgba(0, 0, 0, 0);
    border: 0;
    tab-size: 4;
  }

  p {
    &.align-right {
      text-align: right;
    }

    &.align-center {
      text-align: center;
    }
  }

  table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    line-height: 1.8;
    border-spacing: 0;

    th {
      text-align: left;

      &[align='center'] {
        text-align: center;
      }

      &[align='right'] {
        text-align: right;
      }
    }

    th, td {
      border: 1px solid #e0e0e0;
      padding: 3px 12px;
      height: 34px;
    }

    tr:hover td {
      background-color: #f1f1f1;
    }
  }

  img {
    max-width: 100%;
    display: inline-block;
    padding: 3px;
    vertical-align: middle;
  }

  a {
    text-decoration: none;
    color: var(--bs-primary);

    &:hover {
      text-decoration: underline;
    }
  }

  blockquote {
    padding: 5px 5px 5px 15px;
    color: #777777;
    border-left: 4px solid #dddddd;
  }

  video {
    display: block;
    width: 100%;
  }

  audio {
    display: block;
    width: 100%;
  }

  pre {
    padding: 0;
  }

  pre,
  code {
    font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  }

  pre code {
    display: block;
    overflow-x: auto;
    padding: 1em;
  }

  code {
    padding: 3px 5px;
  }

  .hljs,
  pre {
    background: #1a1b26;
    color: #cbd2ea;
  }

  .hljs-comment,
  .hljs-meta {
    color: #565f89;
  }

  .hljs-deletion,
  .hljs-doctag,
  .hljs-regexp,
  .hljs-selector-attr,
  .hljs-selector-class,
  .hljs-selector-id,
  .hljs-selector-pseudo,
  .hljs-tag,
  .hljs-template-tag,
  .hljs-variable.language_ {
    color: #f7768e;
  }

  .hljs-link,
  .hljs-literal,
  .hljs-number,
  .hljs-params,
  .hljs-template-variable,
  .hljs-type,
  .hljs-variable {
    color: #ff9e64;
  }

  .hljs-attribute,
  .hljs-built_in {
    color: #e0af68;
  }

  .hljs-keyword,
  .hljs-property,
  .hljs-subst,
  .hljs-title,
  .hljs-title.class_,
  .hljs-title.class_.inherited__,
  .hljs-title.function_ {
    color: #7dcfff;
  }

  .hljs-selector-tag {
    color: #73daca;
  }

  .hljs-addition,
  .hljs-bullet,
  .hljs-quote,
  .hljs-string,
  .hljs-symbol {
    color: #9ece6a;
  }

  .hljs-code,
  .hljs-formula,
  .hljs-section {
    color: #7aa2f7;
  }

  .hljs-attr,
  .hljs-char.escape_,
  .hljs-keyword,
  .hljs-name,
  .hljs-operator {
    color: #bb9af7;
  }

  .hljs-punctuation {
    color: #c0caf5;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }
`;

