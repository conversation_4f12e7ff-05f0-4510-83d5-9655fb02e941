import styled from 'styled-components';
import { useAppDispatch, useAppSelector } from '../hooks';
import { useEffect } from 'react';
import { show } from '../slice/window';

export default function Custom({ item, index }: { item: CustomItem, index: number }) {
    const name = `custom-${index}`;
    const [visible] = useAppSelector(state => [state.window.name === name]);

    if (visible) {
        switch (item.type) {
            case 'image':
                return <ImageContainer>
                    {item.text && <p>{item.text}</p>}
                    <img src={item.image} />
                </ImageContainer>;
            case 'url':
                return <UrlContainer url={item.url} />;
        }
    }

    return null;
}

const UrlContainer = ({ url }: { url: string }) => {

    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(show(undefined));
        window.open(url, '_blank');
    });
    return null;
};

const ImageContainer = styled.div`
  position: fixed;
  right: var(--tas-window-side-margin, 80px);
  bottom: var(--tas-window-bottom-margin, 48px);
  z-index: 4000;
  background: #ffffff;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);
  padding: 16px;
  display: flex;
  flex-direction: column-reverse;

  img {
    margin: 0;
    padding: 0;
    max-width: 300px;
    max-height: 300px;
  }

  p {
    margin: 10px 0 0;
    text-align: center;
    color: #333;
  }
`;
