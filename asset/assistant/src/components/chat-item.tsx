import { ReactComponent as ChatI<PERSON> } from '../icons/chat.svg';
import { MenuItem } from './menu';
import { useAppDispatch, useAppSelector } from '../hooks';
import { show } from '../slice/window';
import { useCallback } from 'react';

export default function ChatItem() {

    const dispatch = useAppDispatch();
    const { visible, robot } = useAppSelector(({ window, context: { config: { robot } } }) => ({
        visible: window.name === 'chat' || window.name === 'robot',
        robot
    }));

    const toggle = useCallback(() => {
        if (visible) {
            dispatch(show(undefined));
        } else {
            dispatch(show(robot.enable ? 'robot' : 'chat'));
        }
    }, [visible]);

    return <MenuItem tooltip={'在线客服'} onClick={toggle}><ChatIcon /></MenuItem>;
};
