import Menu from './menu';
import Document from './document';
import { useAppSelector } from '../hooks';
import styled from 'styled-components';
import Hecong from './chat/hecong';
import Wechat from './chat/wechat';
import Broadcast from './broadcast';
import Custom from './custom';
import Feedback from './feedback';
import { lazy, Suspense } from 'react';

const Robot = lazy(() => import('./robot'));

export default function App() {

    const [
        appearance,
        robot,
        doc,
        chat,
        broadcast,
        customs,
        feedback
    ] = useAppSelector(({ context: { config } }) => [config.appearance, config.robot, config.doc, config.chat, config.broadcast, config.customs, config.feedback]);

    let chatWindow = null;
    if (chat.enable) {
        switch (chat.channel) {
            case 'hecong':
                chatWindow = <Hecong channelId={chat.channelId} />;
                break;
            case 'wechat':
                chatWindow = <Wechat url={chat.url} />;
                break;
        }
    }

    return <Container appearance={appearance}>
        {!appearance.button.hidden && <Menu />}
        {doc.enable && <Document base={doc.url} />}
        {broadcast.enable && <Broadcast broadcast={broadcast} />}
        {feedback && <Feedback feedback={feedback} />}
        {robot.enable && <Suspense fallback={null}><Robot robot={robot} /></Suspense>}
        {chatWindow}
        {customs.map((item, index) => <Custom index={index} key={index} item={item} />)}
    </Container>;
}

const Container = styled.div<{ appearance: Config['appearance'] }>`
  --tas-window-tone: ${props => props.appearance.window.tone};
  --tas-window-side-margin: ${props => props.appearance.window.sideMargin}px;
  --tas-window-bottom-margin: ${props => props.appearance.window.bottomMargin}px;
  --tas-button-color: ${props => props.appearance.button.color};
  --tas-button-background: ${props => props.appearance.button.background};
  --tas-button-size: ${props => ({
    small: '30px',
    normal: '40px',
    large: '50px'
  }[props.appearance.button.size])};
  --tas-button-side-margin: ${props => props.appearance.button.sideMargin}px;
  --tas-button-bottom-margin: ${props => props.appearance.button.bottomMargin}px;

  * {
    font-family: PingFang SC, Helvetica Neue, Helvetica, Arial, Hiragino Sans GB, Heiti SC, Microsoft YaHei, WenQuanYi Micro Hei, sans-serif;;
  }
`;
