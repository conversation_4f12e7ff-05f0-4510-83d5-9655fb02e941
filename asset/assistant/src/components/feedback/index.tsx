import styled, { css, keyframes } from 'styled-components';
import CloseButton from '../close-button';
import Rate from 'rc-rate';
import { ReactComponent as StarIcon } from '../../images/star.svg';
import StarsCrop from '../../images/stars-crop.gif';
import { lazy, Suspense, useEffect, useRef, useState } from 'react';
import { useAppSelector } from '../../hooks';
import { show } from '../../slice/window';
import { useDispatch } from 'react-redux';
import { dataUriToFile } from '../../utils/data-uri-to-file';

const ScreenshotButton = lazy(() => import('./screenshot-button'));

const texts = ['非常不满意', '不满意', '一般', '满意', '非常满意'] as const;

interface Props {
    feedback: Feedback;
}

type STEP = 'star' | 'reason' | 'complete';

const HEIGHTS = {
    star: 177.5,
    reason: 377.5,
    complete: 194,
};

const getStorageItem = (id: string, suffix: string) => localStorage.getItem(`_tas_f_${id}_${suffix}`);
const setStorageItem = (id: string, suffix: string, value: string) => localStorage.setItem(`_tas_f_${id}_${suffix}`, value);

function Window({ feedback }: Props) {
    const { question } = feedback;

    const [baseUrl, sessionId] = useAppSelector(({ context: { baseUrl, sessionId } }) => [baseUrl, sessionId]);
    const dispatch = useDispatch();

    const [focus, setFocus] = useState(false);
    const [step, setStep] = useState<STEP>('star');
    const [star, setStar] = useState(0);
    const [reason, setReason] = useState<string>();
    const [screenshot, setScreenshot] = useState<string>();

    const onSubmit = () => {
        setStep('complete');

        //提交数据
        const body = new FormData();
        body.append('sessionId', sessionId);
        body.append('feedback', feedback.id);
        body.append('star', String(star));
        if (reason) body.append('reason', reason);
        if (screenshot) {
            body.append('screenshot', dataUriToFile(screenshot, 'screenshot.png'));
        }

        //记录已经提交数据
        setStorageItem(feedback.id, 's', 'true');

        fetch(`${baseUrl}/feedback`, {
            method: 'POST',
            body: body
        });

        //关闭窗口
        setTimeout(() => {
            dispatch(show(undefined));
        }, 2000);
    };

    useEffect(() => {
        if (star && step == 'star') {
            if (question.reason.enable) {
                setStep('reason');
            } else {
                onSubmit();
            }
        }
    }, [star]);

    const textarea = useRef<HTMLTextAreaElement>(null);

    return <Container style={{ height: HEIGHTS[step] }}>
        <CloseButton onClick={() => dispatch(show(undefined))} />
        {step == 'complete' ? <Complete>
                <img src={StarsCrop} />
                <p>{question.complete}</p>
            </Complete> :
            <>
                <Title>{question.title}</Title>
                <Rate
                    allowClear={false}
                    value={star}
                    onChange={(value) => {
                        textarea.current?.focus();
                        setStar(value);
                    }}
                    character={({ index, value }) => {
                        index = Number(index);
                        value = Number(value);
                        return <>
                            <StarIcon />
                            {(value - 1) === index && <Text><span>{texts[index]}</span></Text>}
                        </>;
                    }}
                />
                {step == 'reason' && question.reason.enable && <Reason>
                    <Textarea $focus={focus}>
                        <textarea
                            ref={textarea}
                            autoFocus
                            onFocus={() => setFocus(true)}
                            onBlur={() => setFocus(false)}
                            placeholder={question.reason.text}
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                        />
                        {question.reason.screenshot && <Suspense fallback={null}>
                            <ScreenshotButton
                                active={!!screenshot}
                                onComplete={(base64) => {
                                    textarea.current?.focus();
                                    setScreenshot(base64);
                                }}
                            />
                        </Suspense>}
                    </Textarea>
                    <Button onClick={() => onSubmit()}>提交</Button>
                </Reason>}
            </>
        }
        <PoweredBy>
            <a href='https://www.topthink.com/product/assistant' target='_blank'>顶想云提供技术支持</a>
        </PoweredBy>
    </Container>;
}

export default function Feedback({ feedback }: Props) {
    const [visible] = useAppSelector(state => [state.window.name === 'feedback']);
    const dispatch = useDispatch();
    const { distribution } = feedback;

    useEffect(() => {
        //自动弹出
        if (distribution.timing.handle != 'click') {
            //是否已经提交过
            if (getStorageItem(feedback.id, 's')) {
                return;
            }

            if (distribution.strategy.enable) {
                switch (distribution.strategy.rate) {
                    case 'mild':
                        //每用户总共弹出 1 次，无论是否填写
                        if (Number(getStorageItem(feedback.id, 'c')) >= 1) {
                            return;
                        }
                        break;
                    case 'moderate':
                        //每用户总共弹出 7 次，每次间隔 3 天
                        const count = Number(getStorageItem(feedback.id, 'c'));
                        const threeDaysInMs = 3 * 24 * 60 * 60 * 1000;

                        if (count >= 7 || Date.now() - Number(getStorageItem(feedback.id, 't')) < threeDaysInMs) {
                            return;
                        }
                        break;
                    case 'radical':
                        //每用户总共弹出次数不限，每次间隔 1 天
                        if (Date.now() - Number(getStorageItem(feedback.id, 't')) < 24 * 60 * 60 * 1000) {
                            return;
                        }
                        break;
                }
            }

            const open = () => {
                //记录上次弹出时间
                setStorageItem(feedback.id, 't', String(Date.now()));
                //记录弹出次数
                setStorageItem(feedback.id, 'c', String(Number(getStorageItem(feedback.id, 'c')) + 1));

                dispatch(show('feedback'));
            };

            if (distribution.timing.handle == 'delay') {
                setTimeout(open, distribution.timing.delay * 1000);
            } else {
                open();
            }
        }
    }, []);

    if (!visible) {
        return null;
    }
    return <Window feedback={feedback} />;
}

const Complete = styled.div`
  padding-left: 8px;
  padding-right: 8px;
  text-align: center;
  font-size: 14px;
  line-height: 22px;
  color: #333;
  transition: all 0.2s ease-in;

  img {
    margin: auto;
    height: 91px;
    width: 160px;
  }

  p {
    margin-top: 0px;
    margin-bottom: 0px;
    width: 100%;
    word-break: break-all;
    text-align: center;
    font-size: 14px;
    line-height: 24px;
    color: #333;
  }
`;

const PoweredBy = styled.div`
  padding-top: 16px;
  text-align: center;

  a {
    border-radius: .25rem;
    color: #C4C8CC;
    display: inline-block;
    font-size: 12px;
    line-height: 18px;
    padding-left: 4px;
    padding-right: 4px;
    text-align: center;
    text-decoration: none;
    transition: background 0.3s cubic-bezier(0.5, 1, 0.89, 1);

    &:hover {
      background-color: #F1F3F5;
    }
  }
`;

const Button = styled.button`
  background: var(--tas-window-tone);
  border-color: var(--tas-window-tone);
  color: #FFFFFF;
  border-radius: 20px;
  border-style: solid;
  border-width: 1px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 600;
  line-height: 20px;
  max-width: 100%;
  outline: 2px solid transparent;
  outline-offset: 2px;
  overflow: hidden;
  padding: 9px 16px;
  text-align: center;
  text-overflow: ellipsis;
  transition-duration: .3s;
  white-space: nowrap;
  width: 100%;

  &:hover {
    opacity: 0.9;
  }
`;

const Textarea = styled.div<{ $focus: boolean }>`
  margin-bottom: 16px;
  overflow: hidden;
  background-color: white;
  border: 1px solid #E0E3E5;
  border-radius: 4px;
  height: 120px;
  display: flex;
  flex-direction: column;

  ${({ $focus }) => $focus && css`
    border-color: var(--tas-window-tone);
  `};

  textarea {
    height: auto;
    background-color: #FFFFFF;
    resize: none;
    color: #333;
    outline: 0;
    border-radius: 6px;
    margin: 0 2px;
    width: calc(100% - 4px);
    display: block;
    padding: 8px;
    font-size: 14px;
    line-height: 20px;
    border: 0;
    flex-grow: 1;
    overflow: auto;
    box-sizing: border-box;
  }
`;

const slideIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0px);
  }
`;

const Reason = styled.div`
  margin-top: 38px;
  animation: ${slideIn} ease 0.3s;
`;

const Text = styled.div`
  position: absolute;
  bottom: -22px;
  width: 100%;
  white-space: nowrap;
  font-size: 12px;
  color: #4d4f52;
  justify-content: center;
  display: flex;
`;

const Title = styled.div`
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
`;

const Container = styled.div`
  position: fixed;
  z-index: 4001;
  overflow: hidden;
  right: var(--tas-window-side-margin, 80px);
  bottom: var(--tas-window-bottom-margin, 48px);
  border-radius: 5px;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);
  padding: 24px 24px 12px;
  background: #fff;
  width: 320px;
  box-sizing: border-box;
  transition: height 0.3s;

  .rc-rate {
    padding: 0;
    list-style: none;
    display: flex;
    justify-content: space-between;
    margin: 16px -4px;
  }

  .rc-rate-rtl {
    direction: rtl;
  }

  .rc-rate-disabled .rc-rate-star {
    cursor: default;
  }

  .rc-rate-disabled .rc-rate-star:before,
  .rc-rate-disabled .rc-rate-star-content:before {
    cursor: default;
  }

  .rc-rate-disabled .rc-rate-star:hover {
    transform: scale(1);
  }

  .rc-rate-star {
    margin: 0;
    padding: 0;
    color: #e9e9e9;
    position: relative;
    display: flex;
    flex: 1 1 0%;
    justify-content: center;
    text-align: center;

    [role="radio"] {
      position: relative;
      margin-left: 4px;
      margin-right: 4px;
      display: flex;
      height: 40px;
      width: 100%;
      cursor: pointer;
      justify-content: center;
      border: 1px solid #E0E3E5;
      border-radius: 6px;
      transition-duration: 160ms;
      animation-duration: 800ms;

      svg {
        width: 24px;
      }
    }
  }

  .rc-rate-star-full {
    [role="radio"] {
      background-color: #FFEED6;
      border-color: #FFEED6;
    }
  }

  .rc-rate-rtl .rc-rate-star {
    margin-right: 0;
    margin-left: 8px;
    float: right;
  }

  .rc-rate-star-first,
  .rc-rate-star-second {
    display: flex;
    align-items: center;
    position: relative;
    color: #CED4DAFF;
  }

  .rc-rate-star-first {
    position: absolute;
    left: 0;
    top: 0;
    width: 50%;
    height: 100%;
    overflow: hidden;
    opacity: 0;
  }

  .rc-rate-rtl .rc-rate-star-first {
    right: 0;
    left: auto;
  }

  .rc-rate-star-half .rc-rate-star-first,
  .rc-rate-star-half .rc-rate-star-second {
    opacity: 1;
  }

  .rc-rate-star-half .rc-rate-star-first,
  .rc-rate-star-full .rc-rate-star-second {
    color: #E6A447FF;
  }

`;
