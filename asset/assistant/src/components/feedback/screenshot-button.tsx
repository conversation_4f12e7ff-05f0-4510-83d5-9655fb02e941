import styled, { css } from 'styled-components';
import { ReactComponent as ScreenShotIcon } from '../../images/screenshot.svg';
import ScreenShot from 'js-web-screen-shot';
import '../../scss/style.scss';

interface Props {
    active: boolean;
    onComplete: (screenshot: string) => void;
}

export default function ScreenshotButton({ active, onComplete }: Props) {
    return <Container
        $active={active}
        onClick={(e) => {
            e.preventDefault();
            new ScreenShot({
                enableWebRtc: false,
                level: 9999,
                hiddenToolIco: {
                    save: true
                },
                completeCallback({ base64 }: any) {
                    onComplete(base64);
                }
            });
        }}
    >
        <ScreenShotIcon />{active ? '已截屏' : '截屏'}
    </Container>;
}

const Container = styled.a<{ $active: boolean }>`
  align-items: center;
  color: #868e96;
  cursor: pointer;
  display: flex;
  font-size: 14px;
  gap: 1px;
  line-height: 20px;
  margin-bottom: 8px;
  margin-left: 8px;
  width: fit-content;
  ${({ $active }) => $active && css`
    color: var(--tas-window-tone);
  `};
`;
