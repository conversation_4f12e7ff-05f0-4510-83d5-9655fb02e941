import styled, { css } from 'styled-components';
import { useCallback, useEffect, useState } from 'react';
import { ReactComponent as ResizeIcon } from '../images/resize.svg';
import { ReactComponent as QuitResizeIcon } from '../images/quit-resize.svg';
import { ReactComponent as MaximumIcon } from '../images/maximum.svg';
import { ReactComponent as QuitMaximumIcon } from '../images/quit-maximum.svg';
import { ReactComponent as CloseIcon } from '../images/close.svg';
import { ReactComponent as LinkIcon } from '../images/link.svg';
import Draggable, { DraggableEventHandler } from 'react-draggable';
import { useWindowSize } from '@react-hook/window-size';
import { setMode, setUrl } from '../slice/doc';
import { useAppDispatch, useAppSelector } from '../hooks';
import { show } from '../slice/window';

interface Props {
    base: string;
}

export default function Document({ base }: Props) {

    const [visible, mode, url, appearance] = useAppSelector(({
        window,
        doc,
        context: { config }
    }) => [window.name === 'doc', doc.mode, doc.url, config.appearance]);

    const dispatch = useAppDispatch();

    const [current, setCurrent] = useState(base);
    const [bootstrapped, setBootstrapped] = useState(false);

    const [winWidth, winHeight] = useWindowSize();

    const getPosition = useCallback((winWidth: number, winHeight: number) => {
        return {
            x: winWidth - 420 - appearance.window.sideMargin,
            y: winHeight - 630 - appearance.window.bottomMargin
        };
    }, [appearance]);

    const [position, setPosition] = useState(() => {
        return getPosition(winWidth, winHeight);
    });

    useEffect(() => {
        setPosition(getPosition(winWidth, winHeight));
    }, [winWidth, winHeight]);

    const toggleCenter = useCallback(() => {
        dispatch(setMode(mode !== 'center' ? 'center' : 'normal'));
    }, [mode]);

    const toggleMaximum = useCallback(() => {
        dispatch(setMode(mode !== 'maximum' ? 'maximum' : 'normal'));
    }, [mode]);

    const onStop = useCallback<DraggableEventHandler>((_, data) => {
        setPosition({ x: data.x, y: data.y });
    }, []);

    const [iframe, setIframe] = useState<HTMLIFrameElement | null>(null);

    useEffect(() => {
        if (iframe && iframe.contentWindow && url && bootstrapped) {
            iframe.contentWindow.postMessage(['history', 'replace', url || base], '*');
            dispatch(setUrl(''));
        }
    }, [url, bootstrapped]);

    useEffect(() => {
        if (iframe && iframe.contentWindow) {
            const listener = (event: MessageEvent) => {
                try {
                    const [type, action, ...args] = event.data;
                    switch (type) {
                        case 'bootstrapped':
                            setBootstrapped(true);
                            break;
                        case 'history':
                            switch (action) {
                                case 'replace':
                                case 'push':
                                    setCurrent(args[0]);
                                    break;
                            }
                            break;
                    }
                } catch {

                }
            };

            window.addEventListener('message', listener);

            return () => {
                window.removeEventListener('message', listener);
            };
        }
    }, [iframe]);

    if (!visible && !iframe) {
        return null;
    }

    return <Root $visible={visible}>
        <Draggable
            handle='.handle'
            disabled={mode !== 'normal'}
            position={mode === 'normal' ? position : { x: 0, y: 0 }}
            onStop={onStop}
        >
            <Container $mode={mode}>
                <Header className='handle'>
                    <Title>文档中心</Title>
                    <Actions>
                        <Action onClick={toggleCenter}>
                            {mode === 'center' ? <QuitResizeIcon /> : <ResizeIcon />}
                        </Action>
                        <Action onClick={toggleMaximum}>
                            {mode === 'maximum' ? <QuitMaximumIcon /> : <MaximumIcon />}
                        </Action>
                        <Action onClick={() => dispatch(show(undefined))}><CloseIcon /></Action>
                    </Actions>
                </Header>
                <iframe ref={setIframe} src={base} />
                <Footer><a href={current} target='_blank'>文档中心打开<LinkIcon /></a></Footer>
            </Container>
        </Draggable>
    </Root>;
}

const Footer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0px 16px;
  height: 40px;
  line-height: 40px;
  box-shadow: 0 -1px 2px 0 rgba(0, 0, 0, 0.16);
  background: #ffffff;

  a {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 13px;
    color: #333;
    text-decoration: none;

    &:hover {
      color: var(--tas-window-tone, #3c60ff);
      text-decoration: none;
    }
  }
`;

const Action = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;

  svg {
    fill: #ffffff;
  }
`;

const Actions = styled.div`
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 12px;
`;

const Title = styled.div`
  flex: 1 1 auto;
  line-height: 32px;
  color: #fff;
  font-size: 16px;
`;

const Root = styled.div<{ $visible: boolean; }>`
  position: fixed;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  z-index: 5000;

  ${props => !props.$visible && css`
    display: none;
  `}
  * {
    box-sizing: border-box;
  }
`;

const Header = styled.div`
  height: 60px;
  background: var(--tas-window-tone, #3c60ff);
  cursor: move;
  display: flex;
  padding: 14px;
  align-items: center;
`;

const Container = styled.div<{ $mode: string; }>`
  position: absolute;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);
  display: flex;
  flex-direction: column;
  width: 420px;
  height: 630px;
  color: #333;
  font-size: 14px;

  ${props => props.$mode === 'center' && css`
    top: 60px;
    bottom: 60px;
    left: 120px;
    right: 120px;
    position: fixed;
    width: auto;
    height: auto;
    z-index: 5000;

    ${Header} {
      cursor: auto;
    }
  `}
  ${props => props.$mode === 'maximum' && css`
    top: 0;
    bottom: 0;
    left: auto;
    right: 0;
    position: fixed;
    width: 530px;
    height: auto;
    z-index: 5000;
    border-radius: 0;

    ${Header} {
      cursor: auto;
    }
  `}
  iframe {
    flex: 1;
    background: #FFF;
    border: none;
  }
`;
