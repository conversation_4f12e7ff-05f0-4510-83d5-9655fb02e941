import styled from 'styled-components';
import { ReactComponent as CloseIcon } from '../images/close.svg';

const CloseButton = styled.div.attrs({
    children: <CloseIcon />,
})`
  position: absolute;
  top: 4px;
  right: 4px;
  align-items: center;
  border-radius: 9999px;
  color: #C4C8CC;
  cursor: pointer;
  display: flex;
  height: 24px;
  justify-content: center;
  transition: background 0.3s cubic-bezier(0.5, 1, 0.89, 1);
  width: 24px;
  z-index: 20;

  &:hover {
    background-color: #e9ecef;
  }
`;

export default CloseButton;
