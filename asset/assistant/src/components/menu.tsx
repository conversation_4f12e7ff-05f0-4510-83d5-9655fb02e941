import styled from 'styled-components';
import { ComponentProps, ReactNode } from 'react';
import { ReactComponent as GotopIcon } from '../icons/gotop.svg';
import useWindowScroll from '@react-hook/window-scroll';
import ChatItem from './chat-item';
import DocItem from './doc-item';
import { useAppSelector } from '../hooks';
import CustomItem from './custom-item';
import Tooltip from 'rc-tooltip';
import useWindowSize from '../utils/use-window-size';
import FeedbackItem from './feedback-item';

type TooltipProps = ComponentProps<typeof Tooltip>;

export interface MenuItemProps {
    children: ReactNode;
    tooltip?: TooltipProps['overlay'] | TooltipProps;
    onClick?: () => void;
}

export const MenuItem = function({ children, tooltip, onClick }: MenuItemProps) {
    const item = <Item onClick={onClick}>{children}</Item>;

    if (tooltip) {
        const defaultProps = {
            placement: 'left',
            getTooltipContainer: (dom: HTMLElement) => {
                return dom.parentElement || document.body;
            }
        };

        let props: TooltipProps;
        if (typeof tooltip === 'object' && tooltip.hasOwnProperty('overlay')) {
            props = tooltip as TooltipProps;
        } else {
            props = {
                overlay: tooltip as TooltipProps['overlay']
            };
        }

        return <Tooltip
            {...defaultProps}
            {...props}
        >
            {item}
        </Tooltip>;
    }
    return item;
};

const GotopItem = function() {
    const scrollTop = useWindowScroll();
    if (scrollTop > 500) {
        return <MenuItem tooltip='回到顶部' onClick={() => window.scrollTo({
            top: 0,
            behavior: 'smooth'
        })}><GotopIcon /></MenuItem>;
    }
    return null;
};

export default function Menu() {
    const {
        robot,
        chat,
        doc,
        gotop,
        customs,
        feedback
    } = useAppSelector(({ context: { config } }) => config);

    //手机上只显示回到顶部按钮
    const size = useWindowSize();

    return <Container>
        {gotop.enable && <GotopItem />}
        {size === 'desktop' && doc.enable && <DocItem />}
        {size === 'desktop' && (chat.enable || robot.enable) && <ChatItem />}
        {size === 'desktop' && feedback && <FeedbackItem />}
        {size === 'desktop' && customs.map((item, index) => <CustomItem index={index} key={index} item={item} />)}
    </Container>;
}

const Item = styled.div`
    width: var(--tas-button-size, 40px);
    height: var(--tas-button-size, 40px);
    background: var(--tas-button-background, #FFFFFF);
    box-shadow: 0 1px 4px -2px rgb(0 0 0 / 13%), 0 2px 8px rgb(0 0 0 / 8%), 0 8px 16px 4px rgb(0 0 0 / 4%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    margin-top: 10px;
    color: var(--tas-button-color, #585a5a);

    svg {
        width: 65%;
        height: 65%;
        fill: currentColor;
    }
`;

const Container = styled.div`
    position: fixed;
    right: var(--tas-button-side-margin, 24px);
    bottom: var(--tas-button-bottom-margin, 48px);
    z-index: 4000;
    display: flex;
    flex-direction: column;

    .rc-tooltip.rc-tooltip-zoom-appear,
    .rc-tooltip.rc-tooltip-zoom-enter {
        opacity: 0;
    }

    .rc-tooltip.rc-tooltip-zoom-enter,
    .rc-tooltip.rc-tooltip-zoom-leave {
        display: block;
    }

    .rc-tooltip-zoom-enter,
    .rc-tooltip-zoom-appear {
        opacity: 0;
        animation-duration: 0.3s;
        animation-fill-mode: both;
        animation-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1.28);
        animation-play-state: paused;
    }

    .rc-tooltip-zoom-leave {
        animation-duration: 0.3s;
        animation-fill-mode: both;
        animation-timing-function: cubic-bezier(0.6, -0.3, 0.74, 0.05);
        animation-play-state: paused;
    }

    .rc-tooltip-zoom-enter.rc-tooltip-zoom-enter-active,
    .rc-tooltip-zoom-appear.rc-tooltip-zoom-appear-active {
        animation-name: rcToolTipZoomIn;
        animation-play-state: running;
    }

    .rc-tooltip-zoom-leave.rc-tooltip-zoom-leave-active {
        animation-name: rcToolTipZoomOut;
        animation-play-state: running;
    }

    @keyframes rcToolTipZoomIn {
        0% {
            opacity: 0;
            transform-origin: 50% 50%;
            transform: scale(0, 0);
        }
        100% {
            opacity: 1;
            transform-origin: 50% 50%;
            transform: scale(1, 1);
        }
    }
    @keyframes rcToolTipZoomOut {
        0% {
            opacity: 1;
            transform-origin: 50% 50%;
            transform: scale(1, 1);
        }
        100% {
            opacity: 0;
            transform-origin: 50% 50%;
            transform: scale(0, 0);
        }
    }

    .rc-tooltip {
        position: absolute;
        z-index: 1070;
        display: block;
        visibility: visible;
        font-size: 12px;
        line-height: 1.5;
        opacity: 0.9;
    }

    .rc-tooltip-hidden {
        display: none;
    }

    .rc-tooltip-placement-top,
    .rc-tooltip-placement-topLeft,
    .rc-tooltip-placement-topRight {
        padding: 5px 0 9px 0;
    }

    .rc-tooltip-placement-right,
    .rc-tooltip-placement-rightTop,
    .rc-tooltip-placement-rightBottom {
        padding: 0 5px 0 9px;
    }

    .rc-tooltip-placement-bottom,
    .rc-tooltip-placement-bottomLeft,
    .rc-tooltip-placement-bottomRight {
        padding: 9px 0 5px 0;
    }

    .rc-tooltip-placement-left,
    .rc-tooltip-placement-leftTop,
    .rc-tooltip-placement-leftBottom {
        padding: 0 9px 0 5px;
    }

    .rc-tooltip-inner {
        padding: 8px 10px;
        color: #fff;
        text-align: left;
        text-decoration: none;
        background-color: #373737;
        border-radius: 6px;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.17);
    }

    .rc-tooltip-arrow {
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
    }

    .rc-tooltip-placement-top .rc-tooltip-arrow,
    .rc-tooltip-placement-topLeft .rc-tooltip-arrow,
    .rc-tooltip-placement-topRight .rc-tooltip-arrow {
        bottom: 4px;
        margin-left: -5px;
        border-width: 5px 5px 0;
        border-top-color: #373737;
    }

    .rc-tooltip-placement-top .rc-tooltip-arrow {
        left: 50%;
    }

    .rc-tooltip-placement-topLeft .rc-tooltip-arrow {
        left: 15%;
    }

    .rc-tooltip-placement-topRight .rc-tooltip-arrow {
        right: 15%;
    }

    .rc-tooltip-placement-right .rc-tooltip-arrow,
    .rc-tooltip-placement-rightTop .rc-tooltip-arrow,
    .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
        left: 4px;
        margin-top: -5px;
        border-width: 5px 5px 5px 0;
        border-right-color: #373737;
    }

    .rc-tooltip-placement-right .rc-tooltip-arrow {
        top: 50%;
    }

    .rc-tooltip-placement-rightTop .rc-tooltip-arrow {
        top: 15%;
        margin-top: 0;
    }

    .rc-tooltip-placement-rightBottom .rc-tooltip-arrow {
        bottom: 15%;
    }

    .rc-tooltip-placement-left .rc-tooltip-arrow,
    .rc-tooltip-placement-leftTop .rc-tooltip-arrow,
    .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
        right: 4px;
        margin-top: -5px;
        border-width: 5px 0 5px 5px;
        border-left-color: #373737;
    }

    .rc-tooltip-placement-left .rc-tooltip-arrow {
        top: 50%;
    }

    .rc-tooltip-placement-leftTop .rc-tooltip-arrow {
        top: 15%;
        margin-top: 0;
    }

    .rc-tooltip-placement-leftBottom .rc-tooltip-arrow {
        bottom: 15%;
    }

    .rc-tooltip-placement-bottom .rc-tooltip-arrow,
    .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow,
    .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
        top: 4px;
        margin-left: -5px;
        border-width: 0 5px 5px;
        border-bottom-color: #373737;
    }

    .rc-tooltip-placement-bottom .rc-tooltip-arrow {
        left: 50%;
    }

    .rc-tooltip-placement-bottomLeft .rc-tooltip-arrow {
        left: 15%;
    }

    .rc-tooltip-placement-bottomRight .rc-tooltip-arrow {
        right: 15%;
    }

`;
