import styled from 'styled-components';
import useLocalState from '../utils/use-local-state';
import useWindowSize from '../utils/use-window-size';
import CloseButton from './close-button';

interface Props {
    broadcast: Broadcast;
}

export default function Broadcast({ broadcast }: Props) {
    const { image, url, expire_time, position, height } = broadcast;
    const [show, setShow] = useLocalState(`assistant-broadcast-${image}`, true);
    //手机上只显示回到顶部按钮
    const size = useWindowSize();

    if (expire_time) {
        const expireDate = Date.parse(expire_time);
        if (expireDate < Date.now()) {
            return null;
        }
    }

    if (!show || !image || size === 'mobile') {
        return null;
    }

    const children = <>
        <CloseButton onClick={() => setShow(false)} />
        {url ? <a href={url} onClick={() => setShow(false)} target='_blank'><img src={image} /></a> :
            <img src={image} />}
    </>;

    switch (position) {
        case 'bottom':
            return <BottomContainer $height={height}>{children}</BottomContainer>;
        default:
            return <BottomRightContainer>{children}</BottomRightContainer>;
    }

}

const Container = styled.div`
  position: fixed;
  z-index: 3000;
  padding: 0;
  overflow: hidden;

  a {
    display: flex;
  }

  ${CloseButton} {
    display: none;
  }

  &:hover {
    ${CloseButton} {
      display: flex;
    }
  }
`;

const BottomContainer = styled(Container)<{ $height: number }>`
  right: 0;
  left: 0;
  bottom: 0;
  height: ${props => props.$height || 100}px;
  z-index: 4000;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const BottomRightContainer = styled(Container)`
  right: var(--tas-window-side-margin, 80px);
  bottom: var(--tas-window-bottom-margin, 48px);
  border-radius: 5px;
  box-shadow: 0 5px 40px rgb(0 0 0/16%);

  img {
    max-width: 450px;
    max-height: 450px;
  }
`;
