import { MenuItem } from './menu';
import { ReactComponent as ChatIcon } from '../icons/doc.svg';
import { useDispatch } from 'react-redux';
import { show } from '../slice/window';
import { useAppSelector } from '../hooks';

export default function DocItem() {

    const [visible] = useAppSelector(state => [state.window.name === 'doc']);
    const dispatch = useDispatch();

    return <MenuItem tooltip={'文档中心'} onClick={() => dispatch(show(visible ? undefined : 'doc'))}><ChatIcon /></MenuItem>;
};
