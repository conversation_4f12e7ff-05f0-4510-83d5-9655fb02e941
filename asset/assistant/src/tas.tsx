import Tracker from './tracker';
import type View from './view';
import defaultsDeep from 'lodash/defaultsDeep';

export default class Tas {

    readonly #host: string;

    #view?: View;
    #tracker?: Tracker;

    initialized = false;

    baseUrl?: string;
    sessionId?: string;
    config?: Config;

    constructor(script: HTMLScriptElement) {

        const attr = script.getAttribute.bind(script);

        this.#host = new URL(script.src).origin;

        const id = attr('data-id');

        if (id) {
            this.init(id);
        }
    }

    async #getClientId() {
        let clientId = localStorage.getItem('_tas_cid');
        if (!clientId) {
            const { default: FingerprintJS } = await import('@fingerprintjs/fingerprintjs');
            const fp = await FingerprintJS.load();

            const result = await fp.get();

            clientId = result.visitorId;
            localStorage.setItem('_tas_cid', clientId);
        }

        return clientId;
    }

    async #getConfig(id: string, defaultConfig?: Partial<Config>): Promise<boolean> {
        try {

            this.baseUrl = `${this.#host}/assistant/${id}`;

            const clientId = await this.#getClientId();

            const params = new URLSearchParams({
                client_id: clientId
            });

            const response = await fetch(`${this.baseUrl}?${params.toString()}`, {
                headers: { Accept: 'application/json' }
            });

            if (response.ok) {
                const { sessionId, config } = await response.json();

                this.config = defaultsDeep(defaultConfig, config);
                this.sessionId = sessionId;

                return true;
            }
        } catch {

        }
        return false;
    }

    async init(id: string, defaultConfig?: Partial<Config>) {
        if (!this.initialized) {
            this.initialized = true;

            if (await this.#getConfig(id, defaultConfig)) {
                const config = this.getConfig();

                const context: Context = {
                    baseUrl: this.getBaseUrl(),
                    sessionId: this.getSessionId(),
                    config: config
                };

                //加载统计器
                this.#tracker = new Tracker(context);

                //加载窗口
                import('./view').then(({ default: View }) => {
                    this.#view = new View(context);
                });

                //加载自定义脚本
                const { scripts } = config;

                if (scripts) {
                    const fragment = document.createRange().createContextualFragment(scripts);
                    document.body.appendChild(fragment);
                }

                window.dispatchEvent(new CustomEvent('tas.init'));
            }
        }
    }

    isReady() {
        return !!this.config;
    }

    getBaseUrl() {
        if (!this.baseUrl) {
            throw new Error('tas not ready');
        }
        return this.baseUrl;
    }

    getConfig() {
        if (!this.config) {
            throw new Error('tas not ready');
        }
        return this.config;
    }

    getSessionId() {
        if (!this.sessionId) {
            throw new Error('tas not ready');
        }
        return this.sessionId;
    }

    public trackEvent(eventName: string, eventData?: Record<string, string>) {
        if (this.#tracker) {
            this.#tracker.trackEvent(eventName, eventData);
        }
    }

    public openDoc(url?: string, mode?: string) {
        if (this.#view) {
            this.#view.openDoc(url, mode);
        }
    }

    public closeDoc() {
        if (this.#view) {
            this.#view.closeDoc();
        }
    }

    public toggleDoc() {
        if (this.#view) {
            this.#view.toggleDoc();
        }
    }

    public toggleChat() {
        if (this.#view) {
            this.#view.toggleChat();
        }
    }

    public toggleFeedback() {
        if (this.#view) {
            this.#view.toggleFeedback();
        }
    }

    public toggleCustom(index: number) {
        if (this.#view) {
            this.#view.toggleCustom(index);
        }
    }

    public getIcons() {
        return import('./icons').then(({ default: icons }) => {
            return icons;
        });
    }

}
