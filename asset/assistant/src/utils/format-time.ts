export default function formatTime(time: number) {
    const date = new Date(time);
    const today = new Date();

    const showDay = date.getFullYear() !== today.getFullYear() || date.getMonth() !== today.getMonth() || date.getDate() !== today.getDate();

    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const str = [`${hours}:${minutes}`];

    if (showDay) {
        str.unshift(`${month}-${day}`);
    }

    return str.join(' ');
}
