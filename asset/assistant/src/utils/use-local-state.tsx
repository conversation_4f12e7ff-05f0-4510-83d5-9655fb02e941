import { Dispatch, SetStateAction, useCallback, useRef, useState } from 'react';

const isCallable = (value: unknown): value is Function => typeof value === 'function';
export default function useLocalState<S>(key: string, defaultValue: S | (() => S)): [S, Dispatch<SetStateAction<S>>] {

    const [value, setValue] = useState<S>(() => {

        const toStore = isCallable(defaultValue) ? defaultValue() : defaultValue;

        const item = window.localStorage.getItem(key);
        try {
            return item ? JSON.parse(item) : toStore;
        } catch (error) {
            return toStore;
        }
    });

    const lastValue = useRef(value);
    lastValue.current = value;

    const setLocalStateValue = useCallback(
        (newValue: SetStateAction<S>) => {
            const toStore = isCallable(newValue) ? newValue(lastValue.current) : newValue;
            window.localStorage.setItem(key, JSON.stringify(toStore));
            setValue(toStore);
        },
        [key]
    );

    return [value, setLocalStateValue];
}
