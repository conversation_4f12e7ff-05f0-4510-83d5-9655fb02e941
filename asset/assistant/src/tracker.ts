const hook = (obj: History, method: keyof History, callback: Function) => {
    const orig = obj[method];

    return (...args: any[]) => {
        callback.apply(null, args);

        return orig.apply(obj, args);
    };
};

export default class Tracker {
    protected document: Document;
    protected location: Location;

    protected baseUrl: string;
    protected sessionId: string;

    protected initialized: boolean = false;

    protected screen: string;
    protected language: string;
    protected title: string | null;
    protected currentUrl: string;
    protected currentRef: string;

    protected cache?: string;

    constructor({ sessionId, baseUrl }: Context) {

        this.baseUrl = baseUrl;
        this.sessionId = sessionId;

        const {
            screen: { width, height },
            navigator: { language },
            location,
            document,
            history,
        } = window;

        this.document = document;
        this.location = location;

        this.screen = `${width}x${height}`;
        this.language = language;
        this.currentUrl = location.href;
        this.currentRef = document.referrer;
        this.title = document.title;

        history.pushState = hook(history, 'pushState', this.handlePush);
        history.replaceState = hook(history, 'replaceState', this.handlePush);

        this.handleClick();
        this.observeTitle();

        document.addEventListener('readystatechange', this.init, true);

        this.init();
    }

    init = () => {
        if (document.readyState === 'complete' && !this.initialized) {
            this.initialized = true;
            this.track();
        }
    };

    handlePush = (_: any, __: string, url?: string | URL | null) => {
        if (!url) return;

        this.currentRef = this.currentUrl;
        this.currentUrl = new URL(url.toString(), this.currentUrl).toString();

        if (this.currentUrl !== this.currentRef) {
            setTimeout(this.track, 300);
        }
    };

    handleClick = () => {
        const trackElement = (el: HTMLElement) => {
            const attr = el.getAttribute.bind(el);
            const eventName = attr('data-tas-event');

            if (eventName) {
                const eventData: Record<string, any> = {};

                el.getAttributeNames().forEach(name => {
                    const match = name.match(/data-tas-event-([\w-_]+)/);

                    if (match) {
                        eventData[match[1]] = attr(name);
                    }
                });

                return this.track(eventName, { data: eventData });
            }
            return Promise.resolve();
        };

        document.addEventListener('click', (e) => {
            const findATagParent = (rootElem: HTMLElement, maxSearchDepth: number) => {
                let currentElement: HTMLElement | null = rootElem;
                for (let i = 0; i < maxSearchDepth; i++) {
                    if (currentElement.tagName === 'A') {
                        return currentElement;
                    }
                    currentElement = currentElement.parentElement;
                    if (!currentElement) {
                        return null;
                    }
                }
                return null;
            };

            const el = e.target as HTMLElement;

            const anchor = (el.tagName === 'A' ? el : findATagParent(el, 10)) as HTMLAnchorElement;

            if (anchor) {
                const { href, target } = anchor;
                const external =
                    target === '_blank' ||
                    e.ctrlKey ||
                    e.shiftKey ||
                    e.metaKey ||
                    (e.button && e.button === 1);
                const eventName = anchor.getAttribute('data-tas-event');

                if (eventName && href) {
                    if (!external) {
                        e.preventDefault();
                    }
                    return trackElement(anchor).then(() => {
                        if (!external) this.location.href = href;
                    });
                }
            } else {
                trackElement(el);
            }

        }, true);
    };

    observeTitle = () => {
        const observer = new MutationObserver(([entry]) => {
            this.title = entry.target.textContent;
        });

        const node = document.querySelector('head > title');

        if (node) {
            observer.observe(node, {
                subtree: true,
                characterData: true,
                childList: true,
            });
        }
    };

    getPayload = () => ({
        sessionId: this.sessionId,
        screen: this.screen,
        language: this.language,
        title: this.title,
        url: this.currentUrl,
        referrer: this.currentRef,
    });

    send = (payload: object) => {

        const body = new FormData();
        body.append('type', 'event');
        body.append('payload', JSON.stringify(payload));

        if (this.cache) {
            body.append('cache', this.cache);
        }

        return fetch(`${this.baseUrl}/send`, {
            method: 'POST',
            body
        }).then(res => res.text()).then((text) => {
            this.cache = text;
        });
    };

    track = (obj?: any, data?: any) => {
        if (typeof obj === 'string') {
            return this.send({
                ...this.getPayload(),
                name: obj,
                data: typeof data === 'object' ? data : undefined,
            });
        } else if (typeof obj === 'object') {
            return this.send(obj);
        } else if (typeof obj === 'function') {
            return this.send(obj(this.getPayload()));
        }
        return this.send(this.getPayload());
    };

    trackEvent = (eventName: string, eventData?: Record<string, string>) => {
        return this.track(eventName, { data: eventData });
    };
}
