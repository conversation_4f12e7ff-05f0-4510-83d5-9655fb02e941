module '*.svg';
module '*.xlsx';
module '*.png';

interface Window {
    tnf: {
        init: Function;
    };
}

interface StatsDataItem {
    date: string,
    value: number
}

interface Manifest {
    scripts: string;
}

interface Application {
    id: number;
    name: string;
    client_id: string;
    client_secret: string;
    redirect_uri: string;
    logout_uri: string;
    is_confidential: boolean;
}

interface AssistantSite {
    id: number;
    name: string;
    url: string;
    appearance: object;
    features: object;
    customs: object;
    broadcast: object;
    robot: object;
    plan: string;
    expire_time: string;
    code: string;
    scripts: string;
    is_standard: boolean;
    pivot: {
        access_level: number;
    };
    status: number;
}

interface AssistantSiteFeedback {
    id: string;
    name: string;
    status: number;
    question: any;
    distribution: any;
}

interface AssistantSiteMetric {
    x: string;
    y: number;
}

interface AssistantWeaccount {
    id: number;
    plan: 'trial' | 'standard';
    expire_time: string;
    weaccount: {
        nickname: string;
        avatar: string;
        signature: string;
        robot: object;
    };
}

interface AssistantHecong {
    id: number;
    plan: 'trial' | 'standard';
    expire_time: string;
    seat: number;
}

interface SslBrand {
    id: number;
    name: string;
    description: string;
    dv?: {
        price: {
            one: string;
            wildcard: string;
        },
        product: string;
    };
    ov?: {
        price: {
            one: string;
            wildcard: string;
        },
        product: string;
    };
    auth_type: string;
}

interface SslOrder {
    id: string;
    brand: string;
    type: 'dv' | 'ov';
    status: number;
    one: number;
    wildcard: number;
    can_revoke: boolean;
    cert_id: string;
}

interface SslContact {
    id: string;
    first_name: string;
    last_name: string;
    telephone: string;
}

interface SslOrg {
    id: string;
    name: string;
}

interface Charge {
    id: number;
    is_paid: boolean;
    revoke_time: string | null;
    status: number;
    can_revoke: boolean;
    application: Application;
    amount: number;
    net_amount: number;
}

interface PayResult {
    pay_url: string;
    order_no: string;
}

interface WritePrivatization {
    id: number;
}

interface AccessToken {
    id: number;
    scopes: string[];
}

interface Api {
    id: number;
    title: string;
    logo: string;
    price_type: number;
    identify_type: number;
    trial_nums: number;
    url: string;
    applied: boolean;
    pivot: ApiUser;
    packages: ApiPackage[];
}

interface ApiUser {
    rate: [number, number, string];
    nums: number;
    warn_nums: number;
}

interface ApiPackage {
    id: number;
    nums: number;
    price: number;
    original_price: number;
}

interface ApiVip {
    plan: 'senior' | 'gold';
    expire_time: string;
}

interface ApiCategory {
    id: number;
    name: string;
}

interface ApiMcp {
    id: number;
    name: string;
    apis: Api[];
}

interface Sms {
    minute_limit: number;
    hour_limit: number;
    day_limit: number;
    warn_nums: number;
    webhook: string;
    batch_send: boolean;
}

interface SmsSign {
    id: number;
    name: string;
    source: string;
    company: object | null;
    proof: string | null;
    status: number;
    error: string;
}

interface SmsTask {
    id: number;
    total: number;
}

interface SmsTemplate {
    id: number;
    name: string;
    status: number;
    error: string;
    sign: SmsSign;
    content: string;
}

interface SmsLog {
    phone: string;
    status: number;
    send_id: string;
    content: string;
    message: string;
    send_time: string;
}

interface SmsPackage {
    nums: number;
    price: number;
}

type SmsPackages = Record<string, {
    name: string;
    packages: SmsPackage[];
}>;

interface UserTag {
    id: number;
    name: string;
}

interface UserCertification {
    passed: boolean;
    certify_url: string;
    failed_reason?: string;
    name: string;
    identity: string;
    update_time: string;
}

interface UserEnterprise {
    corporation: string;
    name: string;
    identity: string;
    img: string;
    status: number;
    update_time: string;
}

interface BasicStatistics {
    user: {
        total: number;
        yesterday: number;
        enterprise: number;
    };
    order: {
        total: number;
        amount: number;
    };
    charge: {
        total: number;
        amount: number;
    };
    approval: {
        enterprise: number;
        invoice: number;
        sms: {
            sign: number;
        };
        withdraw: {
            coin: number;
            money: number;
        };
    };
}

interface Ai {
    token: number;
    warn_token: number;
    packages: Record<string, AiPackage>;
}

interface AiPackage {
    name: string;
    amount: number;
    origin: number;
    token: number;
}

interface AiDataset {
    id: string;
    name: string;
    channel: string;
}

interface AiModel {
    id: string;
}

interface AiRobot {
    id: string;
    name: string;
    model_id: string;
    datasets: AiDataset[];
    model: AiModel;
    chat_url: string;
}

interface AiDatasetPart {
    id: string;
    payload: {
        title: string;
        content: string;
    };
}

interface AiPlugin {
    id: string;
    name: string;
    description: string;
    icon: string;
    auth: object;
    schema: string;
    api: string;
    config: any;
    tools: AiPluginTool[];
}

interface AiPluginTool {
    name: string;
    title: string;
    description: string;
    parameters: Record<string, {
        type: any
        title?: string
        description: string
        required?: boolean
        placeholder?: string
        encrypt?: boolean
        default?: string
        enum?: string[]
        enumNames?: string[]
        url?: string
        provider?: 'llm' | 'user'
    }> | null;
    fee: number;
}

interface Partner {
    fee: {
        token: number;
        wiki: number;
        bot: number;
        chat: number;
        ai: number;
    } | null;
}
