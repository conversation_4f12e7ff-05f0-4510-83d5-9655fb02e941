import { createRoot } from 'react-dom/client';
import './scss/app.scss';
import { createApplication, request, RequestButton } from '@topthink/common';
import routes from '@/routes';
import './utils/dayjs';
import { useEffect, useRef } from 'react';

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);

    const App = createApplication({
        baseURL: '/data',
        routes,
        async userResolver() {
            return await request('/current');
        },
        async onAuthorize(token) {
            const res = await request({
                method: 'POST',
                url: '/login/token',
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            return res.token;
        },
        onLogin({ onAuthorize }) {
            const ref = useRef<HTMLButtonElement>(null);

            useEffect(() => {
                ref.current?.click();
            }, []);

            return <RequestButton
                ref={ref}
                variant={'light'}
                className={'w-100'}
                url={'/login'}
                onSuccess={({ url }) => {
                    onAuthorize(url);
                }}
            >登录中……</RequestButton>;
        },
        async onLogout() {
            await request('/logout');
        }
    });

    root.render(<App />);
}
