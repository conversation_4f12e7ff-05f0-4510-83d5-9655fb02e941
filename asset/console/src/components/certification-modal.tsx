import InfoModal from '@/components/info-modal';
import { ComponentProps } from 'react';

interface Props extends Omit<ComponentProps<typeof InfoModal>, 'id' | 'renderChildren' | 'data'> {
    id: number;
}

export default function CertificationModal({ id, ...props }: Props) {
    return <InfoModal<UserCertification>
        header={'实名认证'}
        source={`/admin/user/${id}/certification`}
        renderChildren={({ data }) => {
            return <>
                <dl className='row'>
                    <dd className='col-2'>真实姓名</dd>
                    <dd className='col-10'>{data.name}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>身份证</dd>
                    <dd className='col-10'>{data.identity}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>认证时间</dd>
                    <dd className='col-10'>{data.update_time}</dd>
                </dl>
            </>;
        }}
        {...props}
    />;
}
