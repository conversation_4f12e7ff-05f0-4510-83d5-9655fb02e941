import { ImageZoom } from '@topthink/common';
import InfoModal from '@/components/info-modal';
import { ComponentProps } from 'react';


interface Props extends Omit<ComponentProps<typeof InfoModal>, 'id' | 'renderChildren' | 'data'> {
    id: number;
}

export default function EnterpriseModal({ id, ...props }: Props) {

    return <InfoModal<UserEnterprise>
        header={'企业认证'}
        source={`/admin/user/${id}/enterprise`}
        renderChildren={({ data }) => {
            return <>
                <dl className='row'>
                    <dd className='col-2'>公司名称</dd>
                    <dd className='col-10'>{data.name}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>统一社会信用代码</dd>
                    <dd className='col-10'>{data.identity}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>企业法人</dd>
                    <dd className='col-10'>{data.corporation}</dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>营业执照</dd>
                    <dd className='col-10'>
                        <ImageZoom>
                            <img src={data.img} height={20} />
                        </ImageZoom>
                    </dd>
                </dl>
                <dl className='row'>
                    <dd className='col-2'>认证时间</dd>
                    <dd className='col-10'>{data.update_time}</dd>
                </dl>
            </>;
        }}
        {...props}
    />;
}
