import { Access, LinkButton } from '@topthink/common';
import { PropsWithChildren } from 'react';
import PageResult from '@/components/page-result';

export default function CertificationAccess({ children }: PropsWithChildren<any>) {
    return <Access
        require={(user) => {
            return user.is_certified;
        }}
        fallback={<PageResult
            status={'error'}
            title={'系统检测到您的当前账号尚未进行实名认证，点击进行实名认证。'}
            extra={<LinkButton to={'/user/certification'}>进行实名认证</LinkButton>}
        />}>
        {children}
    </Access>;
}
