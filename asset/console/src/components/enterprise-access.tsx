import { Access, LinkButton } from '@topthink/common';
import { PropsWithChildren } from 'react';
import PageResult from '@/components/page-result';

export default function EnterpriseAccess({ children }: PropsWithChildren<any>) {
    return <Access
        require={(user) => {
            return user.is_enterprise;
        }}
        fallback={<PageResult
            status={'error'}
            title={'系统检测到您的当前账号尚未进行企业认证，点击进行企业认证。'}
            extra={<LinkButton to={'/user/enterprise'}>进行企业认证</LinkButton>}
        />}>
        {children}
    </Access>;
}
