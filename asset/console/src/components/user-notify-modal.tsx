import { ModalForm, ModalFormProps, Toast } from "@topthink/common";

interface Props {
    id: number;
    buttonProps?: ModalFormProps['buttonProps'];
    onSuccess?: ModalFormProps['onSuccess'];
}

export default function UserNotifyModal({ id, buttonProps }: Props) {
    return <ModalForm
        text={'发送通知'}
        buttonProps={buttonProps}
        action={`/admin/user/${id}/notify`}
        method={'post'}
        schema={{
            type: 'object',
            properties: {
                content: {
                    type: 'string',
                }
            }
        }}
        uiSchema={{
            content: {
                'ui:widget': 'textarea',
                'ui:autofocus': true,
                'ui:options': {
                    placeholder: '通知内容',
                    label: false,
                    rows: 5,
                    autoFocus: true
                }
            }
        }}
        onSuccess={() => {
            Toast.success('发送成功');
        }}
    />;
}