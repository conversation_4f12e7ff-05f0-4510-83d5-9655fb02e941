interface Props {
    status: number;
}

export default function SslOrderStatus({ status }: Props) {

    switch (status) {
        case -1:
            return <span className={'text-danger'}>已取消</span>;
        case 0:
            return <span className={'text-info'}>待申请</span>;
        case 1:
            return <span className={'text-warning'}>待验证</span>;
        case 2:
            return <span className={'text-primary'}>待签发</span>;
        case 3:
            return <span className={'text-primary'}>已签发</span>;
    }
    return null;
}
