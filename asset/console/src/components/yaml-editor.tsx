import AceEditor from 'react-ace';
import 'ace-builds/webpack-resolver';
import { styled } from '@topthink/common';

interface Props {
    value: string;
    onChange: (value: string) => void;
    className?: string;
}

export default function YamlEditor({ className, value, onChange }: Props) {
    return <StyledAceEditor
        className={className}
        mode='yaml'
        theme='tomorrow'
        fontSize={14}
        tabSize={2}
        height={''}
        width={''}
        editorProps={{ $blockScrolling: true }}
        value={value}
        onChange={onChange}
    />;
}

const StyledAceEditor = styled(AceEditor)`
    width: 100%;
    min-height: 500px;

    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);

    &.ace_focus {
        border-color: #9eb0ff;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(60, 96, 255, 0.25);
    }
`;
