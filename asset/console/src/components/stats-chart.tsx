import { Card, useRequest } from '@topthink/common';
import { Column, ColumnConfig, Line, LineConfig } from '@ant-design/plots';
import { formatLongNumber, formatPeriodDate } from '@/utils/format';

interface DataSource {
    data: any[];
}

interface UrlSource {
    url: string;
}

type Props = {
    period: string;
    card?: boolean;
    formatNumber?: boolean;
    series?: string | Record<string, {
        text: string;
        color: string;
    }>;
    type?: 'line' | 'column';
    isStack?: boolean;
} & (DataSource | UrlSource)

export default function StatsChart({
    series = {
        success: {
            text: '成功',
            color: '#5B8FF9'
        },
        fail: {
            text: '失败',
            color: '#F4664A'
        }
    },
    period,
    formatNumber = false,
    card = true,
    isStack = true,
    type = 'column',
    ...props
}: Props) {
    const single = typeof series === 'string';
    let source: any;
    if ('url' in props) {
        const { result } = useRequest({
            url: props.url,
            params: { period }
        }, { refreshDeps: [period] });

        if (!result) {
            return null;
        }
        source = result;
    } else {
        source = props.data;
    }

    const data = single ? source : Object.keys(series).flatMap((key) => {
        return source[key].map((item: any) => ({
            ...item,
            type: key
        }));
    });

    const config: ColumnConfig & LineConfig = {
        data,
        isStack,
        xField: 'x',
        yField: 'y',
        seriesField: single ? undefined : 'type',
        appendPadding: [30, 0, 20, 0],
        color: function(datum) {
            return typeof series === 'string' ? '#5B8FF9' : series[datum.type].color;
        },
        yAxis: {
            label: {
                formatter: (val) => {
                    return formatNumber ? formatLongNumber(val) : val;
                }
            }
        },
        smooth: true,
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                }
            },
        },
        tooltip: {
            formatter: (datum) => {
                const name = typeof series === 'string' ? series : series[datum.type].text;

                return {
                    name: name,
                    value: formatNumber ? formatLongNumber(datum.y) : datum.y,
                };
            }
        },
        legend: {
            position: 'bottom',
            itemName: {
                formatter(_, item) {
                    return typeof series === 'string' ? series : series[item.name].text;
                }
            },
            offsetY: 5
        }
    };

    const ChartComponent = type === 'column' ? Column : Line;

    if (!card) {
        return <ChartComponent {...config} />;
    }

    return <Card>
        <ChartComponent {...config} />
    </Card>;
}
