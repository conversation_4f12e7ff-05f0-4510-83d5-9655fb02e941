import useCountDown from 'react-countdown-hook';
import { RequestButton, RequestConfig } from '@topthink/common';
import { useCallback } from 'react';

const initialTime = 60 * 1000;
const interval = 1000;

interface Props {
    url: RequestConfig;
    method?: string;
}

export default function CodeButton(props: Props) {
    const [timeLeft, { start }] = useCountDown(initialTime, interval);

    const children = timeLeft > 0 ? `${timeLeft / 1000}秒后重发` : '发送验证码';

    const onSuccess = useCallback(() => {
        start();
    }, [start]);

    return <RequestButton {...props} onSuccess={onSuccess} disabled={timeLeft > 0} variant={'outline-secondary'}>
        {children}
    </RequestButton>;
}
