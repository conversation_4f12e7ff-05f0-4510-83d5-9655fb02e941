import { Tooltip } from '@topthink/common';

export default function SmsStatus({ status, message }: { status: number, message?: string }) {
    switch (status) {
        case 1:
            return <span className='text-success'>发送成功</span>;
        case -1:
            return <Tooltip tooltip={`失败回执：${message}`}>
                <span role='button' className='text-danger'>
                    发送失败
                    <i className='bi bi-question-circle ms-1' />
                </span>
            </Tooltip>;
        default:
            return <Tooltip tooltip={'正在等待网关发回状态；如长时间未返回状态，可能该号码暂时处于无信号、停机或欠费状态'}>
                <span role='button' className='text-info'>
                    提交成功
                    <i className='bi bi-question-circle ms-1' />
                </span>
            </Tooltip>;
    }
}
