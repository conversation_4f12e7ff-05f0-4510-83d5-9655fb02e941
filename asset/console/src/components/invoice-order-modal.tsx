import { NumberFormat, Table } from '@topthink/common';
import InfoModal from '@/components/info-modal';
import ChargeStatus from '@/components/charge-status';

interface Props {
    source: string;
}

export default function InvoiceOrderModal({ source }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            return <Table
                toolBarRender={false}
                card={false}
                source={source}
                columns={[
                    {
                        title: '标题',
                        dataIndex: 'full_subject',
                    },
                    {
                        title: '可开票金额',
                        dataIndex: 'net_amount',
                        width: 150,
                        align: 'center',
                        render({ value }) {
                            return <NumberFormat value={value / 100} />;
                        }
                    },
                    {
                        title: '日期',
                        width: 150,
                        align: 'center',
                        dataIndex: 'create_time',
                    },
                    {
                        title: '状态',
                        width: 100,
                        align: 'center',
                        render({ record }) {
                            return <ChargeStatus charge={record} />;
                        }
                    },
                ]}
            />;
        }}
        text={'查看'}
        header={'关联交易'}
    />;
}
