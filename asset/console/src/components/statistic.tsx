import { Card, styled } from '@topthink/common';
import { ReactNode } from 'react';

export interface StatisticProps {
    title: ReactNode;
    content: ReactNode;
    footer?: ReactNode;
}

export default function Statistic({ title, content, footer }: StatisticProps) {

    return <Card>
        <Title>{title}</Title>
        <Content>{content}</Content>
        {footer && <Footer>{footer}</Footer>}
    </Card>;
}

const Footer = styled.div`
  margin-top: 9px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  color: rgba(0, 0, 0, .65);
`;

const Content = styled.div`
  color: rgba(0, 0, 0, .85);
  font-size: 24px;
`;

const Title = styled.div`
  margin-bottom: 4px;
  color: rgba(0, 0, 0, .45);
  font-size: 1.1rem;
`;

