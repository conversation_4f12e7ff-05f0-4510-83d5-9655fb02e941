import { Loader, styled, useRequest } from '@topthink/common';
import { formatLongNumber } from '@/utils/format';
import { ReactNode } from 'react';

interface MetricDataItem {
    x: string;
    y: number;

    [index: string]: any;
}

interface Metric {
    total: number;
    data: MetricDataItem[];
}

interface Props {
    title: string;
    metric?: string;
    url: string;
    period: string;
    renderLabel?: (item: MetricDataItem) => ReactNode;
    className?: string;
}

export default function MetricTable({
    period,
    url,
    title,
    metric,
    renderLabel,
    className
}: Props) {

    const { result } = useRequest<Metric>({
        url: url,
        params: { period }
    }, { refreshDeps: [period] });

    return <Container className={className}>
        <Header>
            <Row>
                <Label>{title}</Label>
                {metric && <Metric>{metric}</Metric>}
            </Row>
        </Header>
        <Body>
            {result ? result.data.map((item: any, index) => {
                return <Row key={index}>
                    <Label>{renderLabel ? renderLabel(item) : item.x}</Label>
                    <Metric>
                        <Value>{formatLongNumber(item.y)}</Value>
                        <Progress now={((item.y / result.total) * 100).toFixed(0)} />
                    </Metric>
                </Row>;
            }) : <Loader />}
        </Body>
    </Container>;
}

const Metric = styled.div`
    justify-content: center;
    display: flex;
`;

const Value = styled.span`
    text-align: right;
    margin-right: 10px;
    font-weight: bold;
    white-space: nowrap;
`;

const Progress = styled.div<{ now: string }>`
    width: 50px;
    text-align: center;
    border-left: 1px solid var(--bs-gray-500);
    position: relative;

    &:after {
        color: var(--bs-gray-500);
        content: "${props => props.now}%";
    }

    &:before {
        width: ${props => props.now}%;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        opacity: .1;
        background: var(--bs-primary);
    }
`;

const Row = styled.div`
    display: flex;
    justify-content: space-between;
    gap: 10px;
    line-height: 30px;
`;

const Body = styled.div`
    display: flex;
    flex-direction: column;
    gap: 5px;
`;

const Header = styled.div`
    font-weight: bold;

    ${Row} {
        margin-bottom: 10px;
    }

    ${Metric} {
        width: 100px;
    }
`;

const Label = styled.div`
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
`;

const Container = styled.div`

`;
