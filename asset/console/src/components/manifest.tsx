import { Loader, useRequest } from '@topthink/common';
import { PropsWithChildren } from 'react';

export default function Manifest({ children }: PropsWithChildren<any>) {

    const { result } = useRequest<Manifest>('/manifest', {
        onSuccess({ scripts }) {
            const fragment = document.createRange().createContextualFragment(scripts);
            document.body.appendChild(fragment);
        }
    });

    if (!result) {
        return <Loader />;
    }

    return children;
}
