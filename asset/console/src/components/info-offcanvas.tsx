import { Loader, OffcanvasButton, OffcanvasButtonProps, OffcanvasType, RequestConfig, request } from '@topthink/common';
import { ReactNode, createElement, useCallback, useRef, useState } from 'react';

type ChildrenProps<T, P extends Record<string, any>> = {
    data: T;
    action: OffcanvasType;
} & P;

interface Props<T, P extends Record<string, any>> extends OffcanvasButtonProps {
    source?: RequestConfig;
    data?: T;
    renderChildren: (props: ChildrenProps<T, P>) => JSX.Element;
    header?: ReactNode;
    childrenProps?: P;
}

export default function InfoOffcanvas<T = any, P extends Record<string, any> = {}>({
    text,
    data,
    header,
    source,
    childrenProps,
    renderChildren,
    offcanvasProps,
    variant,
    ...props
}: Props<T, P>) {

    const [children, setChildren] = useState(() => <Loader />);

    const ref = useRef<OffcanvasType>(null);

    const handleShow = useCallback(async () => {
        if (!data && source) {
            try {
                data = await request(source);
            } catch {
                setChildren(<div className='text-center'>数据加载失败</div>);
            }
        }

        if (data) {
            setChildren(createElement(renderChildren, {
                data: data,
                action: ref.current!,
                ...(childrenProps as P)
            }));
        }
    }, [renderChildren, childrenProps, source, data]);

    return <OffcanvasButton
        ref={ref}
        text={text}
        variant={variant}
        onShow={handleShow}
        offcanvasProps={{
            header,
            placement: 'end',
            ...offcanvasProps
        }}
        {...props}
    >
        {children}
    </OffcanvasButton>;
}
