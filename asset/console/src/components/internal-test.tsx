import { Access, Card, Result } from '@topthink/common';
import { ReactNode } from 'react';

interface Props {
    children: ReactNode;
    feature: number;
}

export default function InternalTest({ children, feature }: Props) {
    return <Access
        require={(user) => {
            return (user.features & feature) !== 0;
        }}
        fallback={<div className={'container mt-5'}>
            <Card>
                <Result status={'info'} title={'该服务内测中，如有需要请联系管理员开通'} />
            </Card>
        </div>}
    >
        {children}
    </Access>;
}
