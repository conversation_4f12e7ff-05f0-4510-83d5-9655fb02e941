import { Loader, Result, useRequest } from "@topthink/common";

export default function Certification({ id }: { id: number; }) {

    const { result: data, loading } = useRequest(`/admin/user/${id}/certification`);

    if (loading) {
        return <Loader />;
    }

    if (!data) {
        return <Result status='info' title='尚未认证' />;
    }

    return <>
        <dl className='row'>
            <dd className='col-2'>真实姓名</dd>
            <dd className='col-10'>{data.name}</dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>身份证</dd>
            <dd className='col-10'>{data.identity}</dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>认证时间</dd>
            <dd className='col-10'>{data.update_time}</dd>
        </dl>
    </>;
}