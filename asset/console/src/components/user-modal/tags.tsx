import { FormWidgetProps, Loader, ModalForm, useRequest } from '@topthink/common';
import classNames from 'classnames';
import { useEffect, useState } from 'react';

export default function Tags({ id }: { id: number; }) {

    const { result: tags = [], refresh } = useRequest<UserTag[]>(`/admin/user/${id}/tags`);

    return <div className='d-flex flex-column gap-3'>
        <div className='d-flex gap-2 flex-wrap'>
            {tags.map((item) => {
                return <span key={item.id} className='badge fs-6 fw-normal text-bg-light'>{item.name}</span>;
            })}
            <ModalForm
                text='设置标签'
                action={`/admin/user/${id}/tags`}
                method='post'
                buttonProps={{ variant: 'link' }}
                formData={{
                    tags: tags.map((item) => item.id)
                }}
                schema={{
                    type: 'object',
                    properties: {
                        tags: {
                            type: 'string',
                        }
                    }
                }}
                onSuccess={refresh}
                uiSchema={{
                    tags: {
                        'ui:widget': function(props: FormWidgetProps) {
                            const { value = [], onChange } = props;
                            const { result = [], loading } = useRequest<UserTag[]>('/admin/user/tag');

                            const [tags, setTags] = useState<Set<number>>(() => new Set(value));

                            useEffect(() => {
                                onChange(Array.from(tags));
                            }, [tags]);

                            if (loading) {
                                return <Loader />;
                            }

                            return <div className='d-flex gap-2 flex-wrap'>
                                {result.map((item) => {
                                    return <span key={item.id} role='button' onClick={() => {
                                        if (tags.has(item.id)) {
                                            tags.delete(item.id);
                                        } else {
                                            tags.add(item.id);
                                        }
                                        setTags(new Set(tags));
                                    }} className={classNames('badge fs-6 fw-normal', {
                                        'text-bg-light': !tags.has(item.id),
                                        'text-bg-primary': tags.has(item.id)
                                    })}>{item.name}</span>;
                                })}
                            </div>;
                        },
                        'ui:label': false,
                    }
                }}
            />
        </div>

    </div>;
}
