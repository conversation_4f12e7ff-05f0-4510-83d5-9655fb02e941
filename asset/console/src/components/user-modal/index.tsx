import { ModalButtonProps, NumberFormat, styled, Table, User } from '@topthink/common';
import UserTags from './tags';
import { Nav, Tab } from 'react-bootstrap';
import InfoOffcanvas from '../info-offcanvas';
import ChargeStatus from '../charge-status';
import Certification from './certification';
import Enterprise from './enterprise';
import UserNotifyModal from '../user-notify-modal';

interface Props extends Omit<ModalButtonProps, 'id'> {
    id?: number;
    user?: User;
}

export default function UserModal({ id, user, ...props }: Props) {

    return <InfoOffcanvas
        source={id ? `/admin/user/${id}` : undefined}
        header={'用户信息'}
        data={user}
        offcanvasProps={{
            style: {
                width: '1100px',
                maxWidth: '80%'
            },
            bodyAs: Container
        }}
        {...props}
        renderChildren={({ data }) => {
            return <Tab.Container defaultActiveKey={'info'} mountOnEnter unmountOnExit>
                <div className='p-3'>
                    <div className='row g-3'>
                        <div className={'col-12 col-md-2 col-lg-1'}>
                            <img className='rounded-circle' width={60} height={60} src={data.avatar} />
                        </div>
                        <div className={'col-12 col-md-10 col-lg-11'}>
                            <dl className='row g-3'>
                                <dd className='col-12 fs-5'>
                                    {data.name}
                                </dd>
                                <dd className='col-12 col-md-6'>手机：{data.mobile}</dd>
                                <dd className='col-12 col-md-6'>实名认证：{data.is_certified ? '是' : '否'}</dd>
                                <dd className='col-12 col-md-6'>邮箱：{data.email || '--'}</dd>
                                <dd className='col-12 col-md-6'>企业认证：{data.is_enterprise ? '是' : '否'}</dd>
                                <dd className='col-12'>
                                    <UserNotifyModal id={data.id} buttonProps={{ size: 'sm', variant: 'primary' }} />
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
                <StyledNav>
                    <Nav.Item>
                        <Nav.Link eventKey='info'>基本信息</Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                        <Nav.Link eventKey='charge'>支付流水</Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                        <Nav.Link eventKey='money'>余额明细</Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                        <Nav.Link eventKey='coin'>云币明细</Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                        <Nav.Link eventKey='certification'>实名认证</Nav.Link>
                    </Nav.Item>
                    <Nav.Item>
                        <Nav.Link eventKey='enterprise'>企业认证</Nav.Link>
                    </Nav.Item>
                </StyledNav>
                <Tab.Content className='p-3'>
                    <Tab.Pane eventKey='info'>
                        <dl className='row g-3'>
                            <dd className='col-3 col-md-1'>注册IP</dd>
                            <dd className='col-9 col-md-5'>{data.reg_ip || '--'}</dd>
                            <dd className='col-3 col-md-1'>注册时间</dd>
                            <dd className='col-9 col-md-5'>{data.create_time}</dd>
                            <dd className='col-3 col-md-1'>登录IP</dd>
                            <dd className='col-9 col-md-5'>{data.last_ip || '--'}</dd>
                            <dd className='col-3 col-md-1'>登录时间</dd>
                            <dd className='col-9 col-md-5'>{data.last_time || '--'}</dd>
                            <dd className='col-3 col-md-1'>标签</dd>
                            <dd className='col-9'><UserTags id={data.id} /></dd>
                        </dl>
                    </Tab.Pane>
                    <Tab.Pane eventKey='charge'>
                        <Table
                            card={false}
                            toolBarRender={false}
                            source={`/admin/finance/charge?user=${data.id}&status=2`}
                            columns={[{
                                title: '支付单号',
                                dataIndex: 'trade_no',
                                width: 140
                            },
                                {
                                    title: '名称',
                                    dataIndex: 'full_subject',
                                },
                                {
                                    title: '订单金额',
                                    dataIndex: 'amount',
                                    align: 'center',
                                    render({ value }) {
                                        return <NumberFormat value={value / 100} />;
                                    }
                                },
                                {
                                    title: '交易状态',
                                    align: 'center',
                                    dataIndex: 'status',
                                    width: 80,
                                    render({ record }) {
                                        return <ChargeStatus charge={record} />;
                                    }
                                },
                                {
                                    title: '创建时间',
                                    dataIndex: 'create_time',
                                    width: 150
                                }]}
                        />
                    </Tab.Pane>
                    <Tab.Pane eventKey='money'>
                        <Table
                            toolBarRender={false}
                            card={false}
                            source={`/admin/user/${data.id}/money/logs`}
                            columns={[
                                {
                                    title: '日期',
                                    align: 'left',
                                    dataIndex: 'create_time',
                                    width: 200
                                },
                                {
                                    title: '说明',
                                    align: 'left',
                                    dataIndex: 'info',
                                },
                                {
                                    title: '金额',
                                    align: 'right',
                                    dataIndex: 'number',
                                    render({ value, record }) {
                                        const className = record.type === 1 ? 'text-success' : 'text-danger';

                                        return <span className={className}>
                                            {record.type === 1 ? '+' : '-'}
                                            <NumberFormat currency={false} value={value / 100} />
                                        </span>;
                                    }
                                }
                            ]}
                        />
                    </Tab.Pane>
                    <Tab.Pane eventKey='coin'>
                        <Table
                            toolBarRender={false}
                            card={false}
                            source={`/admin/user/${data.id}/coin/logs`}
                            columns={[
                                {
                                    title: '日期',
                                    align: 'left',
                                    dataIndex: 'create_time',
                                    width: 200
                                },
                                {
                                    title: '说明',
                                    align: 'left',
                                    dataIndex: 'info',
                                },
                                {
                                    title: '数量',
                                    align: 'right',
                                    dataIndex: 'number',
                                    render({ value, record }) {
                                        const className = record.type === 1 ? 'text-success' : 'text-danger';

                                        return <span className={className}>
                                            {record.type === 1 ? '+' : '-'}
                                            <NumberFormat currency={false} value={value / 100} />
                                        </span>;
                                    }
                                }
                            ]}
                        />
                    </Tab.Pane>
                    <Tab.Pane eventKey='certification'>
                        <Certification id={data.id} />
                    </Tab.Pane>
                    <Tab.Pane eventKey='enterprise'>
                        <Enterprise id={data.id} />
                    </Tab.Pane>
                </Tab.Content>
            </Tab.Container>;
        }}
    />;
}

const Container = styled.div`
    padding: 0;

    .tab-content {
        position: relative;
    }
`;

const StyledNav = styled(Nav)`
    background-color: var(--bs-light);

    .nav-link {
        position: relative;

        &.active {
            background-color: #FFFFFF;
            color: var(--bs-primary);

            &:after {
                content: '';
                height: 2px;
                background: var(--bs-primary);
                display: block;
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
            }
        }
    }
`;
