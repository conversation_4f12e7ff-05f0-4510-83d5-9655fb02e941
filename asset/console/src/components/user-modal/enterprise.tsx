import { Loader, Result, useRequest } from "@topthink/common";
import ImageZoom from "../image-zoom";

export default function Enterprise({ id }: { id: number; }) {
    const { result: data, loading } = useRequest(`/admin/user/${id}/enterprise`);

    if (loading) {
        return <Loader />;
    }

    if (!data) {
        return <Result status='info' title='尚未认证' />;
    }

    return <>
        <dl className='row'>
            <dd className='col-2'>公司名称</dd>
            <dd className='col-10'>{data.name}</dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>统一社会信用代码</dd>
            <dd className='col-10'>{data.identity}</dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>企业法人</dd>
            <dd className='col-10'>{data.corporation}</dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>营业执照</dd>
            <dd className='col-10'>
                <ImageZoom>
                    <img src={data.img} height={20} />
                </ImageZoom>
            </dd>
        </dl>
        <dl className='row'>
            <dd className='col-2'>认证时间</dd>
            <dd className='col-10'>{data.update_time}</dd>
        </dl>
    </>;
}