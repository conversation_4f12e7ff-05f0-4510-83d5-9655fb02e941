import { styled } from '@topthink/common';
import classNames from 'classnames';
import RcRate from 'rc-rate';
import { ComponentProps, Suspense } from 'react';

export default function Rate({ size, ...props }: ComponentProps<typeof StyledRate> & { size?: 'sm' | 'lg' }) {
    return <Suspense fallback={null}>
        <StyledRate
            character={<i className={classNames('bi bi-star-fill', {
                'fs-6': !size,
                'fs-7': size === 'sm',
                'fs-5': size === 'lg'
            })} />}
            {...props}
        />
    </Suspense>;
}

const StyledRate = styled(RcRate)`
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    align-items: center;
    font-weight: normal;
    font-style: normal;
    outline: none;


    &.rc-rate-rtl {
        direction: rtl;
    }

    &.rc-rate-disabled .rc-rate-star {
        cursor: default;
    }

    &.rc-rate-disabled .rc-rate-star:before,
    &.rc-rate-disabled .rc-rate-star-content:before {
        cursor: default;
    }

    &.rc-rate-disabled .rc-rate-star:hover {
        transform: scale(1);
    }

    .rc-rate-star {
        margin: 0;
        padding: 0;
        display: inline-block;
        margin-right: 4px;
        position: relative;
        transition: all 0.3s;
        color: #cecece;
        cursor: pointer;
        line-height: 1.5;
    }

    &.rc-rate-rtl .rc-rate-star {
        margin-right: 0;
        margin-left: 8px;
        float: right;
    }

    .rc-rate-star-first,
    .rc-rate-star-second {
        transition: all 0.3s;
        display: flex;
        align-items: center;
    }

    .rc-rate-star-focused,
    .rc-rate-star:hover {
        transform: scale(1.1);
    }

    .rc-rate-star-first {
        position: absolute;
        left: 0;
        top: 0;
        width: 50%;
        height: 100%;
        overflow: hidden;
        opacity: 0;
    }

    &.rc-rate-rtl .rc-rate-star-first {
        right: 0;
        left: auto;
    }

    .rc-rate-star-half .rc-rate-star-first,
    .rc-rate-star-half .rc-rate-star-second {
        opacity: 1;
    }

    .rc-rate-star-half .rc-rate-star-first,
    .rc-rate-star-full .rc-rate-star-second {
        color: rgba(var(--bs-orange-rgb), 1);
    }

    .rc-rate-star-half:hover .rc-rate-star-first,
    .rc-rate-star-full:hover .rc-rate-star-second {
        color: rgba(var(--bs-orange-rgb), .8);
    }

`;
