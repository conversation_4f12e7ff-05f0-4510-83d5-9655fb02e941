import dayjs from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';

dayjs.extend(weekOfYear);

export function formatMoney(value: number) {
    const intl = new Intl.NumberFormat('zh-Hans-CN', {
        currency: 'CNY',
        style: 'currency',
        minimumFractionDigits: 2,
    });

    return intl.format(value);
}

export function formatPeriodDate(period: string, text: string) {
    const date = dayjs(text);

    switch (period) {
        case '1year':
        case 'half-year':
            return date.format('YYYY-MM');
        case '180days':
        case '6months':
            return date.format('MM-DD');
        case '90days':
        case '30days':
        case 'last-month':
            return date.format('YYYY-MM-DD');
        case '7days':
            return date.format('MM-DD HH:mm');
        case 'yesterday':
        case '24hours':
        default:
            return date.format('HH:mm');
    }
}

export function formatNumber(n: number | string) {
    return Number(n).toFixed(0);
}

export function formatLongNumber(value: number | string, fixed: number = 2) {
    const n = Number(value);

    if (n >= 1000000) {
        if (n % 1000000 === 0) {
            fixed = 0;
        }
        return `${(n / 1000000).toFixed(fixed)}M`;
    }

    if (n >= 1000) {
        if (n % 1000 === 0) {
            fixed = 0;
        }
        return `${(n / 1000).toFixed(fixed)}K`;
    }

    return String(n);
}

export function parseTime(val: number) {
    const days = ~~(val / 86400);
    const hours = ~~(val / 3600) - days * 24;
    const minutes = ~~(val / 60) - days * 1440 - hours * 60;
    const seconds = ~~val - days * 86400 - hours * 3600 - minutes * 60;
    const ms = (val - ~~val) * 1000;

    return {
        days,
        hours,
        minutes,
        seconds,
        ms,
    };
}

export function formatShortTime(val: number, formats = ['m', 's'], space = '') {
    const { days, hours, minutes, seconds, ms } = parseTime(val);

    let t = '';

    if (days > 0 && formats.indexOf('d') !== -1) t += `${days}d${space}`;
    if (hours > 0 && formats.indexOf('h') !== -1) t += `${hours}h${space}`;
    if (minutes > 0 && formats.indexOf('m') !== -1) t += `${minutes}m${space}`;
    if (seconds > 0 && formats.indexOf('s') !== -1) t += `${seconds}s${space}`;
    if (ms > 0 && formats.indexOf('ms') !== -1) t += `${ms}ms`;

    if (!t) {
        return `0${formats[formats.length - 1]}`;
    }

    return t;
}
