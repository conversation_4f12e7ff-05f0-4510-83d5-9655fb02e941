export default async function download(files: Record<string, string>, saveAs: string) {
    const JSZip = (await import('jszip')).default;
    const saver = (await import('file-saver')).default;

    const zip = new JSZip();

    for (const [name, file] of Object.entries(files)) {
        zip.file(name, file);
    }

    const content = await zip.generateAsync({ type: 'blob' });
    // see FileSaver.js
    saver(content, saveAs);
}
