import { Message, request } from '@topthink/common';

interface Options {
    result: PayResult;
    onComplete?: () => void;
}

export default function awaitPayComplete({ result, onComplete }: Options) {
    window.open(result.pay_url);

    let open = true;

    Message.confirm({
        title: '支付结果',
        text: '请在新打开的页面上进行支付，支付完成后再关闭此窗口',
        icon: undefined,
        confirmButtonText: '已完成支付',
        showLoaderOnConfirm: true,
        async preConfirm() {
            try {
                await request({
                    url: `/order/check`,
                    method: 'post',
                    data: { order_no: result.order_no },
                    raxConfig: {
                        shouldRetry() {
                            return false;
                        }
                    }
                });
                onComplete?.();
                return true;
            } catch {
                return false;
            }
        },
        willClose() {
            open = false;
        }
    });

    request({
        url: `/order/check`,
        method: 'post',
        data: { order_no: result.order_no },
        raxConfig: {
            shouldRetry(err) {
                return open && err.response?.status === 449;
            }
        }
    }).then(() => {
        onComplete?.();
        Message.close();
    }).catch(() => null);
}
