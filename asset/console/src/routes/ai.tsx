import Activate from '@/pages/ai/activate';
import { Navigate, Outlet, request, RouteObject, SiderLayout, TabLayout } from '@topthink/common';

const ai: RouteObject = {
    path: 'ai',
    id: 'ai',
    async loader() {
        try {
            return await request('/ai');
        } catch {
            return null;
        }
    },
    element: <Activate><SiderLayout /></Activate>,
    meta: {
        title: 'ThinkAI'
    },
    children: [
        {
            index: true,
            element: <Navigate to='model' replace />
        },
        {
            path: 'model',
            element: <Outlet />,
            meta: {
                title: '模型列表'
            },
            children: [
                {
                    element: <TabLayout />,
                    children: [
                        {
                            index: true,
                            element: <Navigate to={'chat'} replace />
                        },
                        {
                            path: 'chat',
                            lazy: () => import('@/pages/ai/model/chat'),
                            meta: {
                                title: '会话'
                            }
                        },
                        {
                            path: 'image',
                            lazy: () => import('@/pages/ai/model/image'),
                            meta: {
                                title: '图像'
                            }
                        },
                        {
                            path: 'video',
                            lazy: () => import('@/pages/ai/model/video'),
                            meta: {
                                title: '视频'
                            }
                        },
                        {
                            path: 'audio',
                            lazy: () => import('@/pages/ai/model/audio'),
                            meta: {
                                title: '语音'
                            }
                        },
                        {
                            path: 'text',
                            lazy: () => import('@/pages/ai/model/text'),
                            meta: {
                                title: '文本'
                            }
                        }
                    ]
                }
            ]
        },
        {
            path: 'plugin',
            lazy: () => import('@/pages/ai/plugin'),
            meta: {
                title: '插件列表'
            }
        },
        {
            path: 'usage',
            element: <Outlet />,
            meta: {
                title: '用量管理'
            },
            children: [
                {
                    element: <TabLayout />,
                    children: [
                        {
                            index: true,
                            element: <Navigate to='stats' replace />
                        },
                        {
                            path: 'stats',
                            lazy: () => import('@/pages/ai/usage/stats'),
                            meta: {
                                title: '用量统计'
                            }
                        },
                        {
                            path: 'buy',
                            lazy: () => import('@/pages/ai/usage/buy'),
                            meta: {
                                title: '账号充值'
                            }
                        }
                    ]
                }
            ]
        },
        {
            path: 'token',
            meta: {
                title: '访问令牌',
                href: '/user/token'
            }
        },
        {
            path: 'doc',
            meta: {
                title: '文档中心',
                href: 'https://doc.topthink.com/think-ai'
            }
        },
    ]
};

export default ai;
