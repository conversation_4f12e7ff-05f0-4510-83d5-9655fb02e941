import { RouteObject, Navigate } from '@topthink/common';

const user: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='info' replace />
    },
    {
        path: 'info',
        lazy: () => import('@/pages/user/info'),
        meta: {
            title: '基本信息'
        }
    },
    {
        path: 'certification',
        lazy: () => import('@/pages/user/certification'),
        meta: {
            title: '实名认证'
        }
    },
    {
        path: 'enterprise',
        lazy: () => import('@/pages/user/enterprise'),
        meta: {
            title: '企业认证'
        }
    },
    {
        path: 'social',
        lazy: () => import('@/pages/user/social'),
        meta: {
            title: '账号绑定'
        }
    },
    {
        path: 'token',
        lazy: () => import('@/pages/user/token'),
        meta: {
            title: '访问令牌'
        }
    },
];


export default user;
