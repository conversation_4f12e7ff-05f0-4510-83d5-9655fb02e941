import { Navigate, Outlet, RouteObject } from '@topthink/common';

const finance: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='dashboard' replace />
    },
    {
        path: 'dashboard',
        lazy: () => import('@/pages/finance/dashboard'),
        meta: {
            title: '财务概览'
        }
    },
    {
        path: 'money',
        element: <Outlet />,
        meta: {
            title: '余额管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/finance/money'),
            },
            {
                path: 'withdraw',
                lazy: () => import('@/pages/finance/money/withdraw'),
            }
        ]
    },
    {
        path: 'coin',
        element: <Outlet />,
        meta: {
            title: '云币管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/finance/coin'),
            },
            {
                path: 'withdraw',
                lazy: () => import('@/pages/finance/coin/withdraw'),
            }
        ]
    },
    {
        path: 'charge',
        lazy: () => import('@/pages/finance/charge'),
        meta: {
            title: '交易管理'
        }
    },
    {
        path: 'invoice',
        element: <Outlet />,
        meta: {
            title: '发票管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/finance/invoice')
            },
            {
                path: 'create',
                lazy: () => import('@/pages/finance/invoice/create')
            }
        ]
    },
];


export default finance;
