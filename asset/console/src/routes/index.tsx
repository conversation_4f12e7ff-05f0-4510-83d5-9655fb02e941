import Dashboard from '../pages/dashboard';
import { Access, request, RouteObject, SiderLayout } from '@topthink/common';
import BasicLayout from '@/layout/basic';
import ErrorBoundary from '@/components/error-boundary';
import user from '@/routes/user';
import admin from './admin';
import finance from './finance';
import ssl from './ssl';
import assistant from '@/routes/assistant';
import api from '@/routes/api';
import sms from '@/routes/sms';
import ai from './ai';
import partner from './partner';
import EnterpriseAccess from '@/components/enterprise-access';
import PageResult from '@/components/page-result';
import SmsActivate from '@/pages/sms/activate';

const routes: RouteObject[] = [
    {
        element: <BasicLayout />,
        loader() {
            return request('/manifest');
        },
        shouldRevalidate() {
            return false;
        },
        children: [
            {
                errorElement: <ErrorBoundary />,
                children: [
                    {
                        index: true,
                        element: <Dashboard />
                    },
                    {
                        path: 'sms',
                        element: <EnterpriseAccess>
                            <SmsActivate>
                                <SiderLayout />
                            </SmsActivate>
                        </EnterpriseAccess>,
                        id: 'sms',
                        async loader() {
                            try {
                                return await request('/sms');
                            } catch {
                                return null;
                            }
                        },
                        children: sms,
                        meta: {
                            title: '短信服务'
                        }
                    },
                    {
                        path: 'api',
                        element: <SiderLayout />,
                        children: api,
                        meta: {
                            title: 'API 服务'
                        }
                    },
                    {
                        path: 'ssl',
                        element: <SiderLayout />,
                        children: ssl,
                        meta: {
                            title: 'SSL 证书'
                        }
                    },
                    {
                        path: 'assistant',
                        element: <SiderLayout />,
                        children: assistant,
                        meta: {
                            title: '运营助理'
                        }
                    },
                    ai,
                    partner,
                    {
                        path: 'user',
                        element: <SiderLayout />,
                        children: user,
                        meta: {
                            title: '个人中心'
                        }
                    },
                    {
                        path: 'finance',
                        element: <SiderLayout />,
                        children: finance,
                        meta: {
                            title: '财务中心'
                        }
                    },
                    {
                        path: 'admin',
                        element: <Access require='admin' fallback={<PageResult status={'error'} title={'无权限'} />}>
                            <SiderLayout />
                        </Access>,
                        children: admin,
                        meta: {
                            title: '管理中心'
                        }
                    },
                ]
            }
        ]
    }
];

export default routes;
