import { Navigate, RouteObject } from '@topthink/common';

const ssl: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='cert' replace />
    },
    {
        path: 'cert',
        lazy: () => import('@/pages/ssl/cert'),
        meta: {
            title: '我的证书'
        }
    },
    {
        path: 'order',
        lazy: () => import('@/pages/ssl/order'),
        meta: {
            title: '我的订单'
        }
    },
    {
        path: 'info',
        lazy: () => import('@/pages/ssl/info'),
        meta: {
            title: '我的信息'
        }
    },
    {
        path: 'buy',
        lazy: () => import('@/pages/ssl/buy'),
        meta: {
            title: '购买证书',
            hideInMenu: true
        }
    }
];


export default ssl;
