import { Navigate, Outlet, request, RouteObject } from '@topthink/common';

const api: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='list' replace />
    },
    {
        path: 'list',
        lazy: () => import('@/pages/api/list'),
        meta: {
            title: '我的API'
        }
    },
    {
        path: 'vip',
        lazy: () => import('@/pages/api/vip'),
        meta: {
            title: '会员中心'
        }
    },
    {
        path: 'mcp',
        meta: {
            title: 'MCP Server',
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/api/mcp'),
            },
            {
                path: 'create',
                lazy: () => import('@/pages/api/mcp/create'),
                meta: {
                    title: '创建 Server',
                    hideInMenu: true
                }
            },
            {
                path: ':id/edit',
                lazy: () => import('@/pages/api/mcp/edit'),
                meta: {
                    title: '编辑 Server',
                    hideInMenu: true
                }
            }
        ]
    },
    {
        path: 'token',
        meta: {
            title: '访问令牌',
            href: '/user/token'
        }
    },
    {
        path: 'doc',
        meta: {
            title: '文档中心',
            href: 'https://doc.topthink.com/think-api'
        }
    },
    {
        path: 'apply/:id?',
        lazy: () => import('@/pages/api/apply'),
    },
    {
        path: ':id',
        element: <Outlet />,
        id: 'api',
        loader: ({ params }) => {
            return request(`/api/${params.id}`);
        },
        children: [
            {
                path: 'package',
                lazy: () => import('@/pages/api/package'),
            },
            {
                path: 'package/buy/:packageId?',
                lazy: () => import('@/pages/api/package/buy'),
            },
            {
                path: 'stats',
                lazy: () => import('@/pages/api/stats'),
            }
        ]
    },

];


export default api;
