import { Navigate, Outlet, request, RouteObject } from '@topthink/common';

const sms: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='dashboard' replace />
    },
    {
        path: 'dashboard',
        lazy: () => import('@/pages/sms/dashboard'),
        meta: {
            title: '概览'
        }
    },
    {
        path: 'sign',
        element: <Outlet />,
        meta: {
            title: '签名管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/sms/sign'),
            },
            {
                path: 'create',
                lazy: () => import('@/pages/sms/sign/create'),
            },
            {
                path: ':id/edit',
                loader({ params: { id } }) {
                    return request(`/sms/sign/${id}`);
                },
                lazy: () => import('@/pages/sms/sign/edit')
            }
        ]
    },
    {
        path: 'template',
        element: <Outlet />,
        meta: {
            title: '模板管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/sms/template'),
            },
            {
                path: 'create',
                lazy: () => import('@/pages/sms/template/create'),
            },
            {
                path: ':id/edit',
                loader({ params: { id } }) {
                    return request(`/sms/template/${id}`);
                },
                lazy: () => import('@/pages/sms/template/edit'),
            }
        ]
    },
    {
        path: 'task',
        element: <Outlet />,
        meta: {
            title: '群发助手'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/sms/task')
            },
            {
                path: 'create',
                lazy: () => import('@/pages/sms/task/create')
            },
            {
                path: ':id',
                lazy: () => import('@/pages/sms/task/detail')
            }
        ]
    },
    {
        path: 'package',
        element: <Outlet />,
        meta: {
            title: '套餐管理'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/sms/package'),
            },
            {
                path: 'buy',
                lazy: () => import('@/pages/sms/package/buy'),
            }
        ]
    },
    {
        path: 'log',
        lazy: () => import('@/pages/sms/log'),
        meta: {
            title: '发送记录'
        }
    },
    {
        path: 'reply',
        lazy: () => import('@/pages/sms/reply'),
        meta: {
            title: '回复记录'
        }
    },
    {
        path: 'setting',
        lazy: () => import('@/pages/sms/setting'),
        meta: {
            title: '系统设置'
        }
    },
    {
        path: 'token',
        meta: {
            title: '访问令牌',
            href: '/user/token'
        }
    },
    {
        path: 'doc',
        meta: {
            title: '文档中心',
            href: 'https://doc.topthink.com/think-sms'
        }
    },
];


export default sms;
