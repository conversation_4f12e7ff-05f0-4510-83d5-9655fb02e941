import { Navigate, Outlet, request, RouteObject, TabLayout } from '@topthink/common';

const site: RouteObject = {
    path: 'site',
    element: <Outlet />,
    meta: {
        title: '网站助理'
    },
    children: [
        {
            index: true,
            lazy: () => import('@/pages/assistant/site')
        },
        {
            path: 'invite/:code',
            lazy: () => import('@/pages/assistant/site/invite')
        },
        {
            path: ':id',
            element: <Outlet />,
            children: [
                {
                    index: true,
                    element: <Navigate to='dashboard' replace />
                },
                {
                    id: 'assistant/site',
                    loader: ({ params }) => {
                        return request(`/assistant/site/${params.id}`);
                    },
                    lazy: () => import('@/pages/assistant/site/layout'),
                    children: [
                        {
                            path: 'dashboard',
                            lazy: () => import('@/pages/assistant/site/dashboard'),
                            meta: {
                                title: '网站概览',
                                icon: 'grid'
                            },
                        },
                        {
                            path: 'menu',
                            element: <Outlet />,
                            meta: {
                                title: '悬浮按钮',
                                icon: 'menu-button'
                            },
                            children: [
                                {
                                    element: <TabLayout />,
                                    children: [
                                        {
                                            index: true,
                                            element: <Navigate to='features' replace />
                                        },
                                        {
                                            path: 'features',
                                            lazy: () => import('@/pages/assistant/site/menu/features'),
                                            meta: {
                                                title: '功能设置',
                                            }
                                        },
                                        {
                                            path: 'customs',
                                            lazy: () => import('@/pages/assistant/site/menu/customs'),
                                            meta: {
                                                title: '自定义按钮',
                                            }
                                        },
                                        {
                                            path: 'appearance',
                                            lazy: () => import('@/pages/assistant/site/menu/appearance'),
                                            meta: {
                                                title: '外观样式',
                                            }
                                        }
                                    ]
                                },
                            ]
                        },
                        {
                            path: 'broadcast',
                            lazy: () => import('@/pages/assistant/site/broadcast'),
                            meta: {
                                title: '全站广播',
                                icon: 'broadcast-pin'
                            },
                        },
                        {
                            path: 'feedback',
                            element: <Outlet />,
                            meta: {
                                title: '网站评价',
                                icon: 'chat-square-text'
                            },
                            children: [
                                {
                                    index: true,
                                    lazy: () => import('@/pages/assistant/site/feedback')
                                },
                                {
                                    path: ':feedbackId',
                                    lazy: () => import('@/pages/assistant/site/feedback/layout'),
                                    id: 'assistant/site/feedback',
                                    loader: ({ params: { id, feedbackId } }) => {
                                        return request(`/assistant/site/${id}/feedback/${feedbackId}`);
                                    },
                                    children: [
                                        {
                                            index: true,
                                            element: <Navigate to='dashboard' replace />
                                        },
                                        {
                                            path: 'dashboard',
                                            lazy: () => import('@/pages/assistant/site/feedback/dashboard'),
                                            meta: {
                                                icon: 'grid',
                                                title: '概览',
                                            }
                                        },
                                        {
                                            path: 'entry',
                                            lazy: () => import('@/pages/assistant/site/feedback/entry'),
                                            meta: {
                                                icon: 'card-list',
                                                title: '数据',
                                            }
                                        },
                                        {
                                            path: 'question',
                                            lazy: () => import('@/pages/assistant/site/feedback/question'),
                                            meta: {
                                                icon: 'file-earmark-medical',
                                                title: '问题',
                                            }
                                        },
                                        {
                                            path: 'distribution',
                                            lazy: () => import('@/pages/assistant/site/feedback/distribution'),
                                            meta: {
                                                icon: 'send',
                                                title: '投放',
                                            }
                                        },
                                        {
                                            path: 'setting',
                                            lazy: () => import('@/pages/assistant/site/feedback/setting'),
                                            meta: {
                                                icon: 'gear',
                                                title: '设置',
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            path: 'member',
                            lazy: () => import('@/pages/assistant/site/member'),
                            meta: {
                                title: '成员管理',
                                icon: 'people'
                            },
                        },
                        {
                            path: 'setting',
                            element: <Outlet />,
                            meta: {
                                title: '网站设置',
                                icon: 'gear'
                            },
                            children: [
                                {
                                    element: <TabLayout />,
                                    children: [
                                        {
                                            index: true,
                                            element: <Navigate to='info' replace />
                                        },
                                        {
                                            path: 'info',
                                            lazy: () => import('@/pages/assistant/site/setting/info'),
                                            meta: {
                                                title: '基本信息',
                                            }
                                        },
                                        {
                                            path: 'tracking',
                                            lazy: () => import('@/pages/assistant/site/setting/tracking'),
                                            meta: {
                                                title: 'SDK 代码',
                                            }
                                        },
                                        {
                                            path: 'scripts',
                                            lazy: () => import('@/pages/assistant/site/setting/scripts'),
                                            meta: {
                                                title: '自定义脚本',
                                            }
                                        }
                                    ]
                                },
                            ]
                        }
                    ],
                }
            ]
        },
    ]
};

export default site;
