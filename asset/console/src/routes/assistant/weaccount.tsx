import { Navigate, Outlet, request, RouteObject } from '@topthink/common';

const weaccount: RouteObject = {
    path: 'weaccount',
    element: <Outlet />,
    meta: {
        title: '公众号助理'
    },
    children: [
        {
            index: true,
            lazy: () => import('@/pages/assistant/weaccount')
        },
        {
            path: ':id',
            element: <Outlet />,
            children: [
                {
                    index: true,
                    element: <Navigate to='info' replace />,
                },
                {
                    id: 'assistant/weaccount',
                    loader: ({ params }) => {
                        return request(`/assistant/weaccount/${params.id}`);
                    },
                    lazy: () => import('@/pages/assistant/weaccount/layout'),
                    children: [
                        {
                            path: 'info',
                            lazy: () => import('@/pages/assistant/weaccount/info'),
                            meta: {
                                title: '基本信息',
                            },
                        },
                        {
                            path: 'token',
                            lazy: () => import('@/pages/assistant/weaccount/token'),
                            meta: {
                                title: 'API令牌',
                            }
                        }
                    ]
                }
            ]
        }
    ]
};

export default weaccount;
