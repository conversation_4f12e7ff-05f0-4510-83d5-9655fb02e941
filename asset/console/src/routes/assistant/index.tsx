import { Navigate, RouteObject } from '@topthink/common';
import site from './site';
import weaccount from './weaccount';

const assistant: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='site' replace />
    },
    site,
    weaccount,
    {
        path: 'hecong',
        lazy: () => import('@/pages/assistant/hecong'),
        meta: {
            title: '合从客服'
        }
    },
    {
        path: 'qingyan',
        meta: {
            title: '轻言社区',
            href: 'https://q.topthink.com'
        }
    },
    {
        path: 'doc',
        meta: {
            title: '文档中心',
            href: 'https://doc.topthink.com/assistant'
        }
    },
];


export default assistant;
