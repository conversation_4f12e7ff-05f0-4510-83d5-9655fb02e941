import { Navigate, Outlet, request, RouteObject, TabLayout } from '@topthink/common';

export const partner: RouteObject = {
    path: 'partner',
    id: 'partner',
    async loader() {
        try {
            return await request('/partner');
        } catch {
            return null;
        }
    },
    lazy: () => import('@/pages/partner/activate'),
    meta: {
        title: '合作伙伴'
    },
    children: [
        {
            index: true,
            element: <Navigate to='promo' replace />
        },
        {
            path: 'promo',
            element: <TabLayout />,
            meta: {
                title: '邀请奖励',
                hideChildrenInMenu: true
            },
            children: [
                {
                    index: true,
                    element: <Navigate to='dashboard' replace />
                },
                {
                    path: 'dashboard',
                    lazy: () => import('@/pages/partner/promo/dashboard'),
                    meta: {
                        title: '概览'
                    }
                },
                {
                    path: 'customer',
                    lazy: () => import('@/pages/partner/promo/customer'),
                    meta: {
                        title: '关联客户'
                    }
                }
            ]
        },
        {
            path: 'license',
            lazy: () => import('@/pages/partner/license'),
            meta: {
                title: '应用代理',
                hideChildrenInMenu: true
            },
            children: [
                {
                    index: true,
                    element: <Navigate to='wiki' replace />
                },
                {
                    path: 'wiki',
                    lazy: () => import('@/pages/partner/license/wiki'),
                    meta: {
                        title: 'ThinkWiki',
                    },
                },
                {
                    path: 'bot',
                    lazy: () => import('@/pages/partner/license/bot'),
                    meta: {
                        title: 'ThinkBot'
                    },
                },
                {
                    path: 'chat',
                    lazy: () => import('@/pages/partner/license/chat'),
                    meta: {
                        title: 'ThinkChat'
                    },
                },
                {
                    path: 'ai',
                    lazy: () => import('@/pages/partner/license/ai'),
                    meta: {
                        title: 'ThinkAI'
                    },
                },
            ]
        },
        {
            path: 'commission',
            element: <Outlet />,
            meta: {
                title: '佣金管理',
                hideChildrenInMenu: true
            },
            children: [
                {
                    index: true,
                    lazy: () => import('@/pages/partner/commission'),
                },
                {
                    path: 'settlement',
                    lazy: () => import('@/pages/partner/commission/settlement'),
                    meta: {
                        title: '佣金结算'
                    }
                }
            ]
        },
    ]
};

export default partner;
