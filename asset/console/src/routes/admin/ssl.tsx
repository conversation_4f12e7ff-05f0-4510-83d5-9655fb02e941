import { Outlet, RouteObject } from '@topthink/common';

const ssl: RouteObject = {
    path: 'ssl',
    element: <Outlet />,
    meta: {
        title: 'SSL 证书'
    },
    children: [
        {
            path: 'dashboard',
            lazy: () => import('@/pages/admin/product/ssl/dashboard'),
            meta: {
                title: '概览'
            },
        },
        {
            path: 'order',
            lazy: () => import('@/pages/admin/product/ssl/order'),
            meta: {
                title: '订单管理'
            },
        },
        {
            path: 'brand',
            lazy: () => import('@/pages/admin/product/ssl/brand'),
            meta: {
                title: '品牌管理'
            },
        },
    ]
};

export default ssl;
