import { Navigate, Outlet, request, RouteObject } from '@topthink/common';


const api: RouteObject = {
    path: 'api',
    element: <Outlet />,
    meta: {
        title: 'API 服务'
    },
    children: [
        {
            index: true,
            element: <Navigate to='dashboard' replace />
        },
        {
            path: 'dashboard',
            lazy: () => import('@/pages/admin/product/api/dashboard'),
            meta: {
                title: '概览'
            },
        },
        {
            path: 'list',
            lazy: () => import('@/pages/admin/product/api/list'),
            meta: {
                title: 'API 管理'
            },
        },
        {
            path: 'category',
            lazy: () => import('@/pages/admin/product/api/category'),
            meta: {
                title: '分类管理'
            },
        },
        {
            path: 'vip',
            lazy: () => import('@/pages/admin/product/api/vip'),
            meta: {
                title: '会员管理'
            },
        },
        {
            path: 'package',
            lazy: () => import('@/pages/admin/product/api/user-package'),
            meta: {
                title: '套餐管理'
            }
        },
        {
            path: 'create',
            lazy: () => import('@/pages/admin/product/api/create'),
        },
        {
            path: ':id',
            element: <Outlet />,
            id: 'admin/api',
            loader: ({ params }) => {
                return request(`/admin/product/api/${params.id}`);
            },
            children: [
                {
                    path: 'edit',
                    lazy: () => import('@/pages/admin/product/api/edit'),
                },
                {
                    path: 'package',
                    lazy: () => import('@/pages/admin/product/api/package'),
                },
                {
                    path: 'stats',
                    lazy: () => import('@/pages/admin/product/api/stats')
                }
            ]
        },
    ]
};

export default api;
