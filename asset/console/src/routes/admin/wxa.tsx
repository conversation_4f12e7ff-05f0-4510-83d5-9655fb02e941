import { Outlet, RouteObject } from '@topthink/common';

const wxa: RouteObject = {
    path: 'wxa',
    element: <Outlet />,
    meta: {
        title: '微信开放平台',
        icon: 'wechat'
    },
    children: [
        {
            path: 'weaccount',
            lazy: () => import('@/pages/admin/wxa/weaccount'),
            meta: {
                title: '公众号管理'
            },
        },
        {
            path: 'weapp',
            lazy: () => import('@/pages/admin/wxa/weapp'),
            meta: {
                title: '小程序管理'
            },
        },
        {
            path: 'template',
            lazy: () => import('@/pages/admin/wxa/template'),
            meta: {
                title: '小程序模板'
            },
        },
    ]
};

export default wxa;
