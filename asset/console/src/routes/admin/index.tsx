import { Navigate, Outlet, RouteObject } from '@topthink/common';
import approval from '@/routes/admin/approval';
import user from '@/routes/admin/user';
import assistant from '@/routes/admin/assistant';
import ssl from '@/routes/admin/ssl';
import wxa from './wxa';
import api from '@/routes/admin/api';
import sms from './sms';
import ai from './ai';
import finance from './finance';
import sales from './sales';

const admin: RouteObject[] = [
    {
        index: true,
        element: <Navigate to='dashboard' replace />
    },
    {
        path: 'dashboard',
        lazy: () => import('@/pages/admin/dashboard'),
        meta: {
            title: '概览',
            icon: 'speedometer2'
        },
    },
    {
        path: 'product',
        element: <Outlet />,
        meta: {
            title: '产品',
            icon: 'grid'
        },
        children: [
            api,
            sms,
            ssl,
            assistant,
            ai,
        ]
    },
    {
        path: 'user',
        element: <Outlet />,
        meta: {
            title: '用户',
            icon: 'people'
        },
        children: user
    },
    {
        path: 'finance',
        element: <Outlet />,
        meta: {
            title: '财务',
            icon: 'currency-yen'
        },
        children: finance
    },
    sales,
    {
        path: 'approval',
        element: <Outlet />,
        meta: {
            title: '审批',
            icon: 'clipboard-check'
        },
        children: approval
    },
    {
        path: 'application',
        element: <Outlet />,
        meta: {
            title: '应用',
            icon: 'app'
        },
        children: [
            {
                index: true,
                lazy: () => import('@/pages/admin/application'),
            },
            {
                path: ':id',
                lazy: () => import('@/pages/admin/application/detail')
            }
        ]
    },
    wxa,
    {
        path: 'setting',
        lazy: () => import('@/pages/admin/setting'),
        meta: {
            title: '设置',
            icon: 'gear'
        }
    }
];

export default admin;
