import { Navigate, Outlet, RouteObject, TabLayout, request } from '@topthink/common';

const sms: RouteObject = {
    path: 'sms',
    element: <Outlet />,
    meta: {
        title: '短信服务',
    },
    children: [
        {
            index: true,
            element: <Navigate to='dashboard' replace />
        },
        {
            path: 'dashboard',
            lazy: () => import('@/pages/admin/product/sms/dashboard'),
            meta: {
                title: '概览'
            }
        },
        {
            path: 'sign',
            meta: {
                title: '签名管理'
            },
            lazy: () => import('@/pages/admin/product/sms/sign')
        },
        {
            path: 'template',
            meta: {
                title: '模板管理'
            },
            children: [
                {
                    index: true,
                    element: <Navigate to='user' replace />
                },
                {
                    path: 'create',
                    lazy: () => import('@/pages/admin/product/sms/template/create'),
                },
                {
                    path: ':id/edit',
                    loader({ params: { id } }) {
                        return request(`/admin/product/sms/template/${id}`);
                    },
                    lazy: () => import('@/pages/admin/product/sms/template/edit'),
                },
                {
                    element: <TabLayout />,
                    children: [
                        {
                            path: 'user',
                            lazy: () => import('@/pages/admin/product/sms/template/user'),
                            meta: {
                                title: '用户模板'
                            },
                        },
                        {
                            path: 'public',
                            lazy: () => import('@/pages/admin/product/sms/template/public'),
                            meta: {
                                title: '公共模板'
                            },
                        },
                    ]
                },
            ]
        },
        {
            path: 'log',
            lazy: () => import('@/pages/admin/product/sms/log'),
            meta: {
                title: '发送记录'
            }
        },
        {
            path: 'reply',
            lazy: () => import('@/pages/admin/product/sms/reply'),
            meta: {
                title: '回复记录'
            }
        },
        {
            path: 'package',
            lazy: () => import('@/pages/admin/product/sms/package'),
            meta: {
                title: '套餐管理'
            }
        },
        {
            path: 'user',
            lazy: () => import('@/pages/admin/product/sms/user'),
            meta: {
                title: '用户管理'
            }
        }
    ]
};

export default sms;
