import { Navigate, RouteObject, TabLayout } from '@topthink/common';

const finance: RouteObject[] = [
    {
        path: 'charge',
        lazy: () => import('@/pages/admin/finance/charge'),
        meta: {
            title: '交易管理'
        },
    },
    {
        path: 'refund',
        lazy: () => import('@/pages/admin/finance/refund'),
        meta: {
            title: '退款管理'
        },
    },
    {
        path: 'invoice',
        element: <TabLayout />,
        meta: {
            title: '发票管理',
            hideChildrenInMenu: true,
        },
        children: [
            {
                index: true,
                element: <Navigate to='pending' replace />
            },
            {
                path: 'pending',
                lazy: () => import('@/pages/admin/finance/invoice/pending'),
                meta: {
                    title: '待开票'
                }
            },
            {
                path: 'issued',
                lazy: () => import('@/pages/admin/finance/invoice/issued'),
                meta: {
                    title: '已开票'
                }
            }
        ]
    }
];

export default finance;
