import { Navigate, RouteObject } from '@topthink/common';

export const sales: RouteObject = {
    path: 'sales',
    meta: {
        title: '营销',
        icon: 'person-arms-up'
    },
    children: [
        {
            index: true,
            element: <Navigate to='promo' replace />
        },
        {
            path: 'promo',
            meta: {
                title: '优惠码'
            },
            lazy: () => import('@/pages/admin/sales/promo')
        },
        {
            path: 'partner',
            meta: {
                title: '合作伙伴'
            },
            lazy: () => import('@/pages/admin/sales/partner')
        }
    ]
};

export default sales;
