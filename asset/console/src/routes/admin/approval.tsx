import { RouteObject } from '@topthink/common';

const approval: RouteObject[] = [
    {
        path: 'enterprise',
        lazy: () => import('@/pages/admin/approval/enterprise'),
        meta: {
            title: '企业认证'
        },
    },
    {
        path: 'invoice',
        lazy: () => import('@/pages/admin/approval/invoice'),
        meta: {
            title: '发票申请'
        },
    },
    {
        path: 'withdraw/money',
        lazy: () => import('@/pages/admin/approval/money-withdraw'),
        meta: {
            title: '余额提现'
        }
    },
    {
        path: 'withdraw/coin',
        lazy: () => import('@/pages/admin/approval/coin-withdraw'),
        meta: {
            title: '云币提现'
        }
    },
    {
        path: 'settlement',
        lazy: () => import('@/pages/admin/approval/settlement'),
        meta: {
            title: '佣金结算'
        }
    },
    {
        path: 'sms/sign',
        lazy: () => import('@/pages/admin/approval/sms-sign'),
        meta: {
            title: '短信签名'
        }
    }
];

export default approval;
