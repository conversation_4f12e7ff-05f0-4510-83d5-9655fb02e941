import { Access, Header, Link, Outlet, useLoaderData, useUser } from '@topthink/common';
import { useEffect, useMemo, useRef } from 'react';
import { Dropdown, NavLink } from 'react-bootstrap';
import loadjs from 'loadjs';

export default function BasicLayout() {

    const { scripts } = useLoaderData() as Manifest;

    const menus = useMemo(() => {
        return <>
            <Dropdown.Item as={Link} to='/user'>
                个人中心
            </Dropdown.Item>
            <Dropdown.Item as={Link} to='/partner'>
                合作伙伴
            </Dropdown.Item>
            <Dropdown.Divider />
            <Dropdown.Item as={Link} to='/logout'>退出登录</Dropdown.Item>
        </>;
    }, []);

    const ref = useRef(null);
    const [user] = useUser();

    useEffect(() => {
        //自定义脚本
        const fragment = document.createRange().createContextualFragment(scripts);
        document.body.appendChild(fragment);

        //加载通知服务
        if (user.notification) {
            loadjs(user.notification.url, () => {
                window.tnf.init(ref.current, user.notification.token);
            });
        }
    }, []);

    return <>
        <Header menus={menus}>
            <Link className='navbar-brand' to='/'>
                控制台
            </Link>
            <ul className='navbar-nav me-auto'>
                <Dropdown className='nav-item d-none d-lg-block'>
                    <Dropdown.Toggle as={NavLink}>产品</Dropdown.Toggle>
                    <Dropdown.Menu className='shadow'>
                        <Dropdown.Item as={Link} to='/api'>
                            🍀 API 服务
                        </Dropdown.Item>
                        <Dropdown.Item as={Link} to='/sms'>
                            📟 短信服务
                        </Dropdown.Item>
                        <Dropdown.Item as={Link} to='/ssl'>
                            🔒 SSL 证书
                        </Dropdown.Item>
                        <Dropdown.Item as={Link} to='/assistant'>
                            📌 运营助理
                        </Dropdown.Item>
                        <Dropdown.Item as={Link} to='/ai'>
                            💎 ThinkAI
                        </Dropdown.Item>
                    </Dropdown.Menu>
                </Dropdown>
            </ul>
            <ul className='navbar-nav gap-2 d-none d-lg-flex'>
                <li className='nav-item'>
                    <Link className='nav-link' to='https://console.topthink.com/partner/promo/dashboard'>
                        🤝 合作
                    </Link>
                </li>
                <li className='nav-item'>
                    <Link className='nav-link' to='https://chat.topthink.com/'>
                        🚀 AI助理
                    </Link>
                </li>                
                <li className='nav-item'>
                    <Link className='nav-link' to='https://doc.topthink.com'>
                        📖 文档
                    </Link>
                </li>
            </ul>
            <ul className='navbar-nav gap-2 me-3 flex-row'>
                <li className='nav-item'>
                    <Link className='nav-link px-2' to='/finance'>
                        💰 费用
                    </Link>
                </li>
                <li className='nav-item d-none d-md-block'>
                    <a className='nav-link px-2' role='button' data-notification='' ref={ref}>
                        <span className='position-relative'>
                            <i className='bi bi-bell'></i>
                            <span data-badge='' hidden className='position-absolute top-0 start-100 translate-middle p-1 bg-danger border border-light rounded-circle'>
                                <span className='visually-hidden'>New alerts</span>
                            </span>
                        </span>
                    </a>
                </li>
                <Access require='admin'>
                    <li className='nav-item'>
                        <Link className='nav-link px-2' to='/admin'>
                            <i className='bi bi-wrench' />
                        </Link>
                    </li>
                </Access>
            </ul>
        </Header>
        <Outlet />
    </>;
}
