import {
    Card,
    Content,
    isRequestError,
    Loader,
    Message,
    request,
    RequestButton,
    useLocation,
    useNavigate,
    useRequest
} from '@topthink/common';
import { ReactComponent as Weixin } from '../../images/weixin.svg';
import { ReactComponent as Qq } from '../../images/qq.svg';
import { ReactComponent as Github } from '../../images/github.svg';
import openWin from '@/utils/open-win';
import { MouseEvent, useEffect, useState } from 'react';
import { Button } from 'react-bootstrap';

interface UserSocial {
    channel: string;
}

const BindButton = ({ type }: { type: string }) => {
    const navigate = useNavigate();
    const [binding, setBinding] = useState(false);

    useEffect(() => {
        const handler = async (ev: MessageEvent) => {
            if (ev.data.source === 'social') {
                const { channel, code } = ev.data.payload;
                if (channel === type) {
                    try {
                        await request({
                            url: `/user/social`,
                            method: 'post',
                            data: { channel, code }
                        });
                        navigate('/user/social', { replace: true });
                    } catch (e: any) {
                        if (isRequestError(e)) {
                            let errors = e.errors;
                            if (typeof e.errors !== 'string') {
                                errors = Object.values(e.errors).join('<br />');
                            }
                            Message.error({ text: errors as string });
                        } else {
                            Message.error(e.message);
                        }
                    } finally {
                        setBinding(false);
                    }
                }
            }
        };
        window.addEventListener('message', handler);
        return () => {
            window.removeEventListener('message', handler);
        };
    }, []);

    const handleClick = async (e: MouseEvent) => {
        e.preventDefault();

        setBinding(true);
        const { url } = await request(`/user/social/${type}`);

        openWin(url, {
            name: 'social',
            specs: {
                width: 650,
                height: 500,
                location: 'no',
                status: 'no',
                scrollbars: 'no',
                resizable: 'no',
            }
        });
    };

    return <Button disabled={binding} onClick={handleClick} variant={'primary'}>绑定</Button>;
};

export const Component = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const { result } = useRequest<UserSocial[]>('/user/social', {
        refreshDeps: [location.key],
    });

    const isBinding = (channel: string) => {
        return result && result.filter(item => item.channel === channel).length > 0;
    };

    const list = [
        {
            channel: 'wechat',
            icon: <Weixin className='me-3' width={40} height={40} />,
            name: '微信'
        },
        {
            channel: 'qq',
            icon: <Qq className='me-3' width={40} height={40} />,
            name: 'QQ'
        },
        {
            channel: 'github',
            icon: <Github className='me-3' width={40} height={40} />,
            name: 'Github'
        }
    ];

    if (!result) {
        return <Loader />;
    }

    return <Content title='账号绑定'>
        <Card>
            <ul className='list-group list-group-flush'>
                {list.map((item) => {
                    return <li key={item.channel} className='list-group-item d-flex align-items-center py-3'>
                        {item.icon}
                        <div className='flex-fill'>
                            <div className='fw-bold'>{item.name}</div>
                            <div className='text-muted'>绑定{item.name}帐号后，您便可使用{item.name}帐号登录网站</div>
                        </div>
                        {isBinding(item.channel) ?
                            <RequestButton
                                variant={'secondary'}
                                confirm='确定要解除绑定吗？'
                                url={`/user/social/${item.channel}`}
                                method={'delete'}
                                onSuccess={() => {
                                    navigate('/user/social', { replace: true });
                                }}
                            >解除绑定</RequestButton> : <BindButton type={item.channel} />}
                    </li>;
                })}
            </ul>
        </Card>
    </Content>;
};
