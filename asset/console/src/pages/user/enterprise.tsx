import {
    Card,
    Content,
    Form,
    FormSchema,
    FormUiSchema,
    Loader,
    Result,
    useLocation,
    useNavigate,
    useRequest
} from '@topthink/common';
import { useCallback } from 'react';
import { Alert } from 'react-bootstrap';

function CreateEnterprise({ formData }: any) {
    const schema: FormSchema = {
        type: 'object',
        properties: {
            corporation: {
                title: '企业法人',
                type: 'string',
            },
            name: {
                title: '公司名称',
                type: 'string',
            },
            identity: {
                title: '注册号/统一社会信用代码',
                type: 'string',
            },
            img: {
                title: '营业执照',
                type: 'string',
            }
        }
    };

    const uiSchema: FormUiSchema = {
        img: {
            'ui:widget': 'upload',
            'ui:options': {
                endpoint: '/upload/enterprise',
                accept: 'image/*'
            }
        }
    };

    const navigate = useNavigate();

    const handleSuccess = useCallback(() => {
        navigate('/user/enterprise', { replace: true });
    }, [navigate]);

    return <Form
        method='post'
        action='/user/enterprise'
        onSuccess={handleSuccess}
        formData={formData}
        schema={schema}
        uiSchema={uiSchema}
    />;
}

export const Component = () => {
    const location = useLocation();

    const { result, loading, error } = useRequest<UserEnterprise>('/user/enterprise', {
        refreshDeps: [location.key]
    });

    if (loading) {
        return <Loader />;
    }

    let children;
    if (error) {
        children = <Result
            status='error'
            title={error.message}
        />;
    } else if (result?.status === 1) {
        children = <>
            <Alert variant='success'>您已完成企业认证</Alert>
            <table className='table table-borderless mb-0'>
                <tbody>
                <tr>
                    <td width='100' className='text-secondary'>公司名称 :</td>
                    <td>{result.name}</td>
                    <td width='150' className='text-secondary'>社会信用代码 :</td>
                    <td>{result.identity}</td>
                </tr>
                <tr>
                    <td width='100' className='text-secondary'>企业法人 :</td>
                    <td>{result.corporation}</td>
                    <td width='150' className='text-secondary'>认证时间 :</td>
                    <td>{result.update_time}</td>
                </tr>
                </tbody>
            </table>
        </>;
    } else if (result?.status === 0) {
        children = <Result
            status='info'
            title='审核中'
        />;
    } else {
        children = <>
            {result && <Alert variant='warning'>未通过审核，请检查</Alert>}
            <CreateEnterprise formData={result} />
        </>;
    }

    return <Content title='企业认证'>
        <Card>
            {children}
        </Card>
    </Content>;
};
