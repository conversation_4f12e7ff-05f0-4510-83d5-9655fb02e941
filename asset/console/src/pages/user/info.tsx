import { Card, Content, useUser } from '@topthink/common';
import EmailModal from '@/pages/user/email-modal';
import { useCallback } from 'react';
import NameModal from '@/pages/user/name-modal';
import Avatar from '@/pages/user/avatar';
import MobileModal from '@/pages/user/mobile-modal';
import { Col, Row } from 'react-bootstrap';

export const Component = () => {

    const [user, setUser] = useUser();

    const onSuccess = useCallback((result: any) => {
        setUser(result);
    }, [setUser]);

    return <Content title='基本信息'>
        <Card>
            <Row>
                <Col xs={12} md={2}>
                    <Avatar onSuccess={onSuccess} src={user.avatar} />
                </Col>
                <Col xs={12} md={10}>
                    <table className='table table-borderless mb-0'>
                        <tbody>
                        <tr>
                            <td width='100'>昵称 :</td>
                            <td width='200'>{user.name}</td>
                            <td>
                                <NameModal onSuccess={onSuccess} />
                            </td>
                        </tr>
                        <tr>
                            <td>手机 :</td>
                            <td>{user.mobile}</td>
                            <td>
                                <MobileModal onSuccess={onSuccess} />
                            </td>
                        </tr>
                        <tr>
                            <td>邮箱 :</td>
                            <td>{user.email || '--'}</td>
                            <td>
                                <EmailModal onSuccess={onSuccess} />
                            </td>
                        </tr>
                        <tr>
                            <td>实名认证 :</td>
                            <td>{user.is_certified ? '是' : '否'}</td>
                        </tr>
                        </tbody>
                    </table>
                </Col>
            </Row>
        </Card>
    </Content>;
};
