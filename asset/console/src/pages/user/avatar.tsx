import { request, styled, useOverlayState } from '@topthink/common';
import { ChangeEvent, useCallback, useRef, useState } from 'react';
import { Button, Modal } from 'react-bootstrap';
import <PERSON>ropper from 'react-easy-crop';
import { Area } from 'react-easy-crop/types';
import { CropperProps } from 'react-easy-crop/Cropper';

interface Props {
    src: string;
    onSuccess: (user: object) => void;
}


export default function Avatar({ src, onSuccess }: Props) {

    const { show, hide, state } = useOverlayState();
    const [saving, setSaving] = useState(false);
    const fileRef = useRef<HTMLInputElement>(null);
    const [url, setUrl] = useState<string>();
    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>();

    const onCropComplete = useCallback<Required<CropperProps>['onCropComplete']>((_, croppedAreaPixels) => {
        setCroppedAreaPixels(croppedAreaPixels);
    }, []);

    const handleFileChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        e.preventDefault();

        const { files } = e.target;
        if (files && files.length > 0) {
            setZoom(1);
            setCrop({ x: 0, y: 0 });
            setUrl(URL.createObjectURL(files[0]));
            show();
        }
        e.target.value = '';
    }, []);

    const saveImage = useCallback(async () => {
        if (croppedAreaPixels && url) {
            try {
                setSaving(true);
                const image = await createImage(url);
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (ctx) {
                    const scaleX = 200 / croppedAreaPixels.width;
                    const scaleY = 200 / croppedAreaPixels.height;

                    canvas.width = image.width * scaleX;
                    canvas.height = image.height * scaleY;
                    ctx.scale(scaleX, scaleY);
                    ctx.drawImage(image, 0, 0);

                    const data = ctx.getImageData(
                        croppedAreaPixels.x * scaleX,
                        croppedAreaPixels.y * scaleY,
                        200,
                        200
                    );

                    canvas.width = 200;
                    canvas.height = 200;

                    ctx.putImageData(data, 0, 0);

                    canvas.toBlob(async (blob) => {
                        const data = new FormData();

                        data.set('avatar', new File([blob!], 'avatar.png'));

                        const result = await request({ url: '/user/avatar', method: 'post', data });
                        onSuccess(result);
                        hide();
                    }, 'image/png');
                }
            } finally {
                setSaving(false);
            }
        }


    }, [croppedAreaPixels]);

    return <Container>
        <ImageContainer>
            <img src={src} />
            <input onChange={handleFileChange} type='file' ref={fileRef} accept='image/*' />
            <EditButton onClick={() => fileRef.current?.click()}>修改</EditButton>
            <Modal {...state} backdrop='static'>
                <Modal.Header>裁剪头像</Modal.Header>
                <Modal.Body>
                    <CropperContainer>
                        <Cropper
                            image={url}
                            crop={crop}
                            cropShape={'round'}
                            aspect={1}
                            zoom={zoom}
                            showGrid={false}
                            onZoomChange={setZoom}
                            onCropChange={setCrop}
                            onCropComplete={onCropComplete}
                        />
                    </CropperContainer>
                </Modal.Body>
                <Modal.Footer>
                    <Button onClick={hide} variant={'secondary'}>取消</Button>
                    <Button disabled={saving} onClick={saveImage}>保存</Button>
                </Modal.Footer>
            </Modal>
        </ImageContainer>
    </Container>;
}

const createImage = (url: string): Promise<HTMLImageElement> =>
    new Promise((resolve, reject) => {
        const image = new Image();
        image.addEventListener('load', () => resolve(image));
        image.addEventListener('error', (error) => reject(error));
        image.setAttribute('crossOrigin', 'anonymous'); // needed to avoid cross-origin issues on CodeSandbox
        image.src = url;
    });

const CropperContainer = styled.div`
    height: 300px;
`;

const EditButton = styled.div`
    width: 100%;
    height: 31px;
    opacity: 0.75;
    background: rgb(24, 24, 24);
    position: absolute;
    bottom: 0px;
    font-size: 12px;
    color: rgb(255, 255, 255);
    text-align: center;
    line-height: 31px;
    cursor: pointer;
`;

const Container = styled.div`
    padding: 1rem;
    margin-right: 1rem;
`;

const ImageContainer = styled.div`
    border-radius: 50%;
    height: 116px;
    width: 116px;
    overflow: hidden;
    position: relative;

    img {
        width: 100%;
        height: 100%;
    }
`;
