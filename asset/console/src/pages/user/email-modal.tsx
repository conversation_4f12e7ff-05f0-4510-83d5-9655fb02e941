import { FormSchema, FormUiSchema, FormWidgetProps, ModalForm, ModalFormProps } from '@topthink/common';
import { createElement } from 'react';
import { InputGroup } from 'react-bootstrap';
import CodeButton from '@/components/code-button';

export default function EmailModal({ onSuccess }: Pick<ModalFormProps, 'onSuccess'>) {

    const schema: FormSchema = {
        type: 'object',
        required: [
            'email',
            'code',
        ],
        properties: {
            email: {
                title: '邮箱',
                type: 'string',
            },
            code: {
                title: '验证码',
                type: 'string'
            },
        }
    };

    const uiSchema: FormUiSchema = {
        email: {
            'ui:widget': 'email',
            'ui:placeholder': '邮箱',
            'ui:options': {
                label: false
            }
        },
        code: {
            'ui:widget': (props: FormWidgetProps) => {
                return <InputGroup>
                    {createElement(props.registry.widgets.TextWidget, props)}
                    <CodeButton method={'post'} url={{
                        url: '/user/email/code',
                        transformRequest: () => {
                            const emailInput = document.getElementById('root_email') as HTMLInputElement;

                            const data = new FormData();

                            data.set('email', emailInput.value);

                            return data;
                        }
                    }} />
                </InputGroup>;
            },
            'ui:placeholder': '验证码',
            'ui:options': {
                label: false,
            }
        }
    };

    return <ModalForm
        buttonProps={{
            variant: 'link'
        }}
        action={`/user/email`}
        method={'post'}
        schema={schema}
        uiSchema={uiSchema}
        text={'修改邮箱'}
        onSuccess={onSuccess}
    />;
}
