import { FormSchema, FormUiSchema, FormWidgetProps, ModalForm, ModalFormProps } from '@topthink/common';
import { InputGroup } from 'react-bootstrap';
import { createElement } from 'react';
import CodeButton from '@/components/code-button';

export default function MobileModal({ onSuccess }: Pick<ModalFormProps, 'onSuccess'>) {
    const schema: FormSchema = {
        type: 'object',
        required: [
            'mobile',
            'code',
        ],
        properties: {
            mobile: {
                title: '手机',
                type: 'string',
            },
            code: {
                title: '验证码',
                type: 'string'
            },
        }
    };

    const uiSchema: FormUiSchema = {
        mobile: {
            'ui:placeholder': '手机',
            'ui:options': {
                label: false
            }
        },
        code: {
            'ui:widget': (props: FormWidgetProps) => {
                return <InputGroup>
                    {createElement(props.registry.widgets.TextWidget, props)}
                    <CodeButton method={'post'} url={{
                        url: '/user/mobile/code',
                        transformRequest: () => {
                            const mobileInput = document.getElementById('root_mobile') as HTMLInputElement;

                            const data = new FormData();

                            data.set('mobile', mobileInput.value);

                            return data;
                        }
                    }} />
                </InputGroup>;
            },
            'ui:placeholder': '验证码',
            'ui:options': {
                label: false,
            }
        }
    };

    return <ModalForm
        buttonProps={{
            variant: 'link',
        }}
        action={`/user/mobile`}
        method={'post'}
        schema={schema}
        uiSchema={uiSchema}
        text={'修改手机'}
        onSuccess={onSuccess}
    />;
}
