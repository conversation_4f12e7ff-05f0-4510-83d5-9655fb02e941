import {
    Card,
    Content,
    Form,
    FormSchema,
    Loader,
    Message,
    request,
    Result,
    useLocation,
    useNavigate,
    useRequest
} from '@topthink/common';
import { useCallback } from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Alert, Button } from 'react-bootstrap';
import { maskString } from 'mask-sensitive-data';

function CreateCertification() {

    const schema: FormSchema = {
        type: 'object',
        properties: {
            name: {
                title: '真实姓名',
                type: 'string',
            },
            identity: {
                title: '身份证号',
                type: 'string',
            }
        }
    };

    const navigate = useNavigate();

    const handleSuccess = useCallback(() => {
        navigate('/user/certification', { replace: true });
    }, [navigate]);

    return <Form
        method='post'
        action='/user/certification'
        onSuccess={handleSuccess}
        schema={schema}
    />;
}

function DealCertification({ certification }: { certification: UserCertification }) {
    let icon, title;
    if (certification.failed_reason) {
        icon = <i className='bi bi-exclamation-circle-fill text-danger' />;
        title = `${certification.failed_reason}，请重新认证`;
    } else if (certification.certify_url) {
        icon = <QRCodeSVG value={certification.certify_url || ''} size={200} />;
        title = '使用支付宝扫描二维码完成认证';
    } else {
        icon = <i className='bi bi-exclamation-circle-fill text-danger' />;
        title = '生成二维码失败，请重新认证';
    }

    const navigate = useNavigate();

    const { execute } = useRequest({
        url: '/user/certification',
        method: 'delete'
    }, {
        manual: true,
        onSuccess() {
            navigate('/user/certification', { replace: true });
        }
    });

    const handleClick = useCallback(async () => {
        if (await Message.confirm({
            text: '确定要重新认证吗？',
        })) {
            await execute();
        }
    }, [certification]);

    return <Result
        icon={icon}
        title={title}
        extra={<Button onClick={handleClick} variant='primary'>重新认证</Button>}
    />;
}

export const Component = () => {
    const location = useLocation();

    const navigate = useNavigate();

    const { result, loading } = useRequest<UserCertification>('/user/certification', {
        refreshDeps: [location.key],
        async onSuccess(certification) {
            if (certification && !certification.passed) {
                //TODO 页面跳转后停止检查
                await request('/user/certification/check');
                navigate('/user/certification', { replace: true });
            }
        }
    });

    if (loading) {
        return <Loader />;
    }

    let children;

    if (result) {
        if (!result.passed) {
            children = <DealCertification certification={result} />;
        } else {
            children = <>
                <Alert variant='success'>您已完成个人实名认证</Alert>
                <table className='table table-borderless mb-0'>
                    <tbody>
                    <tr>
                        <td width='100' className='text-secondary'>真实姓名 :</td>
                        <td>
                            {maskString(result.name, {
                                maskSymbol: '*',
                                maxCharsToMask: result.name.length,
                                visibleCharsFromStart: 0,
                                visibleCharsFromEnd: 1,
                            })}
                        </td>
                        <td width='100' className='text-secondary'>身份证号 :</td>
                        <td>
                            {maskString(result.identity, {
                                maskSymbol: '*',
                                maxCharsToMask: result.identity.length,
                                visibleCharsFromStart: 4,
                                visibleCharsFromEnd: 4,
                            })}
                        </td>
                    </tr>
                    <tr>
                        <td width='100' className='text-secondary'>认证类型 :</td>
                        <td>身份证</td>
                        <td width='100' className='text-secondary'>认证时间 :</td>
                        <td>{result.update_time}</td>
                    </tr>
                    </tbody>
                </table>
            </>;
        }
    } else {
        children = <CreateCertification />;
    }

    return <Content title='实名认证'>
        <Card>
            {children}
        </Card>
    </Content>;
};
