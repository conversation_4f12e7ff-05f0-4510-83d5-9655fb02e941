import { Columns, Content, Message, ModalForm, RequestButton, Table, Toast } from '@topthink/common';
import copy from 'copy-to-clipboard';

export const Component = () => {

    const columns: Columns<AccessToken> = [
        {
            title: '名称',
            dataIndex: 'name'
        },
        {
            title: '授权范围',
            dataIndex: 'scopes',
            render({ value }) {
                return value.map((scope: string) => {
                    switch (scope) {
                        case 'api':
                            return 'API服务';
                        case 'sms':
                            return '短信服务';
                        case 'ai':
                            return 'ThinkAI';
                        default:
                            return scope;
                    }
                }).join('、');
            }
        },
        {
            title: '创建于',
            dataIndex: 'create_time'
        },
        {
            title: '最近使用',
            dataIndex: 'last_time',
            render({ value }) {
                return value || '--';
            }
        },
        {
            title: '到期',
            dataIndex: 'expire_time',
            render({ value }) {
                return value || '--';
            }
        },
        {
            title: '操作',
            key: 'action',
            align: 'right',
            render({ record, action }) {
                return <RequestButton
                    variant={'link'}
                    method={'delete'}
                    confirm={'确定要删除吗？'}
                    url={`/user/token/${record.id}`}
                    onSuccess={action.reload}
                >删除</RequestButton>;
            }
        }
    ];

    return <Content title='令牌管理'>
        <Table
            source={`/user/token`}
            toolBarRender={(table) => {
                return <ModalForm
                    text={'创建令牌'}
                    method={'post'}
                    action={`/user/token`}
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                title: '名称'
                            },
                            scopes: {
                                title: '授权范围',
                                type: 'array',
                                items: {
                                    type: 'string',
                                    enum: [
                                        'api',
                                        'sms',
                                        'ai'
                                    ]
                                },
                                uniqueItems: true
                            },
                            expire_time: {
                                type: 'string',
                                title: '到期时间'
                            }
                        }
                    }}
                    uiSchema={{
                        scopes: {
                            'ui:widget': 'checkboxes',
                            'ui:enumNames': [
                                'API服务',
                                '短信服务',
                                '启智AI'
                            ],
                            'ui:enumDescriptions': [
                                '授予API服务的相关权限',
                                '授予短信服务的相关权限',
                                '授予启智AI的相关权限',
                            ]
                        },
                        expire_time: {
                            'ui:widget': 'datetime',
                            'ui:description': '留空则永久有效',
                        }
                    }}
                    onSuccess={(result) => {
                        table.reload();
                        Message.success({
                            title: 'API令牌创建成功',
                            html: `<p><code>${result.token}</code></p><p class='text-muted'>请确保妥善保存它 - 您无法再次访问它的内容。</p>`,
                            timer: undefined,
                            showConfirmButton: true,
                            toast: false,
                            position: 'center',
                            showCancelButton: true,
                            allowOutsideClick: false,
                            confirmButtonText: '复制令牌',
                            cancelButtonText: '关闭',
                            preConfirm() {
                                copy(result.token);
                                Toast.success('邀请链接已复制至剪贴板');
                                return false;
                            }
                        });
                    }}
                />;
            }}
            columns={columns}
        />
    </Content>;
};
