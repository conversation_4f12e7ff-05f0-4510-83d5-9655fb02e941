import { Loader, Result, styled, useRequest } from '@topthink/common';
import ApiItem from './item';

export default function ApiList({ category, keyword }: {
    category?: number;
    keyword?: string;
}) {

    const { result: apis } = useRequest<Api[]>(
        {
            url: `/api/apply/list`,
            params: { category, keyword }
        },
        {
            refreshDeps: [category, keyword]
        }
    );

    if (!apis) return <Loader wrap />;

    if (apis.length === 0) return <Result status={'warning'} title='没有找到相关API' />;

    return <ApiContainer>
        {apis.map((item) => <ApiItem key={item.id} item={item} />)}
    </ApiContainer>;
}

const ApiContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 1rem;
`;
