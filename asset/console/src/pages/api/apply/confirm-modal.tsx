import { <PERSON><PERSON>, Close<PERSON>utton, Modal, <PERSON>, Button } from 'react-bootstrap';
import { RequestButton, Space, styled, useNavigate } from '@topthink/common';
import { useContext, useState } from 'react';
import { Context } from '@/pages/api/apply/index';

export default function ConfirmModal({ api }: { api: Api }) {

    const [, setCurrent] = useContext(Context);
    const navigate = useNavigate();

    const [show, setShow] = useState(true);

    const [confirmed, setConfirmed] = useState(false);

    return <Modal backdrop={'static'} show={show} onHide={() => setShow(false)} onExited={() => setCurrent(undefined)}>
        <Modal.Body className={'p-4'}>
            <Container>
                <ApiItemIcon><img src={api.logo} /></ApiItemIcon>
                <ApiItemRight>
                    <ApiItemName href={api.url} target={'_blank'}>{api.title}</ApiItemName>
                    <ApiItemDesc>
                        {api.price_type === 0 && <Badge bg={'success'}>免费</Badge>}
                        {api.price_type === 1 && <Badge bg={'orange'}>会员</Badge>}
                        {api.price_type === 2 && <Badge bg={'danger'}>付费</Badge>}
                        {api.identify_type === 1 && <Badge bg={'info'}>个人认证</Badge>}
                        {api.identify_type === 2 && <Badge bg={'info'}>企业认证</Badge>}
                        {api.price_type !== 2 && <span className='text-muted'>根据会员等级，调用频率限制不同</span>}
                        {api.price_type === 2 && <span className='text-muted'>申请赠送：{api.trial_nums}次</span>}
                    </ApiItemDesc>
                </ApiItemRight>
            </Container>
            <Form.Check
                id={'api-apply-confirm'}
                checked={confirmed}
                onChange={e => setConfirmed(e.target.checked)}
                className='mb-3'
                label={<>我已阅读并同意<a className='link-primary' href='https://doc.topthink.com/think-api/license.html' target='_blank'>《API服务协议》</a></>}
            />
            {api.applied ? <Button variant={'secondary'} disabled>已申请</Button> : <RequestButton
                url={`/api/apply/${api.id}`}
                method={'post'}
                onSuccess={() => {
                    requestAnimationFrame(() => navigate('/api/list'));
                }}
                disabled={!confirmed}
                tooltip={!confirmed ? '请先勾选相关服务协议才能购买' : undefined}
            >立即申请</RequestButton>}

        </Modal.Body>
        <StyledCloseButton onClick={() => setShow(false)} />
    </Modal>;
}

const ApiItemDesc = styled(Space)`
  height: 32px;
  align-items: center;
  display: flex;
`;

const ApiItemName = styled.a`
  font-size: 18px;
  line-height: 25px;
  color: #262626;
  margin-bottom: 4px;
`;

const ApiItemRight = styled.div`
  padding-left: 12px;
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const ApiItemIcon = styled.div`
  width: 60px;
  height: 60px;

  img {
    width: 100%;
    height: 100%;
  }
`;

const Container = styled.div`
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  margin-bottom: 20px;
`;

const StyledCloseButton = styled(CloseButton)`
  position: absolute;
  right: 1rem;
  top: 1rem;
`;
