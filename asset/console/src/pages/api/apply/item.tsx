import { css, Space, styled } from '@topthink/common';
import { useContext, useRef } from 'react';
import useHover from '@react-hook/hover';
import { Badge, Button } from 'react-bootstrap';
import { Context } from '@/pages/api/apply/index';

export default function ApiItem({ item }: { item: Api }) {
    const target = useRef(null);

    const isHovering = useHover(target);

    const [, setCurrent] = useContext(Context);

    return <ApiItemContainer ref={target} $applied={item.applied}>
        <ApiItemInner>
            <ApiItemTop>
                <ApiItemTopLeft>
                    <ApiItemIcon><img src={item.logo} /></ApiItemIcon>
                    <ApiItemName href={item.url} target={'_blank'}>{item.title}</ApiItemName>
                    {item.applied && !isHovering && <Badge bg={'secondary'}>已申请</Badge>}
                </ApiItemTopLeft>
                {isHovering && (item.applied ?
                    <Button
                        className={'px-3'}
                        disabled
                        variant={'secondary'}
                        size={'sm'}>已申请</Button> :
                    <Button
                        onClick={() => setCurrent(item)}
                        className={'px-4'}
                        variant={'primary'}
                        size={'sm'}
                    >申请</Button>)}
            </ApiItemTop>
            {isHovering && <ApiItemBottom>
                {item.price_type === 0 && <Badge bg={'success'}>免费</Badge>}
                {item.price_type === 1 && <Badge bg={'orange'}>会员</Badge>}
                {item.price_type === 2 && <Badge bg={'danger'}>付费</Badge>}
                {item.identify_type === 1 && <Badge bg={'info'}>个人认证</Badge>}
                {item.identify_type === 2 && <Badge bg={'info'}>企业认证</Badge>}
                {item.price_type !== 2 && <ApiItemDesc>根据会员等级，调用频率限制不同</ApiItemDesc>}
                {item.price_type === 2 && <ApiItemDesc>申请赠送：{item.trial_nums}次</ApiItemDesc>}
            </ApiItemBottom>}
        </ApiItemInner>
    </ApiItemContainer>;
}

const ApiItemDesc = styled.div`
  color: #888888;
`;

const ApiItemBottom = styled(Space)`
  height: 32px;
  background: rgba(var(--bs-primary-rgb), .1);
  border-radius: 4px;
  margin-top: 10px;
  box-sizing: border-box;
  border: 1px solid rgba(var(--bs-primary-rgb), .2);
  align-items: center;
  padding: 0 8px;
  display: flex;
`;

const ApiItemIcon = styled.div`
  width: 30px;
  height: 30px;
  margin: 0 10px 0 0;

  img {
    width: 100%;
    height: 100%;
  }
`;

const ApiItemName = styled.a`
  font-size: 14px;
  max-width: calc(100% - 40px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 8px;
  flex-shrink: 0;
  line-height: 1.4;
  display: block;
`;

const ApiItemTopLeft = styled.div`
  display: flex;
  align-items: center;
`;

const ApiItemTop = styled.div`
  height: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ApiItemInner = styled.div`
  position: absolute;
  left: 0;
  right: 0;
  padding: 8px 16px 9px 24px;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 4px;
`;

const ApiItemContainer = styled.div<{ $applied: boolean }>`
  width: 50%;
  height: 50px;
  cursor: pointer;
  margin-bottom: 23px;
  position: relative;

  ${(props) => props.$applied && css`
    ${ApiItemTopLeft} {
      opacity: .5;
    }
  `}
  &:hover {
    ${ApiItemTopLeft} {
      opacity: 1;
    }

    ${ApiItemInner} {
      border: 1px solid var(--bs-primary);
      height: 90px;
      background: #fff;
      z-index: 1;
      box-shadow: 0 7px 15px 0 rgba(0, 38, 51, .1);
    }
  }
`;
