import { Card, Content, request, styled, useAsync, useParams, useRequest } from '@topthink/common';
import { createContext, FormEvent, useCallback, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import classNames from 'classnames';
import ApiList from './list';
import ConfirmModal from './confirm-modal';

export const Context = createContext<[Api | undefined, (api: Api | undefined) => void]>([
    undefined,
    () => {
    }
]);

export const Component = function() {

    const { id } = useParams();

    const [current, setCurrent] = useState<Api>();

    useAsync(async () => {
        if (id) {
            return await request(`/api/apply/${id}`);
        }
    }, [], {
        onSuccess(result) {
            setCurrent(result);
        }
    });


    const [category, setCategory] = useState<number>();
    const [keyword, setKeyword] = useState<string>();

    const { result: categories = [] } = useRequest<ApiCategory[]>(`/api/apply/category`);

    const onSearch = useCallback((e: FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setKeyword(e.currentTarget.keyword.value);
        setCategory(undefined);
    }, [setKeyword]);

    return <Context.Provider value={[current, setCurrent]}>
        <Content title={'申请API'}>
            <Card>
                <SearchContainer onSubmit={onSearch}>
                    <input className='form-control' name='keyword' placeholder='请输入想要检索的API' type='search' />
                </SearchContainer>
                <Row>
                    <Col md={2} className='border-end'>
                        <nav className='nav flex-column nav-pills'>
                            <a
                                className={classNames('nav-link', { active: !category && !keyword })}
                                role='button'
                                onClick={() => {
                                    setCategory(undefined);
                                    setKeyword(undefined);
                                }}
                            >精选推荐</a>
                            {categories.map((item) =>
                                <a
                                    key={item.id}
                                    className={classNames('nav-link', { active: category === item.id })}
                                    role='button'
                                    onClick={() => {
                                        setCategory(item.id);
                                        setKeyword(undefined);
                                    }}
                                >{item.name}</a>)}
                        </nav>
                    </Col>
                    <Col md={10} className='position-relative'>
                        <ApiList category={category} keyword={keyword} />
                    </Col>
                </Row>
            </Card>
        </Content>
        {current && <ConfirmModal api={current} />}
    </Context.Provider>;
}

const SearchContainer = styled.form`
    margin-bottom: 1rem;
`;
