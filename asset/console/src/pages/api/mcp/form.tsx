import { Card, Form, FormProps } from '@topthink/common';

export default function McpForm(props: Partial<FormProps>) {
    return <Card>
        <Form
            {...props}
            schema={{
                type: 'object',
                properties: {
                    name: {
                        title: '名称',
                        type: 'string',
                    },
                    apis: {
                        title: '关联API',
                        type: 'array',
                        items: {
                            type: 'number'
                        }
                    }
                }
            }}
            uiSchema={{
                apis: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        endpoint: '/api/search',
                        minLength: 0,
                        multiple: true,
                        filter: true
                    }
                }
            }}
        />
    </Card>;
}
