import { Content, LinkButton, RequestButton, Space, Table, Toast, useClipboard } from '@topthink/common';

export const Component = function() {
    const { copy } = useClipboard();

    return <Content>
        <Table
            toolBarRender={() => {
                return <LinkButton to={'create'}>创建 Server</LinkButton>;
            }}
            source={`/api/mcp`}
            columns={[
                {
                    title: '名称',
                    dataIndex: 'name'
                },
                {
                    title: '关联API',
                    dataIndex: 'apis',
                    render({ value }) {
                        return <ul className={'mb-0'}>
                            {value.map((api: Api) => {
                                return <li key={api.id}>{api.title}</li>;
                            })}
                        </ul>;
                    }
                },
                {
                    title: 'URL',
                    dataIndex: 'endpoint',
                    render: ({ value }) => {
                        return <Space>
                            <span className={'bg-light px-2 py-1 text-muted rounded-2'}>{value}</span>
                            <a onClick={() => {
                                copy(value);
                                Toast.success('已复制');
                            }} className={'fs-7'} role={'button'}><i className={'bi bi-clipboard'} /></a>
                        </Space>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <LinkButton to={`/api/mcp/${record.id}/edit`}>编辑</LinkButton>
                            <RequestButton
                                url={`/api/mcp/${record.id}`}
                                method={'delete'}
                                confirm={'确定要删除吗？'}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
