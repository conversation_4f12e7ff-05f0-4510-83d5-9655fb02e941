import { Content, Loader, useNavigate, useParams, useRequest } from '@topthink/common';
import McpForm from './form';

export const Component = () => {

    const navigate = useNavigate();
    const { id } = useParams();
    const { result } = useRequest<ApiMcp>(`/api/mcp/${id}`);
    
    return <Content>
        {!result && <Loader />}
        {result && <McpForm
            method={'PUT'}
            action={`/api/mcp/${id}`}
            formData={{
                name: result.name,
                apis: result.apis.map(item => item.id)
            }}
            onSuccess={() => {
                navigate('/api/mcp');
            }}
        />}
    </Content>;
};
