import { Columns, Content, LinkButton, ModalForm, Space, Table } from '@topthink/common';
import { Badge } from 'react-bootstrap';

export const Component = function() {
    const columns: Columns<Api> = [
        {
            title: '名称',
            dataIndex: 'name',
            render({ record }) {
                return <Space>
                    <img src={record.logo} style={{ width: 24, height: 24 }} />
                    <a href={record.url} target={'_blank'}>{record.title}</a>
                    {record.price_type === 0 && <Badge bg={'success'}>免费</Badge>}
                    {record.price_type === 1 && <Badge bg={'orange'}>会员</Badge>}
                </Space>;
            }
        },
        {
            title: '使用情况',
            dataIndex: 'numbers',
            render({ record }) {
                if (record.price_type === 2) {
                    return <span className='text-muted'>
                        剩余次数：<span className='text-success'>{record.pivot.nums}</span>次
                    </span>;
                }

                return <span className='text-orange'>限{record.pivot.rate[2]}，根据会员等级，调用频率限制不同</span>;
            }
        },
        {
            title: '操作',
            key: 'action',
            align: 'right',
            render({ record, action }) {
                return <Space>
                    {record.price_type === 2 &&
                        <LinkButton to={`/api/${record.id}/package`} variant={'link'}>套餐管理</LinkButton>}
                    {record.price_type === 2 && <ModalForm
                        onSuccess={action.reload}
                        method={'post'}
                        action={`/api/${record.id}/warn`}
                        modalProps={{ header: '预警设置' }}
                        formContext={{ layout: 'horizontal' }}
                        formData={{
                            warn_nums: record.pivot.warn_nums
                        }}
                        schema={{
                            type: 'object',
                            properties: {
                                warn_nums: {
                                    title: '预警次数',
                                    type: 'number',
                                    minimum: 0,
                                }
                            }
                        }}
                        uiSchema={{
                            warn_nums: {
                                'ui:help': '低于预警次数将发送通知提醒，设置为0的时候关闭预警',
                            }
                        }}
                        text={'预警'}
                    />}
                    <LinkButton to={`/api/${record.id}/stats`} variant={'link'}>统计</LinkButton>
                </Space>;
            }
        }
    ];


    return <Content title={'我的API'}>
        <Table
            sync
            toolBarRender={() => {
                return <LinkButton to={'/api/apply'}>申请新API</LinkButton>;
            }}
            source={`/api`}
            columns={columns}
        />
    </Content>;
};
