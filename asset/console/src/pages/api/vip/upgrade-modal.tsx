import { useCallback, useMemo, useState } from 'react';
import awaitPayComplete from '@/utils/await-pay-complete';
import { ModalButton, NumberFormat, request, showRequestError, Space } from '@topthink/common';
import dayjs from 'dayjs';
import { API_INFO } from '@/utils/constants';
import { FormCheck } from 'react-bootstrap';

interface Props {
    vip: ApiVip;
    onSuccess: () => void;
}

export default function UpgradeModal({ onSuccess, vip }: Props) {
    const [confirmed, setConfirmed] = useState(false);

    const amount = useMemo(() => {
        const today = dayjs();
        const days = dayjs(vip.expire_time).diff(today, 'days');

        return Math.floor(days / 365 * (API_INFO['gold'].price[0] - API_INFO['senior'].price[0]));
    }, [vip]);

    const handleOk = useCallback(async () => {
        try {
            const result = await request<PayResult>({
                url: '/api/vip',
                method: 'put'
            });

            if (result) {
                awaitPayComplete({
                    result,
                    onComplete: onSuccess
                });
                return true;
            }
        } catch (e) {
            showRequestError(e);
        }
        return false;
    }, []);

    return <ModalButton
        text={'升级黄金会员'}
        onOk={handleOk}
        modalProps={{
            okButtonProps: {
                tooltip: !confirmed ? '请先勾选相关服务协议才能购买' : undefined,
                disabled: !confirmed
            },
            footer: function({ okButton, cancelButton }) {
                return <>
                    <FormCheck
                        className='me-auto'
                        id={'api-apply-confirm'}
                        checked={confirmed}
                        onChange={e => setConfirmed(e.target.checked)}
                        label={<>我已阅读并同意<a className='link-primary' href='https://doc.topthink.com/think-api/license.html' target='_blank'>《API服务协议》</a></>}
                    />
                    {cancelButton}
                    {okButton}
                </>;
            }
        }}
    >
        <div className='d-flex'>
            <label className='col-sm-2 col-form-label'>到期日期</label>
            <div className='col-sm-10 d-flex align-items-center'>
                {dayjs(vip.expire_time).format('YYYY-MM-DD')}
            </div>
        </div>
        <div className='d-flex'>
            <label className='col-sm-2 col-form-label'>需补差价</label>
            <Space className='col-sm-10 d-flex align-items-center'>
                <NumberFormat className='text-danger fs-5' value={amount} />
            </Space>
        </div>
    </ModalButton>;
}
