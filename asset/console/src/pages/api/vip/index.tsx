import { API_INFO } from '@/utils/constants';
import { Card, Content, Loader, useRequest } from '@topthink/common';
import dayjs from 'dayjs';
import BuyModal from './buy-modal';
import UpgradeModal from './upgrade-modal';

export const Component = function() {

    const { result, execute } = useRequest<ApiVip>('/api/vip');

    if (result === undefined) {
        return <Loader />;
    }

    return <Content title='会员中心'>
        <Card>
            <dl className='row'>
                <dd className='col-2'>当前版本</dd>
                <dd className='col-10'>{result ? API_INFO[result.plan].name : '普通会员'}</dd>
            </dl>
            <dl className='row mb-0'>
                <dd className='col-2'>到期日期</dd>
                <dd className='col-10'>{result ? dayjs(result.expire_time).format('YYYY-MM-DD') : '-'}</dd>
            </dl>
        </Card>
        <Card>
            <table className='table'>
                <thead>
                <tr>
                    <th></th>
                    <th className='text-center'>
                        <h6>普通会员</h6>
                        <h3 className='text-primary'>
                            免费
                        </h3>
                    </th>
                    <th className='text-center'>
                        <h6>高级会员</h6>
                        <h3 className='text-primary'>
                            ￥499/年
                        </h3>
                    </th>
                    <th className='text-center'>
                        <h6>黄金会员</h6>
                        <h3 className='text-primary'>
                            ￥2599/年
                        </h3>
                    </th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <th colSpan={4}>免费类接口特权</th>
                </tr>
                <tr>
                    <td>申请个数</td>
                    <td className='text-center'>
                        10个
                    </td>
                    <td className='text-center'>
                        20个
                    </td>
                    <td className='text-center'>
                        不限
                    </td>
                </tr>
                <tr>
                    <td>调用频率</td>
                    <td className='text-center'>
                        50次/天
                    </td>
                    <td className='text-center'>
                        100次/分钟
                    </td>
                    <td className='text-center'>
                        1000次/分钟
                    </td>
                </tr>
                <tr>
                    <td>调用次数</td>
                    <td className='text-center'>
                        不限
                    </td>
                    <td className='text-center'>
                        不限
                    </td>
                    <td className='text-center'>
                        不限
                    </td>
                </tr>
                <tr>
                    <th>会员专属接口特权</th>
                    <td className='text-center'>
                        <i className='bi bi-x-lg'></i>
                    </td>
                    <td className='text-center'>
                        <i className='bi bi-check2'></i>
                    </td>
                    <td className='text-center'>
                        <i className='bi bi-check2'></i>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td className='text-center'>

                    </td>
                    <td className='text-center'>
                        {(!result || result.plan === 'senior') &&
                            <BuyModal vip={result} type={'senior'} onSuccess={execute} />}
                    </td>
                    <td className='text-center'>
                        {(result && result.plan === 'senior') && <UpgradeModal vip={result} onSuccess={execute} />}
                        {(!result || result.plan === 'gold') &&
                            <BuyModal vip={result} type={'gold'} onSuccess={execute} />}
                    </td>
                </tr>
                </tbody>
            </table>
        </Card>
    </Content>;
};
