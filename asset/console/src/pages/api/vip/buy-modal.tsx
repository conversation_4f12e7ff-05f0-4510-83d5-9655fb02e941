import { useCallback, useMemo, useState } from 'react';
import dayjs from 'dayjs';
import { ModalForm, NumberFormat, Space } from '@topthink/common';
import awaitPayComplete from '@/utils/await-pay-complete';
import { API_INFO } from '@/utils/constants';
import { FormCheck } from 'react-bootstrap';

interface Props {
    vip?: ApiVip;
    type: 'senior' | 'gold';
    onSuccess: () => void;
}

export default function BuyModal({ type, vip, onSuccess }: Props) {

    const [confirmed, setConfirmed] = useState(false);

    const info = API_INFO[type];

    const [action, startDate] = useMemo(() => {

        const now = dayjs();
        const expire = vip ? dayjs(vip.expire_time) : dayjs();

        const action = vip ? 'renew' : 'buy';

        const startDate = expire.isBefore(now) ? now : expire;

        return [action, startDate];
    }, [vip]);

    const [data, setData] = useState({ years: 1, plan: type });

    const handleSuccess = useCallback(async (result: any) => {
        awaitPayComplete({
            result,
            onComplete: onSuccess
        });
    }, []);

    return <ModalForm
        text={action === 'buy' ? `购买${info.name}` : `续费${info.name}`}
        onSuccess={handleSuccess}
        formContext={{ layout: 'horizontal' }}
        method={'post'}
        action={`/api/vip`}
        schema={{
            type: 'object',
            properties: {
                years: {
                    type: 'number',
                    title: '购买时长',
                    enum: [1, 2],
                    default: 1
                },
                plan: {
                    type: 'string',
                    default: type
                }
            }
        }}
        uiSchema={{
            plan: {
                'ui:widget': 'hidden'
            },
            years: {
                'ui:widget': 'radio',
                'ui:options': {
                    enumNames: ['1年', '2年'],
                    button: true
                }
            }
        }}
        formData={data}
        onChange={e => setData(e.formData)}
        modalProps={{
            okButtonProps: {
                tooltip: !confirmed ? '请先勾选相关服务协议才能购买' : undefined,
                disabled: !confirmed
            },
            footer: function({ okButton, cancelButton }) {
                return <>
                    <FormCheck
                        className='me-auto'
                        id={'api-apply-confirm'}
                        checked={confirmed}
                        onChange={e => setConfirmed(e.target.checked)}
                        label={<>我已阅读并同意<a className='link-primary' href='https://doc.topthink.com/think-api/license.html' target='_blank'>《API服务协议》</a></>}
                    />
                    {cancelButton}
                    {okButton}
                </>;
            }
        }}
    >
        <div className='d-flex'>
            <label className='col-sm-2 col-form-label'>到期日期</label>
            <div className='col-sm-10 d-flex align-items-center'>
                {startDate.add(data.years, 'years').format('YYYY-MM-DD')}
            </div>
        </div>
        <div className='d-flex'>
            <label className='col-sm-2 col-form-label'>合计</label>
            <Space className='col-sm-10 d-flex align-items-center'>
                <NumberFormat className='text-muted fs-5 text-decoration-line-through' value={info.price[1] * data.years} />
                <NumberFormat className='text-danger fs-5' value={info.price[0] * data.years} />
            </Space>
        </div>
    </ModalForm>;
}
