import PeriodButtons from '@/components/period-buttons';
import StatsChart from '@/components/stats-chart';
import { Content, useRouteLoaderData } from '@topthink/common';
import { useState } from 'react';

export const Component = function() {
    const api = useRouteLoaderData('api') as Api;

    const [period, setPeriod] = useState('24hours');

    return <Content
        title={`${api.title} 接口统计`}
        extra={
            <PeriodButtons period={period} periods={['24hours', 'yesterday', '30days', 'last-month', '90days']} onChange={setPeriod} />}
    >
        <StatsChart url={`/api/${api.id}/stats`} period={period} />
    </Content>;
};
