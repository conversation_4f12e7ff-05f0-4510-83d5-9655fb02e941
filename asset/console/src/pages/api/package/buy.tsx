import {
    Card,
    Content,
    Loader,
    NumberFormat,
    RequestButton,
    Space,
    useNavigate,
    useParams,
    useRequest
} from '@topthink/common';
import { useState } from 'react';
import { Alert, Col, Row } from 'react-bootstrap';
import classNames from 'classnames';
import awaitPayComplete from '@/utils/await-pay-complete';

export const Component = function() {
    const navigate = useNavigate();
    const { id, packageId } = useParams();

    const [pkg, setPkg] = useState<ApiPackage>({
        id: 0,
        price: 0,
        nums: 0,
        original_price: 0
    });

    const { result } = useRequest<Api>(`/api/${id}/packages`, {
        onSuccess: (result) => {
            if (result.packages.length > 0) {
                if (packageId) {
                    const pkg = result.packages.find((item) => item.id === parseInt(packageId));
                    if (pkg) {
                        setPkg(pkg);
                        return;
                    }
                }
                setPkg(result.packages[0]);
            }
        }
    });

    if (!result) {
        return <Loader />;
    }

    return <Content title={`${result.title} 套餐购买`}>
        <Card>
            <Row className='mb-4 align-items-center'>
                <Col className={'text-end'} md={2}>选择套餐：</Col>
                <Col md={10}>
                    <Space>
                        {result.packages.map((item) => {
                            return <div
                                key={item.id}
                                onClick={() => setPkg(item)}
                                role='button'
                                className={classNames('border rounded px-5 py-2 ', {
                                    'border-primary text-primary bg-primary bg-opacity-10 fw-bold': pkg.id === item.id,
                                })}
                            >{item.nums}次</div>;
                        })}
                    </Space>
                </Col>
            </Row>
            <Row className='mb-4 align-items-center'>
                <Col className={'text-end'} md={2}>套餐金额：</Col>
                <Col md={10} className={'d-flex align-items-center'}>
                    <NumberFormat className='text-muted fs-3 text-decoration-line-through me-2' value={pkg.original_price} />
                    <NumberFormat className='text-danger fs-3' value={pkg.price} />
                    <span className='text-muted fs-6'>（约{(pkg.price / pkg.nums).toFixed(4)}元/次）</span>
                </Col>
            </Row>
            <Row>
                <Col md={2}></Col>
                <Col md={10}>
                    <RequestButton
                        className={'rounded-pill px-5'}
                        size={'lg'}
                        url={{
                            url: `/api/${id}/package/buy`,
                            method: 'post',
                            data: {
                                package: pkg.id
                            }
                        }}
                        onSuccess={(result) => {
                            awaitPayComplete({
                                result,
                                onComplete: () => {
                                    navigate(`/api/${id}/package`);
                                }
                            });
                        }}
                    >去支付</RequestButton>
                </Col>
            </Row>
        </Card>

        <Alert className='shadow-sm' variant={'primary'}>
            <ul className='mb-0'>
                <li>如无特别说明，单个产品套餐的有效期为自购买之日起两年</li>
            </ul>
        </Alert>
    </Content>;
}
