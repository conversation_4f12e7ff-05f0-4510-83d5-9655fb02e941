import { Content, LinkButton, Table, useRouteLoaderData } from '@topthink/common';

export const Component = function() {

    const api = useRouteLoaderData('api') as Api;

    return <Content title={`${api.title} 套餐管理`}>
        <Table
            toolBarRender={() => {
                return <LinkButton to={`/api/${api.id}/package/buy`}>购买套餐</LinkButton>;
            }}
            source={`/api/${api.id}/package`}
            columns={[
                {
                    title: '套餐名称',
                    dataIndex: 'name'
                },
                {
                    title: '价格',
                    dataIndex: 'price',
                    valueType: 'currency'
                },
                {
                    title: '总次数',
                    dataIndex: 'total_nums'
                },
                {
                    title: '剩余量',
                    dataIndex: 'nums'
                },
                {
                    title: '失效时间',
                    dataIndex: 'expire_time'
                }
            ]}
        />
    </Content>;
}
