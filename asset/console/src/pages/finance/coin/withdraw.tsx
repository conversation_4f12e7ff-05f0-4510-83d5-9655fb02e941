import {
    Content,
    Form,
    FormProps,
    FormType,
    Link,
    Loader,
    ModalButton,
    ModalType,
    NumberFormat,
    Result,
    Table,
    useRequest
} from '@topthink/common';
import { Fragment, ReactNode, useCallback, useRef } from 'react';
import { Alert } from 'react-bootstrap';

function WithdrawModal({ onSuccess }: { onSuccess: FormProps['onSuccess'] }) {

    const { execute, result, error } = useRequest('/finance/coin/withdraw/create', { manual: true });

    const ref = useRef<ModalType>(null);
    const form = useRef<FormType>(null);

    const handleOk = useCallback(() => {
        form.current?.submit();
        return false;
    }, []);

    const handleSuccess = useCallback((data: any) => {
        onSuccess?.(data);
        ref.current?.close();
    }, [onSuccess]);

    let children: ReactNode;
    if (error) {
        children = <Result
            status={'error'}
            title={error.message}
            extra={<Link className={'link-primary'} to={'/user/certification'}>去实名认证</Link>}
        />;
    } else if (result) {
        const total = result.total / 100;

        if (total < 100) {
            children = <Result
                status={'error'}
                title={'云币不足100，无法提现'}
            />;
        } else {
            children = <>
                <Alert variant={'warning'}>为保证资金安全，提现账户的真实姓名必须和实名认证的一致。</Alert>
                <Form
                    ref={form}
                    action={'/finance/coin/withdraw'}
                    method={'post'}
                    schema={{
                        type: 'object',
                        properties: {
                            amount: {
                                title: '提现金额',
                                type: 'number',
                                minimum: 100,
                                description: `当前最多可提现${total}元`
                            },
                            name: {
                                title: '真实姓名',
                                type: 'string',
                                readOnly: true,
                            },
                            account: {
                                title: '支付宝账号',
                                type: 'string',
                                default: result.account
                            }
                        }
                    }}
                    uiSchema={{
                        amount: {
                            'ui:options': {
                                placeholder: `请输入提现金额，最低100元`
                            }
                        },
                        account: {
                            'ui:options': {
                                placeholder: '请填写与上面真实姓名对应的支付宝账号'
                            }
                        }
                    }}
                    formData={{
                        name: result.name
                    }}
                    onSuccess={handleSuccess}
                ><Fragment /></Form>
            </>;
        }
    } else {
        children = <Loader wrap />;
    }

    return <ModalButton ref={ref} text={'申请提现'} onShow={execute} onOk={handleOk}>
        {children}
    </ModalButton>;
};


export const Component = function() {
    return <Content title={'提现记录'}>
        <Table
            toolBarRender={({ reload }) => {
                return <WithdrawModal onSuccess={reload} />;
            }}
            columns={[
                {
                    title: '日期',
                    align: 'left',
                    dataIndex: 'create_time',
                    width: 200
                },
                {
                    title: '账号',
                    align: 'left',
                    dataIndex: 'account',
                    width: 300,
                    render({ record }) {
                        return `${record.name} | ${record.account}`;
                    }
                },
                {
                    title: '数量',
                    dataIndex: 'amount',
                    width: 200,
                    render({ value }) {
                        return <NumberFormat currency={false} className={'text-orange'} value={value / 100} />;
                    }
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    render({ value }) {
                        return value || '--';
                    }
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    width: 200,
                    align: 'right',
                    render({ record }) {
                        return <span className={`text-${record.status_color}`}>{record.status_text}</span>;
                    }
                }
            ]}
            source={'/finance/coin/withdraw'}
        />
    </Content>;
}
