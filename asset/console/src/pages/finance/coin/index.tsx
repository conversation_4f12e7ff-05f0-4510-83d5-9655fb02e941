import { Columns, Content, Link, Loader, NumberFormat, Space, Table, useRequest } from '@topthink/common';

export const Component = function() {

    const { result: coin } = useRequest('/finance/coin');

    if (!coin) {
        return <Loader />;
    }

    const columns: Columns = [
        {
            title: '日期',
            align: 'left',
            dataIndex: 'create_time',
            width: 200
        },
        {
            title: '说明',
            align: 'left',
            dataIndex: 'info',
        },
        {
            title: '数量',
            align: 'right',
            dataIndex: 'number',
            render({ value, record }) {
                const className = record.type === 1 ? 'text-success' : 'text-danger';

                return <span className={className}>
                    {record.type === 1 ? '+' : '-'}
                    <NumberFormat currency={false} value={value / 100} />
                </span>;
            }
        }
    ];

    return <Content title='云币管理'>
        <Table
            toolBarRender={() => {
                return <Space>
                    <span className={'d-flex align-items-center'}>
                        共
                        <span className='fs-4 text-orange mx-2'>
                            <NumberFormat currency={false} value={coin.total / 100} />
                        </span>
                        云币
                    </span>
                    <Link className={'text-primary'} to={'/finance/coin/withdraw'}>提现</Link>
                </Space>;
            }}
            columns={columns}
            source={'/finance/coin/logs'}
        />
    </Content>;
}
