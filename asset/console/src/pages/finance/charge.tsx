import { Content, NumberFormat, Space, Table } from '@topthink/common';

export const Component = function() {
    return <Content>
        <Table
            source={`/finance/charge`}
            scroll={{ x: 1000 }}
            columns={[
                {
                    title: '支付单号',
                    dataIndex: 'trade_no',
                    width: 140,
                },
                {
                    title: '标题',
                    dataIndex: 'full_subject',
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    width: 200,
                    align: 'center',
                    render({ value, record }) {
                        if (value != record.net_amount) {
                            return <Space size={5}>
                                <NumberFormat className='text-muted text-decoration-line-through' value={value / 100} />
                                <NumberFormat value={record.net_amount / 100} />
                            </Space>;
                        }
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                    width: 150,
                },
                {
                    title: '状态',
                    width: 80,
                    dataIndex: 'status',
                    align: 'center',
                    render({ record }) {
                        if (record.revoke_time) {
                            return <span className={'text-danger'}>已撤销</span>;
                        }
                        if (!record.is_paid) {
                            return <span className={'text-warning'}>未支付</span>;
                        }
                        return <span className={'text-success'}>已支付</span>;
                    }
                },
                {
                    title: '支付方式',
                    width: 80,
                    dataIndex: 'channel',
                    align: 'center',
                    render({ value }) {
                        switch (value) {
                            case 'transfer':
                                return '对公转账';
                            case 'alipay':
                                return '支付宝';
                            case 'wechat':
                                return '微信支付';
                            default:
                                return '--';
                        }
                    }
                },
                {
                    title: '操作',
                    width: 80,
                    key: 'action',
                    align: 'right',
                    render({ record }) {
                        if (!record.is_paid) {
                            return <a
                                target='_blank'
                                rel='noopener noreferrer'
                                href={record.pay_url}
                                className={'text-primary'}
                            >付款</a>;
                        }
                        return '--';
                    }
                }
            ]}
        />
    </Content>;
}
