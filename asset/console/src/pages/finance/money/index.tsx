import { Columns, Content, Link, Loader, NumberFormat, Space, Table, useRequest } from '@topthink/common';

export const Component = function() {

    const { result: money } = useRequest('/finance/money');

    if (!money) {
        return <Loader />;
    }

    const columns: Columns = [
        {
            title: '日期',
            align: 'left',
            dataIndex: 'create_time',
            width: 200
        },
        {
            title: '说明',
            align: 'left',
            dataIndex: 'info',
        },
        {
            title: '金额',
            align: 'right',
            dataIndex: 'number',
            render({ value, record }) {
                const className = record.type === 1 ? 'text-success' : 'text-danger';

                return <span className={className}>
                    {record.type === 1 ? '+' : '-'}
                    <NumberFormat value={value / 100} />
                </span>;
            }
        }
    ];

    return <Content title='余额管理'>
        <Table
            toolBarRender={() => {
                return <Space>
                    <span className={'d-flex align-items-center'}>
                        共
                        <span className='fs-4 text-orange mx-2'>
                            <NumberFormat value={money.total / 100} />
                        </span>
                        元
                    </span>
                    <Link className={'text-primary'} to={'/finance/money/withdraw'}>提现</Link>
                </Space>;
            }}
            columns={columns}
            source={'/finance/money/logs'}
        />
    </Content>;
}
