import { Card, Content, Link, Loader, NumberFormat, styled, useRequest } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export const Component = function() {

    const { result } = useRequest('/finance/statistics/basic');

    if (!result) {
        return <Loader />;
    }
    return <Content title={'财务概览'}>
        <Card title={'账户信息'}>
            <dl className={'row'}>
                <dd className={'col-3 col-md-1'}>余额：</dd>
                <dd className={'col-9'}>
                    <NumberFormat value={result.money / 100} />
                </dd>
            </dl>
            <dl className={'row mb-0'}>
                <dd className={'col-3 col-md-1'}>云币：</dd>
                <dd className={'col-9'}>
                    <NumberFormat value={result.coin / 100} />
                </dd>
            </dl>
        </Card>
        <Card title={'待办事项'}>
            <Row className={'g-3'}>
                <Col md={3}>
                    <Item>
                        <Icon>
                            <i className='bi bi-card-list' />
                        </Icon>
                        <Info>
                            <h5><Link to={'/finance/charge'}>{result.paying}</Link></h5>
                            <p>待支付订单</p>
                        </Info>
                    </Item>

                </Col>
                <Col md={3}>
                    <Item>
                        <Icon>
                            <i className='bi bi-cash-stack' />
                        </Icon>
                        <Info>
                            <h5>
                                <Link to={'/finance/invoice/create'}>
                                    <NumberFormat value={result.invoicing / 100} />
                                </Link>
                            </h5>
                            <p>待开票金额</p>
                        </Info>
                    </Item>
                </Col>
            </Row>
        </Card>
    </Content>;
}

const Item = styled.div`
    width: 100%;
    height: 100px;
    padding: 20px;
    background: #f6f7fb;
    display: flex;
    align-items: center;
`;

const Icon = styled.div`
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    border: 1px solid var(--bs-primary);
    border-radius: 50%;
    justify-content: center;
    margin-right: 20px;

    .bi {
        font-size: 22px;
        color: var(--bs-primary);
    }
`;

const Info = styled.div`
    h5 {
        a {
            color: var(--bs-primary);
        }
    }

    p {
        color: #666;
        margin-bottom: 0;
    }
`;
