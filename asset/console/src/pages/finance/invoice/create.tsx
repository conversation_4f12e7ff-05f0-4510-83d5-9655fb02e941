import {
    Card,
    Content,
    Form,
    Loader,
    NumberFormat,
    PaginationType,
    Space,
    Steps,
    Table,
    useNavigate,
    useRequest
} from '@topthink/common';
import { useMemo, useState } from 'react';
import { Alert, Button } from 'react-bootstrap';

interface OrdersProps {
    onNext: (selected: any[]) => void;
}

const Orders = function ({ onNext }: OrdersProps) {

    const [selected, setSelected] = useState<any[]>([]);

    const amount = useMemo(() => {
        return selected.reduce((total: number, item: any) => total + item.net_amount, 0);
    }, [selected]);

    return <>
        <Table
            toolBarRender={false}
            source={`/finance/invoice/invoiceable`}
            rowSelection={{
                selectedRowKeys: selected,
                onChange: (_, records) => setSelected(records)
            }}
            columns={[
                {
                    title: '订单标题',
                    dataIndex: 'full_subject',
                },
                {
                    title: '可开票金额',
                    dataIndex: 'net_amount',
                    width: 100,
                    align: 'center',
                    render({ value }) {
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    title: '日期',
                    width: 160,
                    align: 'center',
                    dataIndex: 'create_time',
                },
            ]}
        />
        <Card>
            <Space>
                <Button disabled={amount <= 0} onClick={() => {
                    onNext(selected);
                }}>下一步</Button>
                <span>待开票金额：<span className='fs-5 text-success'><NumberFormat value={amount / 100} /></span></span>
            </Space>
        </Card>
    </>;
};

interface SubmitProps {
    selected: any[];
    onPrev: () => void;
}

const Submit = function ({ selected, onPrev }: SubmitProps) {

    const { result } = useRequest<PaginationType>('/finance/invoice');

    const navigate = useNavigate();

    const amount = useMemo(() => {
        return selected.reduce((total: number, item: any) => total + item.net_amount, 0);
    }, [selected]);

    const formData: any = {
        orders: selected.map(item => item.id)
    };

    if (!result) {
        return <Card>
            <Loader wrap />
        </Card>;
    }

    if (result.data.length > 0) {
        formData.taxpayer_number = result.data[0].taxpayer_number;
        formData.title = result.data[0].title;
    }

    return <Card>
        <Alert variant='info'>
            您选取了{selected.length}条单据开具发票，开票金额合计：<NumberFormat value={amount / 100} />
        </Alert>
        <Form
            action={'/finance/invoice'}
            formData={formData}
            onSuccess={() => {
                navigate('/finance/invoice');
            }}
            schema={{
                type: 'object',
                required: ['type', 'title', 'taxpayer_number'],
                properties: {
                    type: {
                        type: 'number',
                        title: '发票类型',
                        enum: [1, 2],
                        default: 1
                    },
                    title: {
                        type: 'string',
                        title: '发票抬头'
                    },
                    taxpayer_number: {
                        type: 'string',
                        title: '纳税人识别号'
                    }
                }
            }}
            uiSchema={{
                type: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['增值税普通发票', '增值税专用发票'],
                    },
                }
            }}
            submitText={'申请发票'}
        >{({ submit }) => {
            return <div className='col-12'>
                <Space>
                    <Button variant={'secondary'} onClick={() => {
                        onPrev();
                    }}>上一步</Button>
                    {submit}
                </Space>
            </div>;
        }}</Form>
    </Card>;
};

export const Component = function() {

    const [step, setStep] = useState(0);
    const [selected, setSelected] = useState<any[]>([]);

    return <Content title={'申请发票'}>
        <Card>
            <div className='row py-4'>
                <div className={'col-md-6 offset-md-3'}>
                    <Steps
                        current={step}
                    >
                        <Steps.Step title={'选择订单'}></Steps.Step>
                        <Steps.Step title={'确定发票信息'}></Steps.Step>
                    </Steps>
                </div>
            </div>
        </Card>
        {step === 0 && <Orders onNext={(selected) => {
            setStep(1);
            setSelected(selected);
        }} />}
        {step === 1 && <Submit onPrev={() => {
            setStep(0);
        }} selected={selected} />}
        <Alert className={'shadow-sm'}>
            <ul className='mb-0'>
                <li>发票内容为<strong>技术服务费</strong></li>
            </ul>
        </Alert>
    </Content>;
}
