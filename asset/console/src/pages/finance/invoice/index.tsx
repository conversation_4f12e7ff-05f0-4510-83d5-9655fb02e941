import { Content, LinkButton, NumberFormat, RequestButton, Table, Tooltip } from '@topthink/common';
import InvoiceOrderModal from '@/components/invoice-order-modal';

export const Component = function() {
    return <Content title={'发票管理'}>
        <Table
            toolBarRender={() => {
                return <>
                    <LinkButton to={`/finance/invoice/create`}>申请发票</LinkButton>
                </>;
            }}
            source={`/finance/invoice`}
            columns={[
                {
                    title: '发票类型',
                    dataIndex: 'type',
                    width: 120,
                    render({ value }) {
                        return value === 1 ? '增值税普通发票' : '增值税专用发票';
                    }
                },
                {
                    title: '发票抬头',
                    dataIndex: 'title',
                },
                {
                    title: '发票总额',
                    dataIndex: 'amount',
                    width: 120,
                    align: 'center',
                    render({ value }) {
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    title: '关联交易',
                    width: 100,
                    key: 'order',
                    align: 'center',
                    render({ record }) {
                        return <InvoiceOrderModal source={`/finance/invoice/${record.id}/charge`} />;
                    }
                },
                {
                    title: '申请时间',
                    dataIndex: 'create_time',
                    width: 200,
                },
                {
                    title: '发票状态',
                    dataIndex: 'status_text',
                    width: 80,
                    align: 'center',
                    render({ value, record }) {
                        if (record.status === -1) {
                            return <Tooltip
                                tooltip={record.remark}
                                placement={'bottom'}
                            >
                                <div>{value}</div>
                            </Tooltip>;
                        }

                        return value;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 80,
                    align: 'right',
                    render({ record, action }) {
                        switch (record.status) {
                            case -1:
                                return <RequestButton
                                    url={`/finance/invoice/${record.id}`}
                                    method={'delete'}
                                    variant={'link'}
                                    confirm={'确定要删除吗？'}
                                    onSuccess={action.reload}
                                >删除</RequestButton>;
                            case 1:
                                return <a className='btn btn-link' href={`${record.remark}`} rel='noopener noreferrer'
                                    target='_blank'>下载</a>;
                            default:
                                return '--';
                        }
                    }
                }
            ]}
        />
    </Content>;
}
