import { Content, LinkButton, Table } from '@topthink/common';
import dayjs from 'dayjs';

export const Component = function() {

    return <Content>
        <Table
            toolBarRender={() => {
                return <LinkButton to={`/sms/package/buy`}>购买套餐</LinkButton>;
            }}
            source={'/sms/package'}
            columns={[
                {
                    title: '套餐名称',
                    dataIndex: 'name'
                },
                {
                    title: '套餐类型',
                    dataIndex: 'type',
                    width: 100,
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return '通知短信';
                            case 2:
                                return '营销短信';
                            default:
                                return '未知';
                        }
                    }
                },
                {
                    title: '价格',
                    dataIndex: 'price',
                    valueType: 'currency',
                    align: 'center',
                    width: 120,
                },
                {
                    title: '总次数',
                    dataIndex: 'total_nums',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '剩余量',
                    dataIndex: 'nums',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '失效时间',
                    dataIndex: 'expire_time',
                    width: 150,
                    render({ value }) {
                        const isExpired = dayjs(value).isBefore(dayjs());
                        return <span className={isExpired ? 'text-muted' : ''}>{value}</span>;
                    }
                }
            ]}
        />
    </Content>;
}
