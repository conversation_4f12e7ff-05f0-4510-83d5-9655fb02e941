import { Card, Content, Loader, NumberFormat, RequestButton, Space, useNavigate, useRequest } from '@topthink/common';
import { Alert, Col, Row } from 'react-bootstrap';
import classNames from 'classnames';
import awaitPayComplete from '@/utils/await-pay-complete';
import { useState, HTMLAttributes, useEffect } from 'react';

const Button = function ({ children, active, ...props }: HTMLAttributes<HTMLDivElement> & { active: boolean; }) {
    return <div
        role='button'
        className={classNames('border rounded px-5 py-2 ', {
            'border-primary text-primary bg-primary bg-opacity-10 fw-bold': active,
        })}
        {...props}
    >{children}</div>;
};

export const Component = function() {
    const navigate = useNavigate();

    const [type, setType] = useState<string>('1');

    const [pkg, setPkg] = useState<SmsPackage>({
        nums: 0,
        price: 0
    });

    const { result } = useRequest<SmsPackages>(`/sms/package/buyable`, {
        onSuccess: (result) => {
            setPkg(result[type].packages[0]);
        }
    });

    useEffect(() => {
        if (result) {
            setPkg(result[type].packages[0]);
        }
    }, [type, result]);

    if (!result) {
        return <Loader />;
    }

    return <Content title='购买短信套餐'>
        <Card>
            <Row className='mb-4 align-items-center'>
                <Col className={'text-end'} md={2}>套餐类型：</Col>
                <Col md={10}>
                    <Space>
                        {Object.entries(result).map(([value, item]) => {
                            return <Button
                                key={value}
                                onClick={() => setType(value)}
                                active={type === value}
                            >{item.name}</Button>;
                        })}
                    </Space>
                </Col>
            </Row>
            <Row className='mb-4 align-items-center'>
                <Col className={'text-end'} md={2}>选择套餐：</Col>
                <Col md={10}>
                    <Space>
                        {result[type].packages.map((item) => {
                            return <Button
                                key={item.nums}
                                onClick={() => setPkg(item)}
                                active={pkg.nums === item.nums}
                            >{item.nums}次</Button>;
                        })}
                    </Space>
                </Col>
            </Row>
            <Row className='mb-4 align-items-center'>
                <Col className={'text-end'} md={2}>套餐金额：</Col>
                <Col md={10} className={'d-flex align-items-center'}>
                    <NumberFormat className='text-danger fs-3' value={pkg.price} />
                    <span className='text-muted fs-6'>（约{(pkg.price / pkg.nums).toFixed(4)}元/次）</span>
                </Col>
            </Row>
            <Row>
                <Col md={2}></Col>
                <Col md={10}>
                    <RequestButton
                        className={'rounded-pill px-5'}
                        size={'lg'}
                        url={{
                            url: `/sms/package/buy`,
                            method: 'post',
                            data: {
                                nums: pkg.nums,
                                type,
                            }
                        }}
                        onSuccess={(result) => {
                            awaitPayComplete({
                                result,
                                onComplete: () => {
                                    navigate(`/sms/package`);
                                }
                            });
                        }}
                    >去支付</RequestButton>
                </Col>
            </Row>
        </Card>
        <Alert className='shadow-sm' variant={'primary'}>
            <ul className='mb-0'>
                <li>如无特别说明，单个产品套餐的有效期为自购买之日起两年</li>
            </ul>
        </Alert>
    </Content >;
}
