import { Col, Row } from 'react-bootstrap';
import Statistic from '@/components/statistic';
import { Loader, Space, useRequest } from '@topthink/common';

export default function Overview({ period }: { period: string; }) {

    const { result } = useRequest({
        url: '/sms/stats/overview',
        params: { period }
    }, { refreshDeps: [period] });

    if (!result) {
        return <Loader />;
    }

    return <Row className='g-3 mb-3'>
        <Col>
            <Statistic
                title={'API请求'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-primary'} />
                    {result.total}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={<div className='d-flex'>
                    <span className='me-auto'>发送成功</span>
                    {result.total > 0 && <span className='text-success'>{(result.success / result.total * 100).toFixed(2)}%</span>}
                </div>}
                content={<Space>
                    <i className={'bi bi-circle-fill text-success'} />
                    {result.success}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'发送失败'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-danger'} />
                    {result.fail}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'实际计费'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-info'} />
                    {result.nums}
                </Space>}
            />
        </Col>
    </Row>;
}
