import { Content } from '@topthink/common';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';
import Overview from '@/pages/sms/dashboard/overview';
import StatsChart from '@/components/stats-chart';

export const Component = function() {

    const [period, setPeriod] = useState('24hours');

    return <Content
        extra={
            <PeriodButtons period={period} periods={['24hours', 'yesterday', '30days', 'last-month', '90days']} onChange={setPeriod} />}
    >
        <Overview period={period} />
        <StatsChart
            url={`/sms/stats`}
            period={period}
            type={'line'}
            isStack={false}
            series={
                {
                    nums: {
                        text: '计费',
                        color: '#07a8ff'
                    },
                    success: {
                        text: '成功',
                        color: '#5B8FF9'
                    },
                    fail: {
                        text: '失败',
                        color: '#F4664A'
                    }
                }
            }
        />
    </Content>;
}
