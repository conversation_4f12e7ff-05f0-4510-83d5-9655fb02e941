import { Content, Form, Card, useRouteLoaderData, useRevalidator } from '@topthink/common';

export const Component = function() {

    const sms = useRouteLoaderData('sms') as Sms;
    const { revalidate } = useRevalidator();

    const formData = {
        limit: {
            minute: sms.minute_limit,
            hour: sms.hour_limit,
            day: sms.day_limit,
        },
        warn: {
            nums: sms.warn_nums,
        },
        webhook: {
            url: sms.webhook,
        }
    };

    return <Content>
        <Card>
            <Form
                method='put'
                action={`/sms`}
                formData={formData}
                onSuccess={() => revalidate()}
                transformData={(data) => {
                    return {
                        minute_limit: data.limit.minute,
                        hour_limit: data.limit.hour,
                        day_limit: data.limit.day,
                        warn_nums: data.warn.nums,
                        webhook: data.webhook.url,
                    };
                }}
                schema={{
                    type: 'object',
                    properties: {
                        limit: {
                            type: 'object',
                            title: '发送频率设置',
                            description: '同一手机号码，同一短信模板',
                            properties: {
                                minute: {
                                    type: 'number',
                                    title: '每1分钟内发送上限',
                                },
                                hour: {
                                    type: 'number',
                                    title: '每1小时内发送上限',
                                },
                                day: {
                                    type: 'number',
                                    title: '每24小时内发送上限',
                                }
                            }
                        },
                        warn: {
                            type: 'object',
                            title: '套餐包预警值设置',
                            description: '套餐包总余量低于该条数时发送预警短信',
                            properties: {
                                nums: {
                                    type: 'number',
                                }
                            }
                        },
                        webhook: {
                            type: 'object',
                            title: '数据推送',
                            description: '会将短信状态报告如：短信发送、回执状态、上行回复内容和模板审核状态推送至您指定的回调 URL',
                            properties: {
                                url: {
                                    type: 'string',
                                }
                            }
                        }
                    }
                }}
                uiSchema={{
                    warn: {
                        nums: {
                            'ui:label': false
                        }
                    },
                    webhook: {
                        url: {
                            'ui:label': false,
                            'ui:placeholder': '请输入回调URL地址'
                        }
                    },
                }}
            />
        </Card>
    </Content>;
}
