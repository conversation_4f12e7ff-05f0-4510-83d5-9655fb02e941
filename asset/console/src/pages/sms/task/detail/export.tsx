import { Button, PaginationType, request, Toast } from '@topthink/common';
import { useCallback, useState } from 'react';

const getData = async (id: string, page: number = 1) => {
    return await request<PaginationType<SmsLog>>({
        url: `/sms/task/${id}/export`,
        params: { page }
    });
};

export default function Export({ id }: { id: string; }) {

    const [loading, setLoading] = useState(false);

    const handleExport = useCallback(async () => {
        setLoading(true);
        let page = 1;
        let data: SmsLog[] = [];
        while (true) {
            const result = await getData(id, page);
            data.push(...result.data);
            if (result.current_page >= result.last_page) {
                break;
            }
            page++;
        }

        if (data.length > 0) {
            const writeXlsxFile = (await import('write-excel-file')).default;

            await writeXlsxFile<SmsLog>(data, {
                schema: [
                    {
                        column: '手机号码',
                        type: String,
                        value: log => log.phone,
                        width: 20
                    },
                    {
                        column: '发送时间',
                        type: String,
                        value: log => log.send_time,
                        width: 20
                    },
                    {
                        column: '送达状态',
                        type: String,
                        value: log => log.status === 1 ? 'success' : 'fail',
                        width: 20
                    },
                    {
                        column: '失败原因',
                        type: String,
                        value: log => log.message,
                        width: 40
                    },
                ],
                fileName: `sms_task_${id}_results.xlsx`
            });
        } else {
            Toast.info('暂无数据');
        }

        setLoading(false);

    }, [id]);

    return <Button loading={loading} onClick={handleExport}>导出结果</Button>;
}
