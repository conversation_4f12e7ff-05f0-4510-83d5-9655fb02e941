import { Col, Row } from 'react-bootstrap';
import Statistic from '@/components/statistic';
import { Loader, Space, useRequest } from '@topthink/common';

export default function Overview({ task }: { task: SmsTask; }) {

    const { result } = useRequest(`/sms/task/${task.id}/overview`);

    if (!result) {
        return <Loader />;
    }

    return <Row className='bg-opacity-50 g-3'>
        <Col>
            <Statistic
                title={'号码总数'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-secondary'} />
                    {String(task.total)}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'提交成功'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-primary'} />
                    {result.total}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'发送成功'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-success'} />
                    {result.success}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'发送失败'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-danger'} />
                    {result.fail}
                </Space>}
            />
        </Col>
        <Col>
            <Statistic
                title={'实际计费'}
                content={<Space>
                    <i className={'bi bi-circle-fill text-info'} />
                    {result.nums}
                </Space>}
            />
        </Col>
    </Row>;
}
