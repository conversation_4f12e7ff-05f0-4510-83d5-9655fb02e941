import { Card, Content, Loader, Table, useParams, useRequest } from "@topthink/common";
import Overview from "./overview";
import SmsStatus from "@/components/sms-status";
import Export from "./export";

export const Component = function() {
    const { id } = useParams();

    const { result } = useRequest(`/sms/task/${id}`);

    if (!result) {
        return <Loader />;
    }

    return <Content title={result.name} showBack>
        <Card>
            <dl className='row'>
                <dd className='col-2'>模板ID</dd>
                <dd className='col-10'>{result.template.hash_id}</dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>模板内容</dd>
                <dd className='col-10'>{`【${result.template.sign.name}】${result.template.content}`}</dd>
            </dl>
            <dl className='row mb-0'>
                <dd className='col-2'>定时发送</dd>
                <dd className='col-10'>{result.send_time ?? '--'}</dd>
            </dl>
        </Card>
        <>
            <Overview task={result} />
            <Table
                toolBarRender={() => {
                    return <Export id={id!} />;
                }}
                source={`/sms/task/${id}/log`}
                rowKey={'send_id'}
                search={{
                    fields: ['phone', 'content', 'status'],
                    ui: {
                        status: {
                            'ui:widget': 'select',
                            'ui:options': {
                                enumOptions: [
                                    {
                                        label: '发送成功',
                                        value: 1
                                    },
                                    {
                                        label: '发送失败',
                                        value: -1
                                    }
                                ]
                            }
                        }
                    }
                }}
                columns={[
                    {
                        title: '手机号',
                        dataIndex: 'phone',
                        width: 110
                    },
                    {
                        title: '短信内容',
                        dataIndex: 'content',
                    },
                    {
                        title: '字数 | 拆分条数',
                        dataIndex: 'content',
                        render: ({ value, record }) => {
                            return `${value.length}字 | ${record.nums}条`;
                        },
                        width: 120
                    },
                    {
                        title: '发送时间',
                        dataIndex: 'send_time',
                        width: 150
                    },
                    {
                        title: '送达状态',
                        dataIndex: 'status',
                        width: 90,
                        render({ record }) {
                            return <SmsStatus status={record.status} message={record.message} />;
                        }
                    }
                ]}
            />
        </>
        <div className="alert alert-info">数据有效期为三个月，过期数据会自动清除</div>
    </Content>;
}