import PeriodButtons from '@/components/period-buttons';
import { Content, LinkButton, RequestButton, Space, Table, useRouteLoaderData } from '@topthink/common';
import dayjs from 'dayjs';
import { useState } from 'react';
import PageResult from '../../../components/page-result';

export const Component = function() {

    const [period, setPeriod] = useState('3days');

    const sms = useRouteLoaderData('sms') as Sms;

    if (!sms.batch_send) {
        return <PageResult
            status={'error'}
            title={'批量发送功能尚未开通，请联系客服'}
        />;
    }

    return <Content
        extra={<PeriodButtons period={period} periods={['3days', '30days', '90days']} onChange={setPeriod} />}
    >
        <Table
            key={period}
            source={`/sms/task?period=${period}`}
            columns={[
                {
                    title: '任务名称',
                    dataIndex: 'name',
                },
                {
                    title: '创建时间',
                    width: 160,
                    dataIndex: 'create_time',
                },
                {
                    title: '定时发送',
                    width: 160,
                    dataIndex: 'send_time',
                    render({ value }) {
                        return value ?? '--';
                    }
                },
                {
                    title: '任务状态',
                    dataIndex: 'status',
                    width: 100,
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return <Space><i className={'bi bi-circle-fill text-success'} />发送完成</Space>;
                            case 2:
                                return <Space><i className={'bi bi-circle-fill text-info'} />发送中</Space>;
                            case -1:
                                return <Space><i className={'bi bi-circle-fill text-danger'} />已撤销</Space>;
                            case -2:
                                return <Space><i className={'bi bi-circle-fill text-warning'} />未完成</Space>;
                            default:
                                return <Space><i className={'bi bi-circle-fill text-secondary'} />等待发送</Space>;
                        }
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 120,
                    render({ record, action }) {
                        //可撤销发送时间5分钟之前的任务
                        const canRevoke = record.status == 0 && record.send_time && dayjs(record.send_time)
                        .isAfter(dayjs().add(5, 'minute'));

                        return <Space>
                            <LinkButton to={`/sms/task/${record.id}`}>详情</LinkButton>
                            {canRevoke && <RequestButton
                                className='text-danger'
                                confirm={`确定要撤销任务【${record.name}】吗？`}
                                method='delete'
                                url={`/sms/task/${record.id}`}
                                onSuccess={() => action.reload()}
                            >撤销</RequestButton>}
                        </Space>;
                    }
                }
            ]}
            toolBarRender={() => {
                return <LinkButton to='create'>添加发送任务</LinkButton>;
            }}
        />
    </Content>;
};
