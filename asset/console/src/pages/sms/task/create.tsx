import { Card, Content, Form, FormProps, FormWidgetProps, Loader, ModalButton, Space, request, useNavigate, useRequest } from "@topthink/common";
import { But<PERSON>, Col, Row } from "react-bootstrap";
import Emulator from "../emulator";
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";
import dayjs, { Dayjs } from "dayjs";

const Context = createContext<string>('');

export const Component = function() {
    const navigate = useNavigate();
    const [content, setContent] = useState('');

    const schema = useMemo(() => {
        return {
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '任务名称'
                },
                template: {
                    type: 'string',
                    title: '短信模板',
                },
                type: {
                    type: 'string',
                    title: '接受号码',
                    enum: ['file', 'input',],
                    enumNames: ['导入接受号码文件', '手动输入接受号码'],
                    default: 'file'
                },
                regular: {
                    type: 'boolean',
                    title: '定时发送',
                    default: false,
                }
            },
            dependencies: {
                type: {
                    oneOf: [
                        {
                            properties: {
                                type: {
                                    const: 'file'
                                },
                                file: {
                                    type: 'string',
                                    title: '导入文件'
                                },
                                fileInfo: {
                                    type: 'string',
                                }
                            }
                        },
                        {
                            properties: {
                                type: {
                                    const: 'input'
                                },
                                input: {
                                    type: 'string',
                                    title: '输入号码'
                                }
                            }
                        }
                    ]
                },
                regular: {
                    oneOf: [
                        {
                            properties: {
                                regular: {
                                    const: true
                                },
                                send_time: {
                                    type: 'string',
                                    format: 'date-time',
                                }
                            }
                        },
                    ]
                }
            }
        } as FormProps['schema'];
    }, []);

    const uiSchema = useMemo<FormProps['uiSchema']>(() => {
        const now = dayjs();

        return {
            'ui:order': ['*', 'regular', 'send_time'],
            template: {
                'ui:widget': 'typeahead',
                'ui:options': {
                    async onSearch({ value }: { value?: string; }) {
                        const data = await request<SmsTemplate[]>({
                            url: '/sms/task/template',
                            params: { value }
                        });

                        return data.map(({ id, name, sign, content }) => ({ value: id, label: name, content: `【${sign.name}】${content}` }));
                    },
                    onChange(items: any[]) {
                        if (items.length > 0) {
                            setContent(items[0].content);
                        } else {
                            setContent('');
                        }
                    },
                    minLength: 0,
                    placeholder: '请选择一个模板'
                }
            },
            type: {
                'ui:widget': 'radio',
                'ui:options': {
                    inline: false
                }
            },
            file: {
                'ui:widget': 'upload',
                'ui:options': {
                    accept: '.xlsx',
                    endpoint: '/upload/sms',
                    label: false
                }
            },
            fileInfo: {
                'ui:widget': function () {
                    const content = useContext(Context);

                    const download = async () => {
                        const writeXlsxFile = (await import('write-excel-file')).default;

                        //提取内容中的变量
                        const variables = (content.match(/\$\{[^\}]+\}/g) || []).map(v => v.substring(2, v.length - 1));

                        await writeXlsxFile([
                            [{ value: 'phone' }, ...variables.map(v => ({ value: v }))],
                        ], {
                            fileName: 'template.xlsx'
                        });
                    };

                    return <div className="alert alert-secondary mb-0">
                        <div><strong>文件模板：</strong>根据您选择的模板，可<span className="text-danger">智能动态</span>生成Excel文件模板，请<a role="button" className="link-primary" onClick={download}>下载</a>，您填写相应内容后上传即可</div>
                        <div><strong>文件要求：</strong>仅支持xlsx格式的Excel文件，单元格式仅支持"常规"或"文本"，不大于3MB，建议每次导入号码数50万以内，若Excel文件含有<span className="text-danger">多个号码工作表则会全部发送</span>，请检查核实下</div>
                    </div>;
                },
                'ui:options': {
                    label: false
                }
            },
            input: {
                'ui:widget': function ({ onChange }: FormWidgetProps) {
                    const [phone, setPhone] = useState<Set<string>>(new Set);
                    const [text, setText] = useState('');
                    const [error, setError] = useState('');

                    const inputting = text.split("\n").filter(v => v.trim() !== '');
                    const nums = inputting.length + phone.size;

                    useEffect(() => {
                        onChange(Array.from(phone).join(','));
                    }, [phone]);

                    useEffect(() => {
                        if (nums > 1000) {
                            setError('不能多于1000个号码');
                        } else {
                            const invalidate = inputting.find(v => !v.match(/\d{11}/));
                            if (invalidate) {
                                setError(`“${invalidate}”格式有误。请输入正确的号码，多个手机号请换行隔开`);
                            } else {
                                setError('');
                            }
                        }
                    }, [text, nums]);

                    return <div>
                        {phone.size > 0 && <Space className='mb-3 d-flex flex-wrap'>
                            {Array.from(phone).map((item) => {
                                return <Button key={item} variant="light rounded-pill py-1 ps-3 pe-2">
                                    {item}
                                    <i className="bi bi-x ms-2" onClick={() => {
                                        phone.delete(item);
                                        setPhone(new Set(phone));
                                    }} />
                                </Button>;
                            })}
                        </Space>}
                        <Space className='mb-3 d-flex'>
                            <ModalButton
                                text={phone.size > 0 ? '继续输入号码' : '输入号码'}
                                variant="light"
                                modalProps={{
                                    centered: true,
                                    size: 'lg',
                                }}
                                onHide={() => {
                                    setText('');
                                }}
                                onOk={() => {
                                    if (error) {
                                        return false;
                                    }
                                    setPhone(new Set([...phone, ...inputting]));
                                }}
                            >
                                <p>手动输入最多支持1000个号码，大批量号码建议通过文件导入形式提交。 您已输入{nums}个号码。</p>
                                <p><textarea value={text} onChange={(e) => setText(e.target.value)} className="form-control" autoFocus rows={5} /></p>
                                {error && <p className="text-danger">{error}</p>}
                                <p>提示：一行输入一个号码，多个手机号请换行隔开。</p>
                            </ModalButton>
                            <Button variant="link" onClick={() => {
                                setPhone(new Set);
                            }}>批量清空</Button>
                        </Space>
                        <div className="alert alert-secondary mb-0">
                            手动输入最多支持1000个号码，大批量号码建议通过文件导入形式提交。 您已输入{phone.size}个号码。
                        </div>
                    </div>;
                },
                'ui:options': {
                    label: false
                }
            },
            send_time: {
                'ui:label': false,
                'ui:placeholder': '请选择日期和时间',
                'ui:help': '如需撤销，请在发送时间的5分钟之前操作',
                'ui:options': {
                    disabledDate: (date: Dayjs) => {
                        return date.isBefore(now, 'day');
                    }
                }
            }
        };
    }, []);

    const onSuccess = useCallback(() => navigate('/sms/task'), []);

    const { result } = useRequest(`/sms/package/balance`);

    if (!result) {
        return <Loader />;
    }

    return <Content showBack title='添加发送任务'>
        <Card>
            <Row>
                <Col md={7}>
                    <div className="alert alert-info">
                        <strong>套餐余额：</strong>当前<strong>通知短信</strong>套餐剩余 <span className='text-danger'>{result[0] || 0}</span> 条，<strong>营销短信</strong>套餐剩余 <span className='text-danger'>{result[1] || 0}</span> 条。超出余额的短信将不会发送。<br />
                        <strong>使用延迟：</strong>群发助手发送短信会存在一定的时间延迟，不适用于注册、登录等场景。<br />
                        <strong>撤销提醒：</strong>已处于发送中、发送成功和发送失败的短信不支持撤销，请谨慎提交！<br />
                        <strong>发送时间：</strong>晚间 22:00 之后提交的营销短信发送请求，为避免对用户造成骚扰，可能会被延迟到隔天的早间 8:00 开始发送。<br />
                    </div>
                    <Context.Provider value={content}>
                        <Form
                            action={'/sms/task'}
                            method="post"
                            schema={schema}
                            uiSchema={uiSchema}
                            onSuccess={onSuccess}
                        />
                    </Context.Provider>
                </Col>
                <Col className="d-flex justify-content-center p-3" md={5}>
                    <Emulator content={content} />
                </Col>
            </Row>
        </Card>
    </Content>;
}