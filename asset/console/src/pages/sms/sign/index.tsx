import { Content, LinkButton, RequestButton, Space, Table } from '@topthink/common';

export const Component = function() {

    return <Content>
        <Table
            toolBarRender={() => {
                return <LinkButton to={'create'}>添加签名</LinkButton>;
            }}
            columns={[
                {
                    title: '签名ID',
                    dataIndex: 'hash_id',
                    width: 100
                },
                {
                    title: '签名名称',
                    dataIndex: 'name',
                },
                {
                    title: '创建时间',
                    width: 150,
                    dataIndex: 'create_time'
                },
                {
                    title: '审核状态',
                    width: 100,
                    dataIndex: 'status',
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return <Space><i className={'bi bi-circle-fill text-success'} />通过</Space>;
                            case 2:
                                return <Space><i className={'bi bi-circle-fill text-secondary'} />已冻结</Space>;
                            case -1:
                                return <Space><i className={'bi bi-circle-fill text-danger'} />未通过</Space>;
                            default:
                                return <Space><i className={'bi bi-circle-fill text-warning'} />审核中</Space>;
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'left',
                    width: 150,
                    render: ({ record, action: { reload } }) => {
                        return <Space>
                            <RequestButton
                                method={'delete'}
                                url={`/sms/sign/${record.id}`}
                                confirm={'确认要删除该短信签名吗？请注意：删除后将无法恢复，请慎重操作'}
                                onSuccess={reload}
                            >删除</RequestButton>
                            {record.status === -1 && <LinkButton to={`/sms/sign/${record.id}/edit`}>查看原因并修改</LinkButton>}
                        </Space>;
                    }
                }
            ]}
            source={'/sms/sign'}
        />
    </Content>;
}
