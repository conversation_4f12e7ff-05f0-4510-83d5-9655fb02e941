import { Card, Form, FormProps, useNavigate } from '@topthink/common';
import { useMemo } from 'react';
import { Alert, Col, Row } from 'react-bootstrap';


export default function SmsSignForm(props: Omit<FormProps<SmsSign>, 'schema' | 'uiSchema'>) {
    const navigate = useNavigate();

    const formData = useMemo(() => {
        if (props.formData) {
            const { name, source, company, proof } = props.formData;
            return {
                name,
                source,
                company,
                proof,
                purpose: company ? 2 : 1,
            };
        }
        return undefined;
    }, [props.formData]);

    return <Card>
        <Alert variant={'info'}>
            <p>根据工信部及各级运营商的相关规定，短信签名必须进行实名制报备。短信签名需符合以下任一条件，请您按以下要求调整：</p>
            <ol className={'ms-1 ps-3 lh-base'}>
                <li><strong>企事业单位名</strong>：签名须为企业名称的全称或简称。比如公司为12345678，签名为【1234】【3456】【456】等连续性的公司名称。
                </li>
                <li><strong>已注册商标名</strong>： 已在中国商标网(https://sbj.cnipa.gov.cn)完成注册。</li>
                <li><strong>已备案APP</strong>：已在国家域名备案信息管理平台完成app备案，签名创建上传时，上传该网站查询到的相关截图
                    （ICP备案：https://beian.miit.gov.cn/）
                </li>
            </ol>
            <p>短信签名将由运营商 <strong>审核通过</strong> 才可使用。创建签名后请耐心等待审核，完全一致的签名报备一般是1-2个工作日。商标类签名报备较慢，如需使用商标名，建议同时创建公司名作为备用。
            </p>
            签名需要具有唯一性，不与其他公司/单位/品牌进行混淆，不支持歧义签名，例如【科技】【公司】等类似签名短信签名
        </Alert>
        <Row>
            <Col md={6}>
                <Form
                    {...props}
                    formData={formData}
                    onSuccess={() => navigate('/sms/sign')}
                    transformData={data => {
                        if (data.purpose == 1) {
                            data.company = null;
                        }
                        delete data.purpose;
                        return data;
                    }}
                    submitText={'提交审核'}
                    schema={{
                        type: 'object',
                        required: ['name'],
                        properties: {
                            name: {
                                type: 'string',
                                title: '签名名称',
                            },
                            source: {
                                type: 'string',
                                title: '签名来源',
                                enum: ['企事业单位名', '已注册商标名', '已备案APP'],
                                default: '企事业单位名',
                            },
                            purpose: {
                                type: 'string',
                                title: '签名用途',
                                enum: [1, 2],
                                enumNames: ['自用（签名所属主体与本账号实名认证的主体一致）', '他用（签名所属企业的主体与本账号实名认证的主体不一致）'],
                                default: 1,
                            },
                        },
                        dependencies: {
                            purpose: {
                                oneOf: [
                                    {
                                        properties: {
                                            purpose: {
                                                enum: [2]
                                            },
                                            company: {
                                                type: 'object',
                                                title: '企事业信息',
                                                properties: {
                                                    name: {
                                                        type: 'string',
                                                        title: '企事业单位名称',
                                                    },
                                                    identity: {
                                                        type: 'string',
                                                        title: '统一社会信用代码',
                                                    },
                                                    corporation: {
                                                        type: 'string',
                                                        title: '法人姓名',
                                                    },
                                                    img: {
                                                        type: 'string',
                                                        title: '营业执照',
                                                    }
                                                }
                                            }
                                        }
                                    },
                                ]
                            },
                            source: {
                                oneOf: [
                                    {
                                        properties: {
                                            source: {
                                                enum: ['已注册商标名']
                                            },
                                            proof: {
                                                title: '商标证书',
                                                type: 'string',
                                                description: '请上传商标证书'
                                            }
                                        },
                                    },
                                    {
                                        properties: {
                                            source: {
                                                enum: ['已备案APP']
                                            },
                                            proof: {
                                                title: '备案证明',
                                                type: 'string',
                                                description: '请上传备案查询截图'
                                            }
                                        },
                                    }
                                ]
                            }
                        }
                    }}
                    uiSchema={{
                        'ui:order': ['*', 'company', 'proof'],
                        purpose: {
                            'ui:widget': 'radio'
                        },
                        source: {
                            'ui:widget': 'radio'
                        },
                        company: {
                            img: {
                                'ui:widget': 'upload',
                                'ui:options': {
                                    endpoint: '/upload/sms',
                                    accept: 'image/*',
                                }
                            }
                        },
                        proof: {
                            'ui:widget': 'upload',
                            'ui:options': {
                                endpoint: '/upload/sms',
                                accept: 'image/*',
                            }
                        }
                    }}
                />
            </Col>
            <Col md={6}>

            </Col>
        </Row>
    </Card>;
}
