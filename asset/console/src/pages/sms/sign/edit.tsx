import { Content, useLoaderData } from '@topthink/common';
import { Alert } from 'react-bootstrap';
import SmsSignForm from './form';

export const Component = function() {

    const sign = useLoaderData() as SmsSign;

    return <Content title={'修改签名'}>
        {sign.status == -1 && <Alert variant={'danger'}>
            <p className='mb-0'>{sign.error}</p>
        </Alert>}
        <SmsSignForm
            formData={sign}
            method={'put'}
            action={`/sms/sign/${sign.id}`}
        />
    </Content>;
}
