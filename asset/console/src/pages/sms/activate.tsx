import { useLoaderData, Card, RequestButton, useRevalidator } from '@topthink/common';
import { PropsWithChildren, useState } from 'react';
import { Col, Container, Form, Row } from 'react-bootstrap';

export default function SmsActivate({ children }: PropsWithChildren<any>) {
    const sms = useLoaderData();
    const { revalidate } = useRevalidator();
    const [confirmed, setConfirmed] = useState(false);

    if (!sms) {
        return <Container className={'mt-5'}>
            <Row>
                <Col md={{ offset: 2, span: 8 }}>
                    <Card title={'短信服务开通'}>
                        <p>
                            <strong>产品简介：</strong>
                            短信服务 SMS(Short Message Service，SMS)
                            是顶想云为用户提供的一种通信服务，支持向国内快速发送验证码、短信通知。顶想云短信服务国内短信支持三网合一专属通道，移动、联通、电信网全覆盖，充分满足跨网发送的需求，实时监控自动切换，到达率高达
                            99%。
                        </p>
                        <p>
                            <strong>开通说明：</strong>
                            产品免费开通，赠送短信套餐 10 条。
                        </p>
                        <Form.Check
                            id={'sms-apply-confirm'}
                            checked={confirmed}
                            onChange={e => setConfirmed(e.target.checked)}
                            className='mb-3'
                            label={<>我已阅读并同意<a className='link-primary' href='https://doc.topthink.com/think-sms/license.html' target='_blank'>《短信服务协议》</a></>}
                        />
                        <RequestButton
                            url={`/sms`}
                            method={'post'}
                            onSuccess={() => {
                                revalidate();
                            }}
                            disabled={!confirmed}
                            tooltip={!confirmed ? '请先勾选相关服务协议' : undefined}
                        >立即开通</RequestButton>
                    </Card>
                </Col>
            </Row>
        </Container>;
    }

    return children;
}
