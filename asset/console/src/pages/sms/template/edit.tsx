import { Content, useLoaderData } from '@topthink/common';
import { Alert } from 'react-bootstrap';
import SmsTemplateForm from '@/pages/sms/template/form';

export const Component = function() {

    const template = useLoaderData() as SmsTemplate;

    return <Content title={'修改模板'}>
        {template.status == -1 && <Alert variant={'danger'}>
            <p className='mb-0'>{template.error}</p>
        </Alert>}
        <SmsTemplateForm
            formData={template}
            method={'put'}
            action={`/sms/template/${template.id}`}
        />
    </Content>;
}
