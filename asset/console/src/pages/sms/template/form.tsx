import { Card, Form, FormProps, request, useNavigate } from '@topthink/common';
import { Alert, Col, Row } from 'react-bootstrap';

export default function SmsTemplateForm(props: Omit<FormProps<SmsTemplate>, 'schema' | 'uiSchema'>) {
    const navigate = useNavigate();

    return <Card>
        <Row>
            <Col md={6}>
                <Form
                    {...props}
                    onSuccess={() => navigate('/sms/template')}
                    schema={{
                        type: 'object',
                        required: ['type', 'sign_id', 'name', 'content', 'remark'],
                        properties: {
                            type: {
                                type: 'string',
                                title: '模板类型',
                                enum: [1, 2],
                                enumNames: ['通知短信', '营销短信'],
                                default: 1
                            },
                            name: {
                                type: 'string',
                                title: '模板名称',
                            },
                            sign_id: {
                                type: 'number',
                                title: '关联签名',
                            },
                            content: {
                                type: 'string',
                                title: '模板内容',
                            }
                        }
                    }}
                    uiSchema={{
                        type: {
                            'ui:widget': 'radio',
                        },
                        sign_id: {
                            'ui:widget': 'typeahead',
                            'ui:options': {
                                async onSearch({ value }: { value?: string; }) {
                                    const data = await request<SmsSign[]>({
                                        url: '/sms/template/sign',
                                        params: { value }
                                    });

                                    return data.map(({ id, name }) => ({ value: id, label: name }));
                                },
                                minLength: 0,
                                placeholder: '请选择一个签名'
                            }
                        },
                        name: {
                            'ui:placeholder': '请输入模板名称'
                        },
                        content: {
                            'ui:widget': 'textarea',
                            'ui:options': {
                                rows: 4,
                                placeholder: '请输入模板内容',
                            },
                            'ui:help': '变量格式：${code}<br />示例：您的验证码为：${code}，该验证码 5 分钟内有效，请勿泄漏于他人。<br /><strong>营销类短信请在短信的末尾加上“拒收请回复R”</strong>'
                        }
                    }}
                    submitText={'提交审核'}
                >
                    {({ submit }) => {
                        return <>
                            <Col md={12}>
                                <Alert variant='success'>
                                    短信字数含“签名+模板内容+变量内容”，普通短信 70 字符计费 1 条（包含标点符号和空格）
                                </Alert>
                                <Alert variant='warning' className='mb-0'>
                                    请注意：创建或更新模板需要人工审核，模板审核时间一般为工作日（周一至周五）：上午8：00-凌晨12：00；
                                    国定假期和双休日：上午9：00-凌晨12：00，工作时间一般为1-5分钟以内审核完成。
                                </Alert>
                            </Col>
                            <Col md={12}>
                                {submit}
                            </Col>
                        </>;
                    }}
                </Form>
            </Col>
            <Col md={6}>

            </Col>
        </Row>
    </Card>;
}
