import { Content, LinkButton, RequestButton, Space, Table, Tooltip } from '@topthink/common';
import InfoModal from '@/components/info-modal';
import { Badge } from 'react-bootstrap';

export const Component = function() {

    return <Content>
        <Table
            toolBarRender={() => {
                return <LinkButton to={'/sms/template/create'}>添加模板</LinkButton>;
            }}
            source={'/sms/template'}
            columns={[
                {
                    title: '模板ID',
                    dataIndex: 'hash_id',
                    width: 100
                },
                {
                    title: '模板名称',
                    dataIndex: 'name',
                    render({ value, record }) {
                        return <Space>
                            {value}
                            {record.user_id == 0 &&
                                <Tooltip tooltip={'公共模板'}><Badge bg={'success'}>公</Badge></Tooltip>}
                        </Space>;
                    }
                },
                {
                    title: '模板类型',
                    dataIndex: 'type',
                    width: 100,
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return '通知短信';
                            case 2:
                                return '营销短信';
                            default:
                                return '未知';
                        }
                    }
                },
                {
                    title: '创建时间',
                    width: 150,
                    dataIndex: 'create_time'
                },
                {
                    title: '审核状态',
                    width: 100,
                    dataIndex: 'status',
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return <Space><i className={'bi bi-circle-fill text-success'} />通过</Space>;
                            case 2:
                                return <Space><i className={'bi bi-circle-fill text-secondary'} />已冻结</Space>;
                            case -1:
                                return <Space><i className={'bi bi-circle-fill text-danger'} />未通过</Space>;
                            default:
                                return <Space><i className={'bi bi-circle-fill text-warning'} />审核中</Space>;
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'left',
                    width: 200,
                    render: ({ record, action: { reload } }) => {
                        return <Space>
                            <InfoModal
                                text={'详情'}
                                renderChildren={() => {
                                    return <>
                                        <dl className='row'>
                                            <dd className='col-2'>模板名称</dd>
                                            <dd className='col-10'>{record.name}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>模板内容</dd>
                                            <dd className='col-10'>【{record.sign ? record.sign.name : '签名'}】{record.content}</dd>
                                        </dl>
                                        {record.user_id > 0 && <dl className='row'>
                                            <dd className='col-2'>场景说明</dd>
                                            <dd className='col-10'>{record.remark}</dd>
                                        </dl>}
                                    </>;
                                }}
                            />
                            {record.status === -1 &&
                                <LinkButton to={`/sms/template/${record.id}/edit`}>查看原因并修改</LinkButton>}
                            {record.user_id > 0 && <RequestButton
                                method={'delete'}
                                url={`/sms/template/${record.id}`}
                                confirm={'确认要删除该模板吗？请注意：删除后将无法恢复，请慎重操作'}
                                onSuccess={reload}
                            >删除</RequestButton>}
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
