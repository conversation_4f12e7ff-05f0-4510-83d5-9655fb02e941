import { styled } from "@topthink/common";
import emulatorBg from '../../images/emulator.png';
export default function Emulator({ content }: { content: string; }) {

    const nums = content.length > 70 ? Math.ceil(content.length / 67) : 1;

    return <Container>
        <Screen>
            <Bubble>{content}</Bubble>
            <Help>计费说明：当前<span className="text-primary">{content.length}</span>字，预计发送条数约为<span className="text-primary">{nums}</span>条（<strong>实际发送时，签名、变量长度会影响总字数</strong>）。</Help>
        </Screen>
    </Container>;
}

const Help = styled.div`
    margin: 8px 0;
    color: #888;
    font-size: 12px;
    line-height: 20px;
`;

const Bubble = styled.div`
    position: relative;
    min-height: 40px;
    margin: 15px 0 0;
    padding: 10px;
    color: #333;
    word-wrap: break-word;
    background: #e9e9eb;
    border-radius: 10px;
    &:after{
        position: absolute;
        bottom: 2px;
        left: -5px;
        border-bottom: 10px solid #e6e7ec;
        border-left: 10px solid transparent;
        content: "";
    }
`;

const Screen = styled.div`
    height: 330px;
    margin: 70px 7px 0;
    padding: 0 15px;
    overflow-y: auto;
`;

const Container = styled.div`
    width: 216px;
    min-height: 425px;
    overflow: hidden;
    background: url(${emulatorBg}) no-repeat 0 0/100%;
`;