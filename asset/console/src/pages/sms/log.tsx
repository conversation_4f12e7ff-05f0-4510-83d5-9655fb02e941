import { Content, Table } from '@topthink/common';
import SmsStatus from '@/components/sms-status';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';


export const Component = function() {

    const [period, setPeriod] = useState('24hours');

    return <Content
        extra={<PeriodButtons period={period} onChange={setPeriod} />}
    >
        <Table
            key={period}
            search={{
                fields: ['phone', 'content', 'status'],
                ui: {
                    status: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '发送成功',
                                    value: 1
                                },
                                {
                                    label: '发送失败',
                                    value: -1
                                }
                            ]
                        }
                    }
                }
            }}
            source={`/sms/log?period=${period}`}
            rowKey={'send_id'}
            columns={[
                {
                    title: '手机号',
                    dataIndex: 'phone',
                    width: 110
                },
                {
                    title: '短信内容',
                    dataIndex: 'content',
                },
                {
                    title: '字数 | 拆分条数',
                    dataIndex: 'content',
                    render: ({ value, record }) => {
                        return `${value.length}字 | ${record.nums}条`;
                    },
                    width: 120
                },
                {
                    title: '发送时间',
                    dataIndex: 'send_time',
                    width: 150
                },
                {
                    title: '送达状态',
                    dataIndex: 'status',
                    width: 90,
                    render({ record }) {
                        return <SmsStatus status={record.status} message={record.message} />;
                    }
                }
            ]}
        />
    </Content>;
}
