import { FormWidgetProps, styled, useOverlayState } from '@topthink/common';
import { useMemo, useRef } from 'react';
import { Button, Col, Overlay, Popover, Row } from 'react-bootstrap';
import icons from './icons';

export default function IconSelector(props: FormWidgetProps) {

    const target = useRef(null);
    const { show, state, hide } = useOverlayState();

    const { value, onChange, rawErrors = [] } = props;

    const className = [];

    rawErrors.length > 0 && className.push('is-invalid');

    const IconComponent = useMemo(() => {
        const icon = icons.find(icon => icon.name === value);
        if (icon) {
            return icon.Component;
        }
    }, [value]);

    return <div className={className.join(' ')}>
        <Button ref={target} variant={'light'} onClick={() => show()}>
            {IconComponent ? <IconComponent width={20} height={20} /> : '选择图标'}
        </Button>
        <Overlay target={target.current} placement='bottom' rootClose={true} {...state}>
            <StyledPopover>
                <Popover.Body>
                    <Row className='g-2' md={5}>
                        {icons.map(({ Component, name }) => {
                            return <Col key={name}>
                                <Cell role='button' onClick={() => {
                                    onChange(name);
                                    hide();
                                }}>
                                    <div className='p-1 py-2 bg-light text-center rounded'>
                                        <Component width={22} height={22} />
                                    </div>
                                </Cell>
                            </Col>;
                        })}
                    </Row>
                </Popover.Body>
            </StyledPopover>
        </Overlay>
    </div>;
}

const Cell = styled.a`
  svg {
    fill: currentColor;
  }
`;

const StyledPopover = styled(Popover)`
  max-width: 300px;
  width: 300px;
`;
