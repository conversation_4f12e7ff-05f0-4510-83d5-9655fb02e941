import { Form, FormType, ModalButton, useRevalidator, useRouteLoaderData } from '@topthink/common';
import { useCallback, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import awaitPayComplete from '@/utils/await-pay-complete';

export default function BuyModal() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { revalidate } = useRevalidator();

    const [type, startDate] = useMemo(() => {

        const now = dayjs();

        const expire = dayjs(site.plan === 'trial' ? undefined : site.expire_time);

        const type = site.plan === 'trial' ? 'buy' : 'renew';

        const startDate = expire.isBefore(now) ? now : expire;

        return [type, startDate];
    }, [site]);

    const [data, setData] = useState({ years: 1 });

    const form = useRef<FormType>(null);

    const handleOk = useCallback(async () => {

        const result = await form.current?.submit();

        if (result) {
            awaitPayComplete({
                result,
                onComplete: () => {
                    revalidate();
                }
            });
            return true;
        }

        return false;
    }, []);

    return <ModalButton size={'sm'} variant={'outline-success'} text={type === 'buy' ? '升级标准版' : '续费标准版'} onOk={handleOk}>
        <Form
            ref={form}
            formContext={{ layout: 'horizontal' }}
            method={'post'}
            action={`/assistant/site/${site.id}/buy`}
            schema={{
                type: 'object',
                properties: {
                    plan: {
                        type: 'string',
                        title: '版本',
                        enum: ['standard'],
                        default: 'standard'
                    },
                    years: {
                        type: 'number',
                        title: '购买时长',
                        enum: [1, 2],
                        default: 1
                    }
                }
            }}
            uiSchema={{
                plan: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['标准版'],
                        button: true
                    }
                },
                years: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['1年', '2年'],
                        button: true
                    }
                }
            }}
            formData={data}
            onChange={e => setData(e.formData)}
        >
            <div className='d-flex'>
                <label className='col-sm-2 col-form-label'>到期日期</label>
                <div className='col-sm-10 d-flex align-items-center'>
                    {startDate.add(data.years, 'years').format('YYYY-MM-DD')}
                </div>
            </div>
            <div className='d-flex'>
                <label className='col-sm-2 col-form-label'>合计</label>
                <div className='col-sm-10 d-flex align-items-center'>
                    <span className='text-danger fs-5'>{199 * data.years} 元</span>
                </div>
            </div>
        </Form>
    </ModalButton>;
}
