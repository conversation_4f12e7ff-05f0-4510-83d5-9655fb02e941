import { Space, useRouteLoaderData } from '@topthink/common';
import { Alert } from 'react-bootstrap';
import BuyModal from '@/pages/assistant/site/buy-modal';
import dayjs from 'dayjs';

export default function Footer() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    let text = '全站广播，多人协作等';

    if (site.plan === 'standard') {
        const expire = dayjs(site.expire_time);
        if (expire.isAfter(dayjs())) {
            text = `标准版 ${expire.fromNow(true)} 后到期`;
        } else {
            text = '标准版已过期';
        }

    }

    return <div className={'p-2'}>
        <Alert variant='success' className={'m-0'}>
            <Space direction='vertical' className={'align-items-center'}>
                <p className='mb-1'>{text}</p>
                <BuyModal />
            </Space>
        </Alert>
    </div>;
}
