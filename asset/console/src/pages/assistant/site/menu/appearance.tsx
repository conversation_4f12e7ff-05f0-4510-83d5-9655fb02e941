import { Card, Form, useLocation, useRevalidator, useRouteLoaderData } from '@topthink/common';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { revalidate } = useRevalidator();
    const location = useLocation();

    return <Card>
        <Form
            key={location.key}
            submitText={'保存'}
            formContext={{ layout: 'horizontal' }}
            method={'put'}
            action={`/assistant/site/${site.id}/appearance`}
            schema={{
                type: 'object',
                properties: {
                    window: {
                        type: 'object',
                        title: '窗口样式',
                        properties: {
                            tone: {
                                type: 'string',
                                title: '主题颜色'
                            },
                            sideMargin: {
                                title: '侧边距',
                                type: 'number'
                            },
                            bottomMargin: {
                                title: '底边距',
                                type: 'number'
                            },
                        }
                    },
                    button: {
                        type: 'object',
                        title: '按钮样式',
                        properties: {
                            color: {
                                type: 'string',
                                title: '图标颜色'
                            },
                            background: {
                                type: 'string',
                                title: '背景颜色'
                            },
                            size: {
                                type: 'string',
                                title: '图标大小',
                                enum: ['small', 'normal', 'large']
                            },
                            sideMargin: {
                                title: '侧边距',
                                type: 'number'
                            },
                            bottomMargin: {
                                title: '底边距',
                                type: 'number'
                            },
                            hidden: {
                                type: 'boolean',
                                title: '隐藏按钮'
                            },
                        }
                    }
                }
            }}
            uiSchema={{
                'ui:col': 6,
                window: {
                    tone: {
                        'ui:widget': 'color'
                    },
                },
                button: {
                    hidden: {
                        'ui:widget': 'checkbox',
                        'ui:help': '隐藏按钮后，可以通过JS调用显示窗口'
                    },
                    color: {
                        'ui:widget': 'color'
                    },
                    background: {
                        'ui:widget': 'color'
                    },
                    size: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            enumNames: ['小', '中', '大'],
                            button: true
                        }
                    }
                }
            }}
            formData={site.appearance}
            onSuccess={() => revalidate()}
        />
    </Card>;
}
