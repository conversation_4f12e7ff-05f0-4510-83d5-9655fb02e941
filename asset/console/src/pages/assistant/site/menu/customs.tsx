import { Card, Form, useLocation, useRevalidator, useRouteLoaderData } from '@topthink/common';
import { ComponentProps, lazy, Suspense } from 'react';

const LazyIconSelector = lazy(() => import('../icon-selector'));

function IconSelector(props: ComponentProps<typeof LazyIconSelector>) {
    return <Suspense fallback={null}>
        <LazyIconSelector {...props} />
    </Suspense>;
}

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { revalidate } = useRevalidator();
    const location = useLocation();

    return <Card>
        <Form
            key={location.key}
            submitText={'保存'}
            formContext={{ layout: 'horizontal' }}
            schema={{
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        icon: {
                            title: '图标',
                            type: 'string'
                        },
                        title: {
                            title: '描述',
                            type: 'string'
                        },
                        type: {
                            title: '类型',
                            type: 'string',
                            enum: ['url', 'image'],
                            default: 'url'
                        },
                    },
                    dependencies: {
                        type: {
                            oneOf: [
                                {
                                    properties: {
                                        type: {
                                            const: 'url'
                                        },
                                        url: {
                                            title: '链接地址',
                                            type: 'string'
                                        }
                                    }
                                },
                                {
                                    properties: {
                                        type: {
                                            const: 'image'
                                        },
                                        image: {
                                            title: '显示图片',
                                            type: 'string'
                                        },
                                        text: {
                                            title: '文字说明',
                                            type: 'string'
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }}
            uiSchema={{
                'ui:col': 6,
                items: {
                    icon: {
                        'ui:widget': IconSelector
                    },
                    type: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            enumNames: ['外链', '图片'],
                            button: true
                        }
                    },
                    image: {
                        'ui:widget': 'upload',
                        'ui:options': {
                            endpoint: '/upload/assistant',
                            accept: 'image/*'
                        }
                    }
                }
            }}
            method={'put'}
            action={`/assistant/site/${site.id}/customs`}
            formData={site.customs}
            onSuccess={() => revalidate()}
        />
    </Card>;
}
