import { Card, Form, useLocation, useRevalidator, useRouteLoaderData } from '@topthink/common';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { revalidate } = useRevalidator();
    const location = useLocation();

    return <Card>
        <Form
            key={location.key}
            submitText={'保存'}
            method={'put'}
            action={`/assistant/site/${site.id}/features`}
            schema={{
                type: 'object',
                properties: {
                    gotop: {
                        title: '返回顶部',
                        type: 'object',
                        properties: {
                            enable: {
                                type: 'boolean',
                                title: '开启'
                            }
                        }
                    },
                    doc: {
                        title: '文档中心',
                        type: 'object',
                        properties: {
                            enable: {
                                type: 'boolean',
                                title: '开启'
                            }
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            url: {
                                                type: 'string',
                                                title: '文档地址'
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    },
                    chat: {
                        title: '在线客服',
                        type: 'object',
                        properties: {
                            enable: {
                                type: 'boolean',
                                title: '开启'
                            }
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            channel: {
                                                title: '客服渠道',
                                                type: 'string',
                                                enum: ['hecong', 'wechat'],
                                                default: 'hecong'
                                            },
                                        },
                                        dependencies: {
                                            channel: {
                                                oneOf: [
                                                    {
                                                        properties: {
                                                            channel: {
                                                                const: 'hecong'
                                                            },
                                                            channelId: {
                                                                type: 'string',
                                                                title: '渠道ID'
                                                            }
                                                        },
                                                    },
                                                    {
                                                        properties: {
                                                            channel: {
                                                                const: 'wechat'
                                                            },
                                                            url: {
                                                                type: 'string',
                                                                title: '接入链接'
                                                            }
                                                        },
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            }}
            uiSchema={{
                'ui:col': 6,
                chat: {
                    channel: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            enumNames: ['合从客服', '微信客服'],
                            button: true
                        }
                    }
                },
                broadcast: {
                    image: {
                        'ui:widget': 'upload',
                        'ui:options': {
                            endpoint: '/upload/assistant',
                            accept: 'image/*'
                        }
                    }
                }
            }}
            formData={site.features}
            onSuccess={() => revalidate()}
        />
    </Card>;
}
