import { Link, styled } from "@topthink/common";
import { useState } from "react";
import { OverlayTrigger, Popover, OverlayTriggerProps } from "react-bootstrap";
import { ReactComponent as BroadcastIcon } from '../../../images/broadcast.svg';
import { ReactComponent as BroadcastOffIcon } from '../../../images/broadcast-off.svg';

export default function Status({ site, className, placement = 'bottom' }: { site: AssistantSite; className?: string; placement?: OverlayTriggerProps['placement']; }) {

    const [show, setShow] = useState(false);

    let popover: OverlayTriggerProps['overlay'];

    switch (site.status) {
        case 1:
            popover = <Popover>
                <Popover.Body>
                    网站已连通
                </Popover.Body>
            </Popover>;
            break;
        case 2:
            popover = <Popover onMouseOver={() => setShow(true)} onMouseLeave={() => setShow(false)}>
                <Popover.Body className='fs-6'>
                    最近24小时未发送任何数据，请检查 SDK 代码是否正常安装。<Link className='link-primary' to={`/assistant/site/${site.id}/setting/tracking`}>查看 SDK 代码</Link>
                </Popover.Body>
            </Popover>;
            break;
        default:
            popover = <Popover onMouseOver={() => setShow(true)} onMouseLeave={() => setShow(false)}>
                <Popover.Body className='fs-6'>
                    网站未连通，请检查 SDK 代码是否正常安装。<Link className='link-primary' to={`/assistant/site/${site.id}/setting/tracking`}>查看 SDK 代码</Link>
                </Popover.Body>
            </Popover>;
    }

    return <OverlayTrigger onToggle={setShow} placement={placement} show={show} overlay={popover}>
        <Container className={className}>
            {site.status === 1 ? <BroadcastIcon /> : <BroadcastOffIcon />}
        </Container>
    </OverlayTrigger>;
}

const Container = styled.div`
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 0.375rem;

    &:hover {
        background-color: rgb(238, 238, 238);
    }
`;