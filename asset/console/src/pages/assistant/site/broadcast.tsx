import { Card, Content, Form, useRevalidator, useRouteLoaderData } from '@topthink/common';
import { Alert } from 'react-bootstrap';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { revalidate } = useRevalidator();
    return <Content>
        {!site.is_standard && <Alert variant={'warning'}>此功能仅<strong>标准版</strong>可用</Alert>}
        <Card>
            <Form
                submitText={'保存'}
                method={'put'}
                action={`/assistant/site/${site.id}/broadcast`}
                schema={{
                    type: 'object',
                    properties: {
                        enable: {
                            type: 'boolean',
                            title: '开启',
                        },
                    },
                    dependencies: {
                        enable: {
                            oneOf: [
                                {
                                    properties: {
                                        enable: {
                                            const: true
                                        },
                                        image: {
                                            type: 'string',
                                            title: '广播图片'
                                        },
                                        url: {
                                            type: 'string',
                                            title: '跳转链接'
                                        },
                                        expire_time: {
                                            type: 'string',
                                            title: '过期时间',
                                            description: '留空则永不过期'
                                        },
                                        position: {
                                            type: 'string',
                                            title: '显示位置',
                                            enum: ['bottom-right', 'bottom'],
                                            default: 'bottom-right'
                                        },
                                    },
                                    dependencies: {
                                        position: {
                                            oneOf: [
                                                {
                                                    properties: {
                                                        position: {
                                                            enum: ['bottom']
                                                        },
                                                        height: {
                                                            type: 'string',
                                                            title: '高度',
                                                            default: 100
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }}
                uiSchema={{
                    'ui:order': ['*', 'position', 'height'],
                    'ui:col': 6,
                    enable: {
                        'ui:help': '在网站显示一个可关闭的图片弹窗'
                    },
                    position: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            enumNames: ['底部靠右', '铺满底部'],
                            button: true
                        }
                    },
                    image: {
                        'ui:widget': 'upload',
                        'ui:options': {
                            endpoint: '/upload/assistant',
                            accept: 'image/*'
                        }
                    },
                    expire_time: {
                        'ui:widget': 'datetime'
                    },
                }}
                formData={site.broadcast}
                onSuccess={() => revalidate()}
            />
        </Card>
    </Content>;
}
