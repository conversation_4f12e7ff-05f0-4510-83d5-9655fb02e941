import { useRouteLoaderData, To<PERSON>, Card } from '@topthink/common';
import { Button } from 'react-bootstrap';
import copy from 'copy-to-clipboard';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    return <Card>
        <p className='text-muted'>请将以下代码放置在网站的 <code>{'</head>'}</code> 标签之前</p>
        <pre className='border p-3 rounded bg-light'><code>{site.code}</code></pre>
        <Button
            variant='primary'
            onClick={() => {
                copy(site.code);
                Toast.success('代码已复制');
            }}
        >复制代码</Button>
    </Card>;
}
