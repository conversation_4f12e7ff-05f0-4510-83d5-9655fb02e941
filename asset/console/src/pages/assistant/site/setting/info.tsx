import {
    Card,
    Form,
    RequestButton,
    useLocation,
    useNavigate,
    useRevalidator,
    useRouteLoaderData
} from '@topthink/common';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const location = useLocation();
    const { revalidate } = useRevalidator();
    const navigate = useNavigate();

    return <>
        <Card>
            <Form
                key={location.key}
                method={'put'}
                action={`/assistant/site/${site.id}`}
                schema={{
                    type: 'object',
                    required: [
                        'name',
                        'url'
                    ],
                    properties: {
                        name: {
                            title: '网站名称',
                            type: 'string',
                        },
                        url: {
                            title: '网站地址',
                            type: 'string',
                        },
                    }
                }}
                formData={{
                    name: site.name,
                    url: site.url
                }}
                submitText={'保存'}
                onSuccess={() => revalidate()}
            />
        </Card>
        {site.pivot.access_level == 60 && <Card title={'删除站点'}>
            <p className='text-muted'>站点删除后，所有数据将无法恢复，请谨慎操作。</p>
            <RequestButton
                confirm='确定要删除吗？'
                url={`/assistant/site/${site.id}`}
                method={'delete'}
                variant={'outline-danger'}
                onSuccess={() => navigate('/assistant/site')}
            >删除</RequestButton>
        </Card>}
    </>;
}
