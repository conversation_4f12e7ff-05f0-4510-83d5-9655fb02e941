import { Card, Form, useLocation, useRevalidator, useRouteLoaderData } from '@topthink/common';
import { Alert } from 'react-bootstrap';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const location = useLocation();
    const { revalidate } = useRevalidator();

    return <>
        {!site.is_standard && <Alert variant={'warning'}>此功能仅<strong>标准版</strong>可用</Alert>}
        <Card>
            <Form
                key={location.key}
                method={'put'}
                action={`/assistant/site/${site.id}/scripts`}
                schema={{
                    title: '自定义脚本',
                    type: 'string',
                }}
                uiSchema={{
                    'ui:widget': 'textarea',
                    'ui:options': {
                        rows: 5,
                        label: false
                    },
                    'ui:help': '自定义脚本将在网站页面加载完成后执行，可用于统计代码、广告代码等'
                }}
                formData={site.scripts}
                submitText={'保存'}
                onSuccess={() => revalidate()}
            />
        </Card>
    </>;
}
