import { Content, RequestButton, Table, useRouteLoaderData, Message, Space } from '@topthink/common';
import copy from 'copy-to-clipboard';
import { Alert } from 'react-bootstrap';

export const Component = function() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    return <Content>
        {!site.is_standard && <Alert variant={'warning'}>此功能仅<strong>标准版</strong>可用</Alert>}
        <Table
            toolBarRender={() => {
                return <RequestButton
                    url={`/assistant/site/${site.id}/member/invite`}
                    method={'post'}
                    onSuccess={(result) => {

                        const url = location.origin + `/assistant/site/invite/${result.code}`;

                        Message.success({
                            title: '邀请链接已创建',
                            html: `<p>${url}</p><p class='text-muted'>链接有效时长为 30 分钟，仅可使用一次</p>`,
                            timer: undefined,
                            showConfirmButton: true,
                            toast: false,
                            position: 'center',
                            showCancelButton: true,
                            allowOutsideClick: false,
                            confirmButtonText: '复制链接',
                            cancelButtonText: '关闭',
                            preConfirm() {
                                copy(url);
                                Message.success({ title: '邀请链接已复制至剪贴板' });
                            }
                        });
                    }}
                >邀请成员</RequestButton>;
            }}
            source={`/assistant/site/${site.id}/member`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '账号',
                    render({ record }) {
                        return <Space>
                            <img className='rounded' width={24} height={24} src={record.avatar} />
                            {record.name}
                        </Space>;
                    }
                },
                {
                    dataIndex: ['pivot', 'create_time'],
                    title: '加入时间'
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right'
                }
            ]}
        />
    </Content>;
}
