import { Card, Content, Loader, RequestButton, Result, useNavigate, useParams, useRequest } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export const Component = function() {
    const { code } = useParams();
    const navigate = useNavigate();

    const { result, error } = useRequest(`/assistant/site/invite/${code}`);

    if (error) {
        return <Content>
            <Card>
                <Result status={'error'} title={'邀请链接不存在或已过期'} />
            </Card>
        </Content>;
    }

    if (!result) {
        return <Loader />;
    }

    const { user, site } = result;

    return <Content>
        <Row>
            <Col lg={{ span: 6, offset: 3 }}>
                <Card>
                    <p className='card-text text-muted mb-5'>
                        <strong className='me-2'>{user.name}</strong>
                        邀请您加入网站助理
                    </p>
                    <h5 className='card-title text-center mb-5'>{site.name}</h5>
                    <div className='d-grid'>
                        <RequestButton
                            url={`/assistant/site/invite/${code}`}
                            method={'post'}
                            onSuccess={() => {
                                requestAnimationFrame(() => {
                                    navigate(`/assistant/site/${site.id}`, { replace: true });
                                });
                            }}
                        >接受邀请</RequestButton>
                    </div>
                </Card>
            </Col>
        </Row>
    </Content>;
}
