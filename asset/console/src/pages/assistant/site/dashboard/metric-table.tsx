import { request, styled, useAsync, useRouteLoaderData } from '@topthink/common';
import { formatLongNumber } from '@/utils/format';

interface Props {
    title: string;
    metric: string;
    type?: string;
    period: string;
    renderLabel?: (label: string) => string;
    source?: any[];
    limit?: number;
    className?: string;
}

export default function MetricTable({
    period,
    type,
    title,
    metric,
    renderLabel,
    source,
    className,
    limit = 10
}: Props) {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const { result } = useAsync(async () => {
        if (source) {
            return source;
        }

        return await request<AssistantSiteMetric[]>({
            url: `/assistant/site/${site.id}/tracer/metrics`,
            params: { period, type }
        });
    }, [period]);

    if (!result) {
        return null;
    }

    const total = result.reduce((n, { y }) => n + Number(y), 0);

    return <Container className={className}>
        <Header>
            <Row>
                <Label>{title}</Label>
                <Metric>{metric}</Metric>
            </Row>
        </Header>
        <Body>
            {result.slice(0, limit).map((item: any) => {
                return <Row key={item.x}>
                    <Label>{renderLabel ? renderLabel(item.x) : item.x}</Label>
                    <Metric>
                        <Value>{formatLongNumber(item.y)}</Value>
                        <Progress now={((item.y / total) * 100).toFixed(0)} />
                    </Metric>
                </Row>;
            })}
        </Body>
    </Container>;
}

const Metric = styled.div`
  justify-content: center;
  display: flex;
`;

const Value = styled.span`
  width: 50px;
  text-align: right;
  margin-right: 10px;
  font-weight: bold;
`;

const Progress = styled.div<{ now: string }>`
  width: 50px;
  text-align: center;
  border-left: 1px solid var(--bs-gray-500);
  position: relative;

  &:after {
    color: var(--bs-gray-500);
    content: "${props => props.now}%";
  }

  &:before {
    width: ${props => props.now}%;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    opacity: .1;
    background: var(--bs-primary);
  }
`;

const Row = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 10px;
  line-height: 30px;
`;

const Body = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;
const Header = styled.div`
  font-weight: bold;

  ${Row} {
    line-height: 40px;
  }

  ${Metric} {
    width: 100px;
  }
`;
const Label = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const Container = styled.div`

`;
