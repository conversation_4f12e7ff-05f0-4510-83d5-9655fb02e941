import { Card, useRequest, useRouteLoaderData } from '@topthink/common';
import { Column, ColumnConfig } from '@ant-design/plots';
import dayjs from 'dayjs';

export default function PageviewsChart({ period }: { period: string }) {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const { result } = useRequest({
        url: `/assistant/site/${site.id}/tracer/pageviews`,
        params: { period }
    }, { refreshDeps: [period] });

    if (!result) {
        return null;
    }

    const data = [
        ...result.pageviews.map((item: any) => ({
            ...item,
            type: 'pageviews'
        })),
        ...result.sessions.map((item: any) => ({
            ...item,
            type: 'sessions'
        }))
    ];

    const config: ColumnConfig = {
        data,
        isStack: false,
        xField: 'x',
        yField: 'y',
        seriesField: 'type',
        appendPadding: [30, 0, 20, 0],
        color: ['#a8ccf7', '#5a9ef0'],
        xAxis: {
            label: {
                formatter(text) {
                    const date = dayjs(text);

                    switch (period) {
                        case '90days':
                        case '30days':
                            return date.format('YYYY-MM-DD');
                        case '7days':
                            return date.format('MM-DD HH:mm');
                        case '24hours':
                        default:
                            return date.format('HH:mm');
                    }

                }
            },
        },
        tooltip: {
            formatter: (datum) => {
                const name = datum.type === 'pageviews' ? '页面浏览量' : '独立访客';

                return {
                    name: name,
                    value: datum.y,
                };
            }
        },
        legend: {
            position: 'bottom',
            itemName: {
                formatter(_, item) {
                    return item.name === 'pageviews' ? '页面浏览量' : '独立访客';
                }
            },
            offsetY: 5
        }
    };

    return <Card>
        <Column {...config} />
    </Card>;
}
