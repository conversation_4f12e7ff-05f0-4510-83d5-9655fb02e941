import { Card, useRequest, useRouteLoaderData } from '@topthink/common';
import { ChoroplethMap, ChoroplethMapConfig } from '@ant-design/maps';
import area from './area.json';
import { Col, Row } from 'react-bootstrap';
import MetricTable from '@/pages/assistant/site/dashboard/metric-table';

type Area = typeof area;
type AreaCode = keyof Area;

export default function MapChart({ period }: { period: string }) {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const { result } = useRequest<AssistantSiteMetric & { adcode: number, x: AreaCode }[]>({
        url: `/assistant/site/${site.id}/tracer/metrics`,
        params: { period, type: 'province' }
    }, { refreshDeps: [period] });

    if (!result) {
        return null;
    }

    const config: ChoroplethMapConfig = {
        map: {
            type: 'mapbox',
            style: 'blank'
        },
        geoArea: {
            url: 'https://jsdelivr.topthink.com/npm/static-geo-atlas@0.0.2/geo-data/choropleth-data',
            type: 'geojson'
        },
        source: {
            data: result.map((item) => {
                item.adcode = area[item.x]?.adcode;
                return item;
            }),
            joinBy: {
                sourceField: 'adcode',
                geoField: 'adcode'
            },
        },
        viewLevel: {
            level: 'country',
            adcode: 100000,
        },
        autoFit: true,
        color: {
            field: 'y',
            value: ['#B8E1FF', '#7DAAFF', '#3D76DD', '#0047A5', '#001D70'],
            scale: { type: 'quantile' }
        },
        style: {
            opacity: 1,
            stroke: '#ccc',
            lineWidth: 0.3,
            lineOpacity: 1,
        },
        chinaBorder: false,
        state: {
            active: {
                fill: '#001D70',
                lineWidth: 0.3,
                lineOpacity: 1,
            }
        },
        tooltip: {
            items: [
                {
                    field: 'y',
                    alias: '访客',
                }
            ],
            customTitle(data) {
                return data.name;
            },
        },
        legend: {
            position: 'bottomleft',
        },
    };

    return <Card>
        <Row>
            <Col md={8}>
                <div className={'flex-fill'} style={{ height: 500 }}>
                    <ChoroplethMap {...config} />
                </div>
            </Col>
            <Col md={4} className='d-flex'>
                <MetricTable
                    className={'flex-fill'}
                    title={'地区'}
                    metric={'访客'}
                    source={result}
                    period={period}
                    renderLabel={(item) => area[item as AreaCode]?.name || '未知'}
                />
            </Col>
        </Row>
    </Card>;
}
