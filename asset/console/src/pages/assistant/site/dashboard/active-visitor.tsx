import { Space, useRequest, useRouteLoaderData } from '@topthink/common';

export default function ActiveVisitor() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { result } = useRequest(`/assistant/site/${site.id}/tracer/active`);
    if (result) {
        return <Space size={'small'}>
            <i className={'bi bi-circle-fill fs-7 text-success'} />
            当前在线 {String(result.active)} 人
        </Space>;
    }
    return null;
}
