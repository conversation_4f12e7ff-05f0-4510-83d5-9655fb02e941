import { Badge, Col, Row } from 'react-bootstrap';
import Statistic, { StatisticProps } from '@/components/statistic';
import { useRequest, useRouteLoaderData } from '@topthink/common';
import { formatLongNumber, formatNumber, formatShortTime } from '@/utils/format';
import classNames from 'classnames';

interface MetricCardProps extends StatisticProps {
    format?: Function;
    change: number;
    reverseColor?: boolean;
}

const MetricCard = ({ format = formatNumber, reverseColor = false, change, title, content }: MetricCardProps) => {

    content = format(content);

    if (change) {
        let success = change > 0;
        if (reverseColor) {
            success = !success;
        }

        title = <div className='d-flex align-items-center'>
            <span className='flex-fill'>{title}</span>
            <Badge className={classNames('bg-opacity-25 fs-7', {
                'bg-success text-success': success,
                'bg-danger text-danger': !success
            })}>
                {change > 0 ? '+' : '-'}
                {format(change)}
            </Badge>
        </div>;
    }

    return <Statistic title={title} content={content} />;
};

const formatFunc = (n: number) => formatLongNumber(Math.abs(n));

export default function Stats({ period }: { period: string }) {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const { result } = useRequest({
        url: `/assistant/site/${site.id}/tracer/stats`,
        params: { period }
    }, { refreshDeps: [period] });

    if (!result) {
        return null;
    }

    let { pageviews, uniques, bounces, totaltime } = result;

    const num = Math.min(uniques.value, bounces.value);
    const diffs = {
        pageviews: pageviews.value - pageviews.change,
        uniques: uniques.value - uniques.change,
        bounces: bounces.value - bounces.change,
        totaltime: totaltime.value - totaltime.change,
    };

    return <Row className='g-3 mb-3'>
        <Col>
            <MetricCard
                title={'浏览量'}
                content={pageviews.value}
                change={pageviews.change}
                format={formatFunc}
            />
        </Col>
        <Col>
            <MetricCard
                title={'访客'}
                content={uniques.value}
                change={uniques.change}
                format={formatFunc}
            />
        </Col>
        <Col>
            <MetricCard
                title={'跳出率'}
                content={uniques.value ? (num / uniques.value) * 100 : 0}
                change={
                    uniques.value && uniques.change
                        ? (num / uniques.value) * 100 -
                        (Math.min(diffs.uniques, diffs.bounces) / diffs.uniques) * 100 || 0
                        : 0
                }
                format={(n: number) => Math.abs(n).toFixed(0) + '%'}
                reverseColor
            />
        </Col>
        <Col>
            <MetricCard
                title={'平均访问时长'}
                content={totaltime.value && pageviews.value
                    ? totaltime.value / (pageviews.value - bounces.value)
                    : 0}
                change={
                    totaltime.value && pageviews.value
                        ? (diffs.totaltime / (diffs.pageviews - diffs.bounces) -
                            totaltime.value / (pageviews.value - bounces.value)) *
                        -1 || 0
                        : 0
                }
                format={(n: number) => `${formatShortTime(Math.abs(n), ['m', 's'], ' ')}`}
            />
        </Col>
    </Row>;
}
