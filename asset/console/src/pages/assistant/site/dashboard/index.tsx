import PeriodButtons from '@/components/period-buttons';
import { Card, Content, Loader, Space, Tooltip, useRouteLoaderData } from '@topthink/common';
import { Suspense, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import ActiveVisitor from './active-visitor';
import MapChart from './charts/map-chart';
import PageviewsChart from './charts/pageviews-chart';
import MetricTable from './metric-table';
import Stats from './stats';


export const Component = function() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const [period, setPeriod] = useState('24hours');

    let periodButtons = <PeriodButtons
        period={period}
        onChange={setPeriod}
        getButtonProps={(period) => {
            if (!site.is_standard && period !== '24hours') {
                return {
                    disabled: true
                };
            }
            return {};
        }}
    />;

    if (!site.is_standard) {
        periodButtons = <Tooltip tooltip={'当前套餐为 [免费版] ,只能查看24小时内的数据'}>
            <div>{periodButtons}</div>
        </Tooltip>;
    }

    return <Content
        extra={<Space size={'large'}>
            <ActiveVisitor />
            {periodButtons}
        </Space>}
    >
        <Stats period={period} />
        <Suspense fallback={<Loader />}>
            <PageviewsChart period={period} />
            <Row className={'g-3 mb-3'}>
                <Col md={6} className='d-flex'>
                    <Card className='flex-fill'>
                        <MetricTable
                            title={'网页'}
                            metric={'浏览量'}
                            type={'url'}
                            period={period}
                        />
                    </Card>
                </Col>
                <Col md={6} className='d-flex'>
                    <Card className='flex-fill'>
                        <MetricTable
                            title={'来源域名'}
                            metric={'浏览量'}
                            type={'referrer'}
                            period={period}
                            renderLabel={(label) => {
                                return label || '直接访问';
                            }}
                        />
                    </Card>
                </Col>
            </Row>
            <Row className={'g-3 mb-3'}>
                <Col md={4} className='d-flex'>
                    <Card className='flex-fill'>
                        <MetricTable
                            title={'浏览器'}
                            metric={'访客'}
                            type={'browser'}
                            period={period}
                            renderLabel={(label) => {
                                return label || '未知';
                            }}
                        />
                    </Card>
                </Col>
                <Col md={4} className='d-flex'>
                    <Card className='flex-fill'>
                        <MetricTable
                            title={'操作系统'}
                            metric={'访客'}
                            type={'os'}
                            period={period}
                            renderLabel={(label) => {
                                return label || '未知';
                            }}
                        />
                    </Card>
                </Col>
                <Col md={4} className='d-flex'>
                    <Card className='flex-fill'>
                        <MetricTable
                            title={'屏幕'}
                            metric={'访客'}
                            type={'screen'}
                            period={period}
                            renderLabel={(label) => {
                                return label || '未知';
                            }}
                        />
                    </Card>
                </Col>
            </Row>
            <MapChart period={period} />
        </Suspense>
    </Content>;
};
