import { formatLongNumber } from '@/utils/format';
import { Columns, Content, FormSchema, Link, ModalForm, Space, Table } from '@topthink/common';
import classNames from 'classnames';
import dayjs from 'dayjs';
import Status from './status';

export const Component = function() {

    const columns: Columns = [
        {
            title: '名称',
            align: 'left',
            dataIndex: 'name',
            width: 120,
            render({ record }) {
                return <Space direction='vertical' size={5}>
                    <Link className='link-primary text-truncate d-block' to={`/assistant/site/${record.id}`}>{record.name}</Link>
                    <span className='fs-7 text-muted'>{record.hash_id}</span>
                </Space>;
            }
        },
        {
            title: '地址',
            align: 'left',
            dataIndex: 'url',
            render({ value }) {
                return <a className={'text-truncate d-block'} href={value} target={'_blank'}>{value}</a>;
            }
        },
        {
            title: '',
            key: 'day',
            width: 120,
            align: 'center',
            render() {
                return <Space className='border-start' direction='vertical' size={5}>
                    <span className='fs-7'>今日</span>
                    <span className='fs-7'>昨日</span>
                </Space>;
            }
        },
        {
            title: '浏览量',
            key: 'pv',
            width: 120,
            align: 'center',
            render({ record }) {
                return <Space direction='vertical' size={5}>
                    <span className='fs-7'>{formatLongNumber(record.stats.today.pageviews)}</span>
                    <span className='fs-7'>{formatLongNumber(record.stats.yesterday.pageviews)}</span>
                </Space>;
            }
        },
        {
            title: '访客',
            key: 'uv',
            width: 120,
            align: 'center',
            render({ record }) {
                return <Space direction='vertical' size={5}>
                    <span className='fs-7'>{formatLongNumber(record.stats.today.uniques)}</span>
                    <span className='fs-7'>{formatLongNumber(record.stats.yesterday.uniques)}</span>
                </Space>;
            }
        },
        {
            title: '版本',
            width: 120,
            align: 'center',
            dataIndex: 'plan',
            render({ value, record }) {
                switch (value) {
                    case 'trial':
                        if (record.expire_time) {
                            const time = dayjs(record.expire_time);
                            const expired = time.isBefore(dayjs());
                            if (!expired) {
                                return <Space direction='vertical' size={5}>
                                    试用版
                                    <span className={classNames('fs-7', expired ? 'text-danger' : 'text-success')}>{time.format('YYYY-MM-DD')}</span>
                                </Space>;
                            }
                        }
                        return '免费版';
                    case 'standard':
                        const time = dayjs(record.expire_time);
                        const expired = time.isBefore(dayjs());
                        return <Space direction='vertical' size={5}>
                            标准版
                            <span className={classNames('fs-7', expired ? 'text-danger' : 'text-success')}>{time.format('YYYY-MM-DD')}</span>
                        </Space>;
                    default:
                        return '未知';
                }
            }
        },
        {
            title: '状态',
            width: 80,
            align: 'center',
            render({ record }) {
                return <div className='w-100 d-flex justify-content-center'><Status site={record} /></div>;
            }
        },
        {
            title: '操作',
            width: 150,
            align: 'right',
            render({ record }) {
                return <Link className='link-primary' to={`/assistant/site/${record.id}`}>进入</Link>;
            }
        }
    ];

    const schema: FormSchema = {
        type: 'object',
        required: [
            'name',
            'url'
        ],
        properties: {
            name: {
                title: '网站名称',
                type: 'string',
            },
            url: {
                title: '网站地址',
                type: 'string',
            },
        }
    };

    return <Content title='网站列表'>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    action={'/assistant/site'}
                    method={'post'}
                    onSuccess={reload}
                    schema={schema}
                    uiSchema={{
                        name: {
                            'ui:autofocus': true
                        }
                    }}
                    text={'新增网站'}
                />;
            }}
            tableLayout={'fixed'}
            columns={columns}
            source={'/assistant/site'}
        />
    </Content>;
}
