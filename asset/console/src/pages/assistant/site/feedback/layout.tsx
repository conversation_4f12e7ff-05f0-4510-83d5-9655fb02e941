import {
    styled,
    TabLayout,
    useLoaderData,
    useRouteLoaderData,
    Message,
    request,
    showRequestError, useRevalidator
} from '@topthink/common';
import Switch from 'rc-switch';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const data = useLoaderData() as AssistantSiteFeedback;
    const { revalidate } = useRevalidator();
    const extra = <StyledSwitch
        checked={data.status === 1}
        onChange={async (checked) => {
            if (checked && !await Message.confirm({ text: '发布后，该评价将出现在您的应用上并回收数据，建议再次检查评价相关投放设置。确认发布吗？' })) {
                return;
            }
            try {
                await request({
                    url: `/assistant/site/${site.id}/feedback/${data.id}/status`,
                    method: 'post',
                    data: {
                        value: checked ? 1 : 0
                    }
                });
                revalidate();
            } catch (e) {
                showRequestError(e);
            }
        }}
        checkedChildren='已发布'
        unCheckedChildren='未发布'
    />;

    return <TabLayout
        title={data.name}
        showBack
        extra={extra}
    />;
}


const StyledSwitch = styled(Switch)`
  width: 84px;
  height: 28px;

  .rc-switch-inner-checked, .rc-switch-inner-unchecked {
    font-size: 14px;
    top: 3px;
  }

  .rc-switch-inner-checked {
    left: -42px;
  }

  .rc-switch-inner-unchecked {
    left: 29px;
  }

  &:after {
    width: 22px;
    height: 22px;
    top: 2px;
  }

  &.rc-switch-checked {
    &:after {
      left: 58px;
    }

    .rc-switch-inner-checked {
      left: 11px;
    }

    .rc-switch-inner-unchecked {
      left: 84px;
    }
  }
`;
