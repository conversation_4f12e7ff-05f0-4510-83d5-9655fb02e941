import { Content, Link, RequestButton, Space, Table, useNavigate, useRouteLoaderData } from '@topthink/common';
import { Alert, Badge } from 'react-bootstrap';
import Rate from '@/components/rate';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const navigate = useNavigate();

    return <Content>
        {!site.is_standard && <Alert variant={'warning'}>此功能仅<strong>标准版</strong>可用</Alert>}
        <Table
            toolBarRender={() => [
                <RequestButton
                    key='add'
                    url={`/assistant/site/${site.id}/feedback`}
                    method='post'
                    variant='primary'
                    onSuccess={(result) => navigate(`/assistant/site/${site.id}/feedback/${result.id}/question`)}
                >新建</RequestButton>
            ]}
            source={`/assistant/site/${site.id}/feedback`}
            columns={[
                {
                    title: '名称',
                    dataIndex: 'name',
                    render: ({ value, record }) => {
                        return <Link to={`/assistant/site/${site.id}/feedback/${record.id}`}>{value}</Link>;
                    }
                },
                {
                    title: '评分',
                    dataIndex: 'entries_avg',
                    width: 130,
                    render({ value }) {
                        return <Space size={3}>
                            <Rate value={value} disabled />
                            {value && Number(value).toFixed(1)}
                        </Space>;
                    }
                },
                {
                    title: '评价数',
                    dataIndex: 'entries_count',
                    align: 'center',
                    width: 100,
                },
                {
                    title: '创建时间',
                    width: 150,
                    dataIndex: 'create_time',
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    align: 'center',
                    width: 100,
                    render: ({ value }) => {
                        return <Badge pill bg={value === 1 ? 'success' : 'secondary'}>{value === 1 ? '已发布' : '未发布'}</Badge>;
                    }
                }
            ]}
        />
    </Content>;
}
