import Rate from '@/components/rate';
import { Table, useRouteLoaderData } from '@topthink/common';
import ImageZoom from '@/components/image-zoom';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const feedback = useRouteLoaderData('assistant/site/feedback') as AssistantSiteFeedback;

    return <Table
        source={`/assistant/site/${site.id}/feedback/${feedback.id}/entry`}
        columns={[
            {
                title: '评分',
                dataIndex: 'star',
                width: 130,
                render({ value }) {
                    return <Rate value={value} disabled />;
                }
            },
            {
                title: '反馈',
                dataIndex: 'reason',
            },
            {
                title: '截屏',
                dataIndex: 'screenshot',
                render({ value }) {
                    if (value) {
                        return <ImageZoom>
                            <img src={value} height={20} />
                        </ImageZoom>;
                    }
                }
            },
            {
                title: '浏览器',
                dataIndex: 'browser',
            },
            {
                title: '操作系统',
                dataIndex: 'os',
            },
            {
                title: '提交时间',
                dataIndex: 'create_time',
                width: 150,
            },
        ]}
    />;
}
