import { Card, Loader, styled, useRequest, useRouteLoaderData } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';
import Rate from '@/components/rate';
import dayjs from 'dayjs';

function ProcessBar({ label, value }: { label: string, value: number }) {
    return <div className='d-flex align-items-center w-100'>
        <span className='me-3 text-muted'>{label}</span>
        <div className='progress flex-fill' style={{ height: '.5rem' }}>
            <div className='progress-bar bg-orange' style={{ width: `${value}%` }}></div>
        </div>
    </div>;
}

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const feedback = useRouteLoaderData('assistant/site/feedback') as AssistantSiteFeedback;

    const { result } = useRequest(`/assistant/site/${site.id}/feedback/${feedback.id}/stats`);

    if (!result) return <Loader />;

    return <Card>
        <Row className='py-3'>
            <Col md={3} className='d-flex align-items-center'>
                <div className='d-flex gap-3 align-items-center p-2'>
                    <StarAvg>{result.star.toFixed(1)}</StarAvg>
                    <div className='d-flex flex-column'>
                        <Rate size='lg' value={Number(result.star.toFixed(1))} allowHalf disabled />
                        <div className='text-muted'>{result.total} 条评价</div>
                    </div>
                </div>
            </Col>
            <Col md={9}>
                <div className='d-flex flex-column align-items-center'>
                    <ProcessBar label='5星' value={result.stars.star5} />
                    <ProcessBar label='4星' value={result.stars.star4} />
                    <ProcessBar label='3星' value={result.stars.star3} />
                    <ProcessBar label='2星' value={result.stars.star2} />
                    <ProcessBar label='1星' value={result.stars.star1} />
                </div>
            </Col>
        </Row>
        {result.list.length > 0 && <Row className='mt-4'>
            {result.list.map((item: any) => {
                return <Col key={item.id} className='d-flex' md={3}>
                    <div className='bg-light p-3 rounded-2 flex-fill'>
                        <div className={'d-flex justify-content-between align-items-center mb-2'}>
                            <Rate size={'sm'} value={item.star} disabled />
                            <small className='text-muted'>{dayjs(item.create_time).format('YYYY-MM-DD')}</small>
                        </div>
                        <div>{item.reason}</div>
                    </div>
                </Col>;
            })}
        </Row>}
    </Card>;
}

const StarAvg = styled.div`
  font-size: 3.75rem;
  font-weight: bold;
`;
