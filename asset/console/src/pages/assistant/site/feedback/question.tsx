import { Card, Form, useRevalidator, useRouteLoaderData } from '@topthink/common';

export const Component = function() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const feedback = useRouteLoaderData('assistant/site/feedback') as AssistantSiteFeedback;
    const { revalidate } = useRevalidator();

    return <Card>
        <Form
            action={`/assistant/site/${site.id}/feedback/${feedback.id}/question`}
            method={'post'}
            formData={feedback.question}
            schema={{
                type: 'object',
                properties: {
                    title: {
                        type: 'string',
                        title: '评价标题',
                        default: '您如何评价当前的体验？'
                    },
                    reason: {
                        type: 'object',
                        properties: {
                            enable: {
                                type: 'boolean',
                                title: '在评价后邀请用户填写更多反馈',
                                default: true
                            }
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            text: {
                                                type: 'string',
                                                default: '我们会珍惜你留下的每一个反馈和建议',
                                            },
                                            screenshot: {
                                                type: 'boolean',
                                                title: '在评价后允许用户截图',
                                                default: false
                                            }
                                        },
                                    }
                                ]
                            }
                        }
                    },
                    complete: {
                        type: 'string',
                        title: '用户提交评价后，显示文案',
                        default: '感谢您的评价！'
                    }
                }
            }}
            uiSchema={{
                reason: {
                    'ui:label': false,
                    text: {
                        'ui:label': false
                    }
                },
                complete: {
                    'ui:widget': 'textarea',
                }
            }}
            submitText={'保存'}
            onSuccess={() => revalidate()}
        />
    </Card>;
}
