import { Card, Form, useRevalidator, useRouteLoaderData } from '@topthink/common';

export const Component = function() {

    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const feedback = useRouteLoaderData('assistant/site/feedback') as AssistantSiteFeedback;
    const { revalidate } = useRevalidator();

    return <Card>
        <Form
            action={`/assistant/site/${site.id}/feedback/${feedback.id}/distribution`}
            method={'post'}
            formData={feedback.distribution}
            schema={{
                type: 'object',
                properties: {
                    timing: {
                        type: 'object',
                        title: '弹出时机',
                        properties: {
                            handle: {
                                title: '访问页面后，何时自动弹出',
                                type: 'string',
                                enum: ['click', 'visit', 'delay'],
                                enumNames: ['仅点击时弹出', '访问时自动弹出', '在页面停留一段时间后自动弹出'],
                                default: 'visit'
                            },
                        },
                        dependencies: {
                            handle: {
                                oneOf: [
                                    {
                                        properties: {
                                            handle: {
                                                const: 'delay'
                                            },
                                            delay: {
                                                type: 'number',
                                                title: '停留时长(秒)',
                                                default: 30
                                            }
                                        },
                                    },
                                ]
                            }
                        }
                    },
                    strategy: {
                        type: 'object',
                        title: '弹出策略',
                        description: '仅在弹出时机为“自动弹出”时生效',
                        properties: {
                            enable: {
                                title: '是否开启弹出策略',
                                type: 'boolean',
                                enumNames: ['开启', '关闭'],
                                default: true
                            }
                        },
                        dependencies: {
                            enable: {
                                oneOf: [
                                    {
                                        properties: {
                                            enable: {
                                                const: true
                                            },
                                            rate: {
                                                type: 'string',
                                                title: '制定弹出策略',
                                                description: '提交一次数据后，不再自动弹出问卷',
                                                enum: ['mild', 'moderate', 'radical'],
                                                enumNames: [
                                                    '温和：每用户总共弹出 1 次，无论是否填写',
                                                    '适中：每用户总共弹出 7 次，每次间隔 3 天',
                                                    '激进：每用户总共弹出次数不限，每次间隔 1 天'
                                                ],
                                                default: 'mild'
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    }
                }
            }}
            uiSchema={{
                timing: {
                    handle: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            inline: false
                        }
                    },
                },
                strategy: {
                    enable: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            inline: false
                        }
                    },
                    rate: {
                        'ui:widget': 'radio',
                        'ui:options': {
                            inline: false
                        }
                    }
                }
            }}
            submitText={'保存'}
            onSuccess={() => revalidate()}
        />
    </Card>;
}
