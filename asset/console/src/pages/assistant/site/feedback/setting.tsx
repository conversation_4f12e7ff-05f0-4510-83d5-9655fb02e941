import { Card, Form, RequestButton, useNavigate, useRevalidator, useRouteLoaderData } from '@topthink/common';

export const Component = function() {
    const site = useRouteLoaderData('assistant/site') as AssistantSite;
    const navigate = useNavigate();
    const { revalidate } = useRevalidator();
    const feedback = useRouteLoaderData('assistant/site/feedback') as AssistantSiteFeedback;

    return <>
        <Card title='基本信息'>
            <Form
                method={'put'}
                action={`/assistant/site/${site.id}/feedback/${feedback.id}`}
                onSuccess={() => revalidate()}
                formData={{
                    name: feedback.name
                }}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            type: 'string',
                            title: '名称'
                        }
                    }
                }}
                submitText={'保存'}
            />
        </Card>
        {site.pivot.access_level == 60 && <Card title={'删除评价'}>
            <p className='text-muted'>站点评价删除后，所有数据将无法恢复，请谨慎操作。</p>
            <RequestButton
                confirm='确定要删除吗？'
                url={`/assistant/site/${site.id}/feedback/${feedback.id}`}
                method={'delete'}
                variant={'outline-danger'}
                onSuccess={() => navigate(`/assistant/site/${site.id}/feedback`)}
            >删除</RequestButton>
        </Card>}
    </>;
}
