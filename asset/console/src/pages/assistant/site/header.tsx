import { Link, styled, useParams, useRequest, useRouteLoaderData } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import Status from './status';

export default function Header() {
    const { id } = useParams();
    const site = useRouteLoaderData('assistant/site') as AssistantSite;

    const { result } = useRequest<AssistantSite[]>('/assistant/site', {
        refreshDeps: [site]
    });

    if (!result) {
        return null;
    }

    return <Container>
        <Link to='/assistant/site'><i className='bi bi-chevron-left' /></Link>
        <StyledDropdown>
            <Dropdown.Toggle as={Toggle}>
                <span className='text-truncate'>{result.find((item) => item.id === Number(id))?.name}</span>
            </Dropdown.Toggle>
            <Dropdown.Menu >
                {result.map((item) => {
                    return <Dropdown.Item key={item.id} as={Link} to={`/assistant/site/${item.id}`}>{item.name}</Dropdown.Item>;
                })}
            </Dropdown.Menu>
        </StyledDropdown>
        <Status site={site} placement='bottom-start' />
    </Container>;
}

const StyledDropdown = styled(Dropdown)`
    min-width: 0;
    margin-right: auto;

    .dropdown-menu {
        max-width: 180px;
        overflow: hidden;

        .dropdown-item {
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
`;

const Toggle = styled.a`
  display: flex;
  min-width: 0;
  align-items: center;
  gap: 4px;
  cursor: pointer;
`;

const Container = styled.div`
  display: flex;
  flex: 1;
  gap: 8px;
  min-width: 0;
  align-items: center;
  font-size: 16px;
`;
