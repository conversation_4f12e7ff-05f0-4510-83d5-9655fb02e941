import { Form, FormType, ModalButton, useNavigate } from '@topthink/common';
import { useCallback, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import awaitPayComplete from '@/utils/await-pay-complete';

interface Props {
    hecong: AssistantHecong;
}

export default function PayModal({ hecong }: Props) {

    const navigate = useNavigate();

    const [type, startDate] = useMemo(() => {

        const now = dayjs();
        const expire = dayjs(hecong.expire_time);

        const type = (hecong.plan === 'trial' || expire.isBefore(now)) ? 'open' : 'renew';

        const startDate = expire.isBefore(now) ? now : expire;

        return [type, startDate];
    }, [hecong]);

    const [data, setData] = useState({ years: 1, seat: 1 });

    const form = useRef<FormType>(null);

    const handleOk = useCallback(async () => {
        const result = await form.current?.submit();

        if (result) {
            awaitPayComplete({
                result,
                onComplete() {
                    navigate('/assistant/hecong');
                }
            });
            return true;
        }
        return false;
    }, []);

    return <ModalButton text={type === 'open' ? '账号开通' : '账号续费'} variant={'primary'} onOk={handleOk}>
        <Form
            ref={form}
            formContext={{ layout: 'horizontal' }}
            method={'post'}
            action={'/assistant/hecong/buy'}
            schema={{
                type: 'object',
                properties: {
                    plan: {
                        type: 'string',
                        title: '版本',
                        enum: ['standard'],
                        default: 'standard'
                    },
                    seat: {
                        type: 'number',
                        title: '坐席数',
                        minimum: 1,
                        default: 1,
                    },
                    years: {
                        type: 'number',
                        title: '购买时长',
                        enum: [1, 2, 3],
                        default: 1
                    }
                }
            }}
            uiSchema={{
                plan: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['标准版'],
                        button: true
                    }
                },
                seat: {
                    'ui:widget': type === 'renew' ? 'hidden' : 'text'
                },
                years: {
                    'ui:widget': 'radio',
                    'ui:options': {
                        enumNames: ['1年', '2年', '3年'],
                        button: true
                    }
                }
            }}
            formData={data}
            onChange={e => setData(e.formData)}
        >
            <div className='d-flex'>
                <label className='col-sm-2 col-form-label'>到期日期</label>
                <div className='col-sm-10 d-flex align-items-center'>
                    {startDate.add(data.years, 'years').format('YYYY-MM-DD')}
                </div>
            </div>
            <div className='d-flex'>
                <label className='col-sm-2 col-form-label'>合计</label>
                <div className='col-sm-10 d-flex align-items-center'>
                    <span className='text-danger fs-5'>{299 * data.years * data.seat} 元</span>
                </div>
            </div>
        </Form>
    </ModalButton>;
}
