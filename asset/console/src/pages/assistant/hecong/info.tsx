import { Card, Content, RequestButton, Space } from '@topthink/common';
import dayjs from 'dayjs';
import { ASSISTANT_PLAN } from '@/utils/constants';

interface Props {
    hecong: AssistantH<PERSON>ong;
}

export default function Info({ hecong }: Props) {

    return <Content title='合从客服'>
        <Card>
            <dl className='row'>
                <dd className='col-2'>到期日期</dd>
                <dd className='col-10'>{dayjs(hecong.expire_time).format('YYYY-MM-DD')}</dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>坐席数</dd>
                <dd className='col-10'>{hecong.plan === 'trial' ? '体验期间无限制' : hecong.seat}</dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>版本</dd>
                <dd className='col-10'>{ASSISTANT_PLAN[hecong.plan]}</dd>
            </dl>
            <Space>
                <RequestButton
                    variant='outline-primary'
                    onSuccess={(result) => window.open(result)}
                    url={`/assistant/hecong/login`}
                >工作台</RequestButton>
            </Space>
        </Card>
    </Content>;
}
