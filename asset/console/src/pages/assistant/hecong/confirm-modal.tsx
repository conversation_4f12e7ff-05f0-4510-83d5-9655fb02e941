import { ModalForm, useUser } from '@topthink/common';

interface Props {
    onSuccess: () => void;
}

export default function ConfirmModal({ onSuccess }: Props) {
    const [user] = useUser();
    return <ModalForm
        method={'post'}
        action={'/assistant/hecong'}
        buttonProps={{ variant: 'primary' }}
        text={'免费试用'}
        schema={{
            type: 'object',
            required: ['company', 'name'],
            properties: {
                company: {
                    type: 'string',
                    title: '公司名称',
                },
                name: {
                    type: 'string',
                    title: '联系人'
                },
                tel: {
                    type: 'string',
                    title: '手机号码',
                    readOnly: true
                }
            }
        }}
        formData={{
            tel: user.mobile
        }}
        onSuccess={onSuccess}
    />;
}
