import { Card, Content, Loader, Result, useLocation, useRequest } from '@topthink/common';
import Info from '@/pages/assistant/hecong/info';

export const Component = function() {
    const { key } = useLocation();

    const { result } = useRequest<AssistantHecong>(`/assistant/hecong`, { refreshDeps: [key] });

    if (result === undefined) {
        return <Loader />;
    }

    if (result) {
        return <Info hecong={result} />;
    }

    const extra = <>
        <p className={'mb-4 text-muted'}>
            可以把网站/公众号/小程序/App等渠道的客户统一接入合从，帮助企业连接客户，多客服协作，内置机器人和营销工具，更高效
            ·
            更智能。
        </p>
    </>;

    return <Content title='合从客服'>
        <Card>
            <Result title={'合从客服'} extra={extra} />
        </Card>
    </Content>;
}
