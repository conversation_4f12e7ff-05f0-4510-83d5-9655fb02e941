import { Link, styled, useParams, useRequest } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';

export default function Header() {
    const { id } = useParams();
    const { result } = useRequest<AssistantWeaccount[]>('/assistant/weaccount');

    if (!result) {
        return null;
    }

    return <Container>
        <Link to='/assistant/weaccount'><i className='bi bi-chevron-left' /></Link>
        <StyledDropdown>
            <Dropdown.Toggle as={Toggle}>
                <span className='text-truncate'>{result.find((item) => item.id === Number(id))?.weaccount.nickname}</span>
            </Dropdown.Toggle>
            <Dropdown.Menu>
                {result.map((item) => {
                    return <Dropdown.Item key={item.id} as={Link} to={`/assistant/weaccount/${item.id}`}>{item.weaccount.nickname}</Dropdown.Item>;
                })}
            </Dropdown.Menu>
        </StyledDropdown>
    </Container>;
}

const StyledDropdown = styled(Dropdown)`
  min-width: 0;
`;

const Toggle = styled.a`
  display: flex;
  min-width: 0;
  align-items: center;
  gap: 4px;
  cursor: pointer;
`;

const Container = styled.div`
  display: flex;
  gap: 8px;
  min-width: 0;
  align-items: center;
  font-size: 16px;
`;
