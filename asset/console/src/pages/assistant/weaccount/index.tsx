import { Content, Link, Table, useRequest } from '@topthink/common';
import InfoModal from '@/components/info-modal';
import { useEffect, useRef } from 'react';

export const Component = function() {
    return <Content title={'公众号助理'}>
        <Table
            source={'/assistant/weaccount'}
            columns={[
                {
                    dataIndex: ['weaccount', 'avatar'],
                    title: '头像',
                    render({ value }) {
                        return <img src={value} width={32} height={32} />;
                    },
                    width: 60
                },
                {
                    dataIndex: ['weaccount', 'nickname'],
                    title: '名称',
                    render({ value, record }) {
                        return <Link to={`/assistant/weaccount/${record.id}`}>{value}</Link>;
                    }
                },
                {
                    dataIndex: ['weaccount', 'status'],
                    title: '状态',
                    align: 'center',
                    width: 100,
                    render({ value }) {
                        if (value === 1) {
                            return <span className='text-success'>正常</span>;
                        } else {
                            return <span className='text-danger'>已取消授权</span>;
                        }
                    }
                },
                {
                    title: '版本',
                    dataIndex: 'plan',
                    render({ value }) {
                        switch (value) {
                            case 'trial':
                                return '试用版';
                            case 'standard':
                                return '标准版';
                            default:
                                return '未知';
                        }
                    }
                },
                {
                    title: '到期时间',
                    dataIndex: 'expire_time'
                }
            ]}
            toolBarRender={({ reload }) => {
                return <InfoModal
                    modalProps={{ backdrop: 'static', header: '公众平台账号授权' }}
                    source={{
                        method: 'post',
                        url: '/assistant/weaccount/qrcode'
                    }}
                    renderChildren={({ data, action }) => {
                        const show = useRef(true);
                        useRequest({
                            url: '/assistant/weaccount/check',
                            params: { uuid: data.uuid },
                            raxConfig: {
                                retryDecider() {
                                    return show.current;
                                }
                            },
                        }, {
                            onSuccess() {
                                action.close();
                                reload();
                            }
                        });

                        useEffect(() => {
                            show.current = true;
                            return () => {
                                show.current = false;
                            };
                        }, []);

                        return <div className='p-3 d-flex flex-column align-items-center justify-content-center'>
                            <img className='mb-3' src={data.url} width={194} height={194} />
                            <div>使用公众平台绑定的管理员个人微信号扫描</div>
                        </div>;
                    }}
                    variant={'primary'}
                    text={'绑定公众号'}
                />;
            }}
        />
    </Content>;
}
