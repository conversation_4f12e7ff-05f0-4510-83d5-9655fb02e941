import { Card, Content, useRouteLoaderData } from '@topthink/common';
import dayjs from 'dayjs';
import { ASSISTANT_PLAN } from '@/utils/constants';

export const Component = function() {

    const weaccount = useRouteLoaderData('assistant/weaccount') as AssistantWeaccount;

    return <Content>
        <Card>
            <dl className='row'>
                <dd className='col-2'>头像</dd>
                <dd className='col-10'>
                    <img width={32} height={32} src={weaccount.weaccount.avatar} />
                </dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>名称</dd>
                <dd className='col-10'>
                    {weaccount.weaccount.nickname}
                </dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>介绍</dd>
                <dd className='col-10'>
                    {weaccount.weaccount.signature}
                </dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>到期日期</dd>
                <dd className='col-10'>{dayjs(weaccount.expire_time).format('YYYY-MM-DD')}</dd>
            </dl>
            <dl className='row mb-0'>
                <dd className='col-2'>版本</dd>
                <dd className='col-10'>{ASSISTANT_PLAN[weaccount.plan]}</dd>
            </dl>
        </Card>
    </Content>;
}
