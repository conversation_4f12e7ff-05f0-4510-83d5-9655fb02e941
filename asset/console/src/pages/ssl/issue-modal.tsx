import { FormSchema, FormWidgetProps, ModalForm, useRequest } from '@topthink/common';
import { FormSelect } from 'react-bootstrap';
import ContactModal from '@/pages/ssl/contact-modal';
import { useEffect } from 'react';
import OrgModal from '@/pages/ssl/org-modal';

interface Props {
    order: SslOrder;
    onSuccess?: () => void;
}

export default function IssueModal({ order, onSuccess }: Props) {

    const properties: FormSchema['properties'] = {};

    order.one && (properties.one = {
        type: 'array',
        title: '标准域名',
        items: new Array(order.one).fill({
            type: 'string'
        })
    });

    order.wildcard && (properties.wildcard = {
        type: 'array',
        title: '泛域名',
        items: new Array(order.wildcard).fill({
            type: 'string'
        })
    });

    properties.auth_type = {
        type: 'string',
        title: '验证方式',
        oneOf: [
            {
                const: 'dns',
                title: 'DNS验证'
            }
        ],
        default: 'dns'
    };

    properties.csr_type = {
        type: 'string',
        title: 'CSR生成',
        oneOf: [
            {
                const: 'auto',
                title: '自动生成'
            }
        ],
        default: 'auto'
    };

    properties.contact = {
        type: 'number',
        title: '联系人信息'
    };
    order.type === 'ov' && (properties.org = {
        type: 'number',
        title: '公司信息'
    });

    const schema: FormSchema = {
        type: 'object',
        properties
    };

    return <ModalForm
        onSuccess={onSuccess}
        modalProps={{ size: 'lg' }}
        formContext={{ layout: 'horizontal' }}
        text={'申请证书'}
        schema={schema}
        method={'post'}
        action={`/ssl/order/${order.id}/issue`}
        uiSchema={{
            one: {
                items: {
                    'ui:options': {
                        label: false,
                        placeholder: '请输入域名'
                    }
                }
            },
            wildcard: {
                items: {
                    'ui:options': {
                        label: false,
                        placeholder: '请输入域名',
                        prefix: '*.'
                    }
                }
            },
            auth_type: {
                'ui:widget': 'radio',
            },
            csr_type: {
                'ui:widget': 'radio',
            },
            contact: {
                'ui:widget': ({ onChange, value }: FormWidgetProps) => {

                    const { result = [], refresh } = useRequest<SslContact[]>('/ssl/contact');

                    useEffect(() => {
                        if (!value && result.length > 0) {
                            onChange(result[0].id);
                        }
                    }, [result]);

                    return <div className='d-flex gap-3'>
                        <FormSelect
                            value={value}
                            className='flex-fill'
                            onChange={(event) => {
                                onChange(event.target.value);
                            }}>
                            {result.map((contact) => {
                                return <option key={contact.id} value={contact.id}>
                                    {contact.last_name}{contact.first_name}
                                </option>;
                            })}
                        </FormSelect>
                        <ContactModal
                            onSuccess={refresh}
                            header={'新增联系人信息'}
                            buttonProps={{ className: 'flex-shrink-0' }}
                        >新增</ContactModal>
                    </div>;
                }
            },
            org: {
                'ui:widget': ({ onChange, value }: FormWidgetProps) => {

                    const { result = [], refresh } = useRequest<SslOrg[]>('/ssl/org');

                    useEffect(() => {
                        if (!value && result.length > 0) {
                            onChange(result[0].id);
                        }
                    }, [result]);

                    return <div className='d-flex gap-3'>
                        <FormSelect
                            className='flex-fill'
                            value={value}
                            onChange={(event) => {
                                onChange(event.target.value);
                            }}>
                            {result.map((contact) => {
                                return <option key={contact.id} value={contact.id}>
                                    {contact.name}
                                </option>;
                            })}
                        </FormSelect>
                        <OrgModal
                            onSuccess={refresh}
                            header={'新增公司信息'}
                            buttonProps={{ className: 'flex-shrink-0' }}
                        >新增</OrgModal>
                    </div>;
                }
            }
        }}
    />;
}
