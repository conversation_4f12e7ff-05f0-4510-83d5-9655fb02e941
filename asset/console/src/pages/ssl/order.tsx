import { Columns, Content, Link, RequestButton, Space, Table } from '@topthink/common';
import IssueModal from '@/pages/ssl/issue-modal';
import VerifyModal from '@/pages/ssl/verify-modal';
import SslOrderStatus from '@/components/ssl-order-status';
import download from '@/utils/download';

export const Component = () => {

    const columns: Columns<SslOrder> = [
        {
            dataIndex: 'brand',
            title: '证书品牌',
            render({ record }) {
                const types = {
                    dv: '域名型(DV)',
                    ov: '企业型(OV)',
                };
                return `${record.brand}-${types[record.type]}`;
            }
        },
        {
            dataIndex: 'domains',
            title: '域名',
            render({ value }) {
                return value || '--';
            }
        },
        {
            width: 80,
            dataIndex: 'status',
            title: '订单状态',
            render({ value }) {
                return <SslOrderStatus status={value} />;
            }
        },
        {
            width: 150,
            dataIndex: 'create_time',
            title: '创建时间',
        },
        {
            width: 100,
            key: 'action',
            title: '操作',
            align: 'right',
            render({ record, action }) {
                return <Space>
                    {record.status === 0 && <IssueModal onSuccess={action.reload} order={record} />}
                    {record.status === 1 && <VerifyModal onSuccess={action.reload} order={record} />}
                    {record.status === 3 &&
                        <RequestButton variant={'link'} onSuccess={(result) => {
                            const name = result.domain.replace('*', '_');

                            const files = {
                                [`${name}.key`]: result.key,
                                [`${name}.crt`]: result.certificate,
                                [`${name}.pem`]: result.certificate
                            };

                            download(files, `${name}.zip`);
                        }} url={`/ssl/cert/${record.cert_id}`}>下载</RequestButton>}
                </Space>;
            }
        },
    ];

    return <Content title={'我的订单'}>
        <Table
            toolBarRender={() => {
                return <Space>
                    <Link to={'/ssl/buy'} className='btn btn-primary'>购买证书</Link>
                </Space>;
            }}
            source={`/ssl/order`}
            columns={columns}
        />
    </Content>;
};
