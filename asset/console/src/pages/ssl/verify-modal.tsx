import { Message, ModalButton, request, styled, Table, TableType, Tooltip } from '@topthink/common';
import { useRef } from 'react';
import { Button } from 'react-bootstrap';
import copy from 'copy-to-clipboard';

interface Props {
    order: SslOrder;
    onSuccess: () => void;
}

export default function VerifyModal({ order, onSuccess }: Props) {

    const table = useRef<TableType>(null);

    return <ModalButton
        text={'验证域名'}
        onOk={async () => {
            try {
                await request({
                    url: `/ssl/order/${order.id}/verify`,
                    method: 'post'
                });
                onSuccess();
                return true;
            } catch {
                table.current?.reload();
            }

            return false;
        }}
        modalProps={{
            size: 'lg',
            okText: '验证域名',
        }}
    >
        <Container>
            <Table
                ref={table}
                rowKey={(record) => record.dnsNames[0]}
                toolBarRender={false}
                card={false}
                paginate={false}
                source={`/ssl/order/${order.id}/domains`}
                columns={[
                    {
                        dataIndex: 'dnsNames',
                        title: '待验证域名',
                        render({ value }) {
                            return value[0];
                        }
                    },
                    {
                        dataIndex: 'recordType',
                        title: '记录类型',
                        width: 100,
                        align: 'center'
                    },
                    {
                        dataIndex: 'hostRecord',
                        align: 'center',
                        width: 100,
                        title: '主机记录',
                        render({ value }) {
                            return <Tooltip placement={'top'} tooltip={value}>
                                <Button variant={'link'} onClick={() => {
                                    copy(value);
                                    Message.success({ title: '复制成功！' });
                                }}>复制</Button>
                            </Tooltip>;
                        }
                    },
                    {
                        dataIndex: 'hashValue',
                        title: '记录值',
                        width: 70,
                        align: 'center',
                        render({ value }) {
                            return <Tooltip placement={'top'} tooltip={value}>
                                <Button variant={'link'} onClick={() => {
                                    copy(value);
                                    Message.success({ title: '复制成功！' });
                                }}>复制</Button>
                            </Tooltip>;
                        }
                    },
                    {
                        title: '状态',
                        dataIndex: 'status',
                        align: 'center',
                        width: 100,
                        render({ value }) {
                            switch (value) {
                                case '2001':
                                    return <span className={'text-warning'}>未验证</span>;
                                case '2002':
                                    return <span className={'text-success'}>已验证</span>;
                            }
                            return '--';
                        }
                    }
                ]}
            />
        </Container>
    </ModalButton>;
}

const Container = styled.div`

`;
