import {
    Columns,
    Content,
    FormSchema,
    FormUiSchema,
    Link,
    ModalForm,
    RequestButton,
    Space,
    Table
} from '@topthink/common';
import { Button } from 'react-bootstrap';
import download from '@/utils/download';

export const Component = () => {

    const columns: Columns = [
        {
            title: '名称',
            align: 'left',
            dataIndex: 'name',
            width: 200
        },
        {
            title: '品牌',
            dataIndex: 'brand',
            width: 200
        },
        {
            title: '域名',
            align: 'left',
            dataIndex: 'domain',
        },
        {
            title: '到期时间',
            dataIndex: 'expire_time',
            width: 200
        },
        {
            title: '操作',
            align: 'right',
            key: 'action',
            render({ record, action }) {
                return <Space>
                    <Button variant={'link'} onClick={() => {
                        const name = record.domain.replace('*', '_');

                        const files = {
                            [`${name}.key`]: record.key,
                            [`${name}.crt`]: record.certificate,
                            [`${name}.pem`]: record.certificate
                        };

                        download(files, `${name}.zip`);
                    }}>下载</Button>
                    {record.brand === '自有' && <RequestButton
                        url={`/ssl/cert/${record.id}`}
                        method={'delete'}
                        confirm='确定要删除吗？'
                        onSuccess={action.reload}
                        variant={'link'}
                    >删除</RequestButton>}
                </Space>;
            }
        }
    ];

    const schema: FormSchema = {
        type: 'object',
        required: [
            'name',
            'certificate',
            'key'
        ],
        properties: {
            name: {
                title: '证书名称',
                type: 'string',
            },
            certificate: {
                title: '证书内容',
                type: 'string',
            },
            key: {
                title: '私钥内容',
                type: 'string'
            },
        }
    };

    const uiSchema: FormUiSchema = {
        name: {
            'ui:placeholder': '请输入证书名称'
        },
        certificate: {
            'ui:widget': 'textarea',
            'ui:placeholder': '证书格式以"-----BEGIN CERTIFICATE-----"开头，以"-----END CERTIFICATE-----"结尾。',
            'ui:options': {
                'rows': 8
            }
        },
        key: {
            'ui:widget': 'textarea',
            'ui:placeholder': '证书私钥格式以"-----BEGIN (RSA|EC) PRIVATE KEY-----"开头，以"-----END(RSA|EC) PRIVATE KEY-----"结尾。',
            'ui:options': {
                'rows': 8
            }
        }
    };

    return <Content title='我的证书'>
        <Table
            toolBarRender={({ reload }) => {
                return <Space>
                    <Link to={'/ssl/buy'} className='btn btn-primary'>购买证书</Link>
                    <ModalForm
                        modalProps={{ size: 'lg' }}
                        action={'/ssl/cert'}
                        method={'post'}
                        onSuccess={reload}
                        schema={schema}
                        uiSchema={uiSchema}
                        buttonProps={{ variant: 'secondary' }}
                        text={'上传证书'}
                    />
                </Space>;
            }}
            columns={columns}
            source={'/ssl/cert'}
        />
    </Content>;
};
