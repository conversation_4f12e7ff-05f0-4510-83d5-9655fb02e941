import { Card, Content, Loader, Result, useRequest } from '@topthink/common';
import BuyForm from '@/pages/ssl/buy-form';

export const Component = () => {

    const { result } = useRequest('/ssl/order/brands');

    if (!result) {
        return <Loader />;
    }

    return <Content title={'购买证书'}>
        <Card>
            {result.length > 0 ? <BuyForm brands={result} /> : <Result status={'error'} title='暂无证书上架' />}
        </Card>
    </Content>;
};
