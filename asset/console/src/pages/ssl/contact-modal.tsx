import { ModalForm, ModalFormProps } from '@topthink/common';
import { ReactNode } from 'react';

interface Props extends Partial<ModalFormProps> {
    children: string;
    header?: ReactNode;
}

export default function ContactModal({ children, header, formData, ...props }: Props) {
    return <ModalForm
        {...props}
        modalProps={{ header }}
        formData={formData}
        method={formData ? 'put' : 'post'}
        action={formData ? `/ssl/contact/${formData.id}` : `/ssl/contact`}
        text={children}
        schema={{
            type: 'object',
            properties: {
                last_name: {
                    type: 'string',
                    title: '姓'
                },
                first_name: {
                    type: 'string',
                    title: '名'
                },
                position: {
                    type: 'string',
                    title: '职位'
                },
                telephone: {
                    type: 'string',
                    title: '手机号'
                },
                email: {
                    type: 'string',
                    title: '邮箱'
                }
            }
        }}
        uiSchema={{
            first_name: {
                'ui:col': 6
            },
            last_name: {
                'ui:col': 6
            }
        }}
    />;
}
