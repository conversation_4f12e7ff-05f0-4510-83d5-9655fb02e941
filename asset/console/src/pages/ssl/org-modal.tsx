import { ModalForm, ModalFormProps } from '@topthink/common';
import { ReactNode } from 'react';

interface Props extends Partial<ModalFormProps> {
    children: string;
    header?: ReactNode;
}

export default function OrgModal({ children, formData, header, ...props }: Props) {
    return <ModalForm
        {...props}
        modalProps={{ header }}
        formData={formData}
        method={formData ? 'put' : 'post'}
        action={formData ? `/ssl/org/${formData.id}` : `/ssl/org`}
        text={children}
        schema={{
            type: 'object',
            properties: {
                name: {
                    type: 'string',
                    title: '公司名称'
                },
                credit_code: {
                    type: 'string',
                    title: '社会信用代码'
                },
                country: {
                    type: 'string',
                    title: '国家',
                    oneOf: [
                        {
                            const: 'CN',
                            title: '中国'
                        }
                    ],
                    default: 'CN'
                },
                province: {
                    type: 'string',
                    title: '省份'
                },
                locality: {
                    type: 'string',
                    title: '城市'
                },
                address: {
                    type: 'string',
                    title: '地址'
                },
                postal_code: {
                    type: 'string',
                    title: '邮编'
                },
                telephone: {
                    type: 'string',
                    title: '联系电话'
                }
            }
        }}
        uiSchema={{
            country: {
                'ui:col': 4
            },
            province: {
                'ui:col': 4
            },
            locality: {
                'ui:col': 4
            }
        }}
    />;
}
