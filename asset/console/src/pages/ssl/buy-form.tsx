import { LoadingButton, NumberFormat, request, showRequestError, Space, styled, useNavigate } from '@topthink/common';
import { Button, ButtonGroup, Col, FormControl, Row } from 'react-bootstrap';
import { useCallback, useEffect, useMemo, useState } from 'react';
import awaitPayComplete from '@/utils/await-pay-complete';

interface Type {
    type: 'dv' | 'ov';
    name: string;
    description: string;
    flex: boolean;
}

function getBrandTypes(brand: SslBrand) {
    const types: Type[] = [];

    brand.dv && types.push({
        type: 'dv',
        name: '域名型(DV)',
        description: '域名型加密SSL证书，浏览器上有https提示并有绿锁标记。对域名所有权进行验证，满足绑定多域名和泛域名的需求，快速颁发，经济实惠，保护网站数据安全，适合个人，中小企业应用。',
        flex: !brand.dv.product.includes(',')
    });

    brand.ov && types.push({
        type: 'ov',
        name: '企业型(OV)',
        description: '企业级加密SSL证书, 浏览器上有https提示并有绿锁标记。对申请公司单位做严格的身份审核验证, 保护内外部网络上敏感数据传输，是中小型企业应用、电商等服务的最佳选择。',
        flex: !brand.ov.product.includes(',')
    });

    return types;
}

const DOMAIN_DESCRIPTION = {
    'one': '仅支持绑定一个二级域名或者子域名，例如 topthink.com、market.topthink.com的其中之一 。',
    'wildcard': '带通配符的域名，例如：*.topthink.com、*.market.topthink.com均为泛域名，包含同一级的全部子域名。',
    'flex': '支持绑定多个标准域名和泛域名，例如：*.domain.com、*.another.com、www.topthink.com,共计2个泛域名,1个标准域名。'
};

interface Props {
    brands: SslBrand[];
}

export default function BuyForm({ brands }: Props) {
    const navigate = useNavigate();
    const [brand, setBrand] = useState(brands[0]);
    const [types, setTypes] = useState<Type[]>(() => getBrandTypes(brand));
    const [type, setType] = useState<Type>(types[0]);
    const [domain, setDomain] = useState<'one' | 'wildcard' | 'flex'>('one');

    const [one, setOne] = useState<number>(1);
    const [wildcard, setWildcard] = useState<number>(1);

    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const types = getBrandTypes(brand);
        setTypes(types);
        setType(types[0]);
    }, [brand]);

    useEffect(() => {
        setDomain('one');
    }, [type]);

    useEffect(() => {
        setOne((domain === 'flex' || domain === 'one') ? 1 : 0);
        setWildcard((domain === 'flex' || domain === 'wildcard') ? 1 : 0);
    }, [domain]);

    const price = useMemo(() => {
        const price = brand[type.type]!.price;

        function getPrice(str: string): number {
            const [, price] = str.split(',');
            return Number(price);
        }

        return getPrice(price.one) * one + getPrice(price.wildcard) * wildcard;
    }, [type, domain, one, wildcard]);

    const originalPrice = useMemo(() => {
        const price = brand[type.type]!.price;

        function getPrice(str: string): number {
            const [price] = str.split(',');
            return Number(price);
        }

        return getPrice(price.one) * one + getPrice(price.wildcard) * wildcard;
    }, [type, domain, one, wildcard]);

    const onSubmit = useCallback(async () => {
        try {
            setLoading(true);

            const data = {
                brand: brand.id,
                type: type.type,
                one,
                wildcard,
            };

            const result = await request({
                url: `/ssl/order`,
                method: 'post',
                data,
            });

            awaitPayComplete({
                result,
                onComplete() {
                    navigate('/ssl/order');
                }
            });
        } catch (e) {
            showRequestError(e);
        } finally {
            setLoading(false);
        }
    }, [type, one, wildcard]);

    return <Space direction={'vertical'} size='large'>
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>证书品牌</Col>
            <Col xs={10}>
                <ButtonGroup>
                    {brands.map((b) => {
                        return <Button
                            key={b.id}
                            variant={'outline-primary'}
                            onClick={() => setBrand(b)}
                            active={b.name === brand.name}
                        >{b.name}</Button>;
                    })}
                </ButtonGroup>
                <div className='form-text'>{brand.description}</div>
            </Col>
        </Row>
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>证书类型</Col>
            <Col xs={10}>
                <ButtonGroup>
                    {types.map((t) => {
                        return <Button
                            key={t.type}
                            variant={'outline-primary'}
                            onClick={() => setType(t)}
                            active={t.type === type.type}
                        >{t.name}</Button>;
                    })}
                </ButtonGroup>
                <div className='form-text'>{type.description}</div>
            </Col>
        </Row>
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>域名类型</Col>
            <Col xs={10}>
                <ButtonGroup>
                    <Button onClick={() => setDomain('one')} variant={'outline-primary'}
                            active={'one' === domain}>单域名</Button>
                    <Button onClick={() => setDomain('wildcard')} variant={'outline-primary'}
                            active={'wildcard' === domain}>泛域名</Button>
                    {type.flex &&
                        <Button onClick={() => setDomain('flex')} variant={'outline-primary'}
                                active={'flex' === domain}>多域名</Button>}
                </ButtonGroup>
                <div className='form-text'>{DOMAIN_DESCRIPTION[domain]}</div>
            </Col>
        </Row>
        {domain === 'flex' && <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>域名数量</Col>
            <Col xs={10}>
                <Row>
                    <Col as={'label'} className={'col-form-label'} xs={1}>标准域名</Col>
                    <Col xs={2}>
                        <FormControl
                            min={0}
                            value={one}
                            onChange={(e) => setOne(Number(e.target.value))}
                            type={'number'}
                        />
                    </Col>
                    <Col xs={1} />
                    <Col as={'label'} className={'col-form-label'} xs={1}>泛域名</Col>
                    <Col xs={2}>
                        <FormControl
                            min={0}
                            value={wildcard}
                            onChange={(e) => setWildcard(Number(e.target.value))}
                            type={'number'}
                        />
                    </Col>
                </Row>
            </Col>
        </Row>}
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>有效期限</Col>
            <Col xs={10} className={'d-flex align-items-center'}>
                1年
            </Col>
        </Row>
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}>费用</Col>
            <Col xs={10}>
                <Space>
                    <StyledNumberFormat className={'fs-5 text-muted'} value={originalPrice} />
                    <NumberFormat className={'fs-4 text-primary'} value={price} />
                </Space>
            </Col>
        </Row>
        <Row>
            <Col as={'label'} className={'col-form-label'} xs={2}></Col>
            <Col xs={10}>
                <LoadingButton onClick={onSubmit} loading={loading} variant={'primary'}>立即购买</LoadingButton>
            </Col>
        </Row>
    </Space>;
}

const StyledNumberFormat = styled(NumberFormat)`
  text-decoration: line-through
`;
