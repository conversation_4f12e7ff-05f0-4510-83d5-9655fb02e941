import { Content, RequestButton, Space, Table } from '@topthink/common';
import ContactModal from '@/pages/ssl/contact-modal';
import OrgModal from './org-modal';

export const Component = () => {
    return <Content title={'我的信息'}>
        <Table
            toolBarRender={(action) => {
                return <ContactModal onSuccess={action.reload}>新增联系人信息</ContactModal>;
            }}
            source={'/ssl/contact'}
            columns={[
                {
                    key: 'name',
                    title: '姓名',
                    render({ record }) {
                        return record.last_name + record.first_name;
                    }
                },
                {
                    dataIndex: 'position',
                    title: '职位'
                },
                {
                    dataIndex: 'telephone',
                    title: '手机号'
                },
                {
                    dataIndex: 'email',
                    title: '邮箱'
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 100,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <ContactModal
                                formData={record}
                                onSuccess={action.reload}
                            >编辑</ContactModal>
                            <RequestButton
                                confirm={'确定要删除吗？'}
                                method={'delete'}
                                url={`/ssl/contact/${record.id}`}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
        <Table
            toolBarRender={(action) => {
                return <OrgModal onSuccess={action.reload}>新增公司信息</OrgModal>;
            }}
            source={'/ssl/org'}
            columns={[
                {
                    dataIndex: 'name',
                    title: '公司名称'
                },
                {
                    dataIndex: 'telephone',
                    title: '联系电话'
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 100,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <OrgModal
                                formData={record}
                                onSuccess={action.reload}
                            >编辑</OrgModal>
                            <RequestButton
                                confirm={'确定要删除吗？'}
                                method={'delete'}
                                url={`/ssl/org/${record.id}`}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
