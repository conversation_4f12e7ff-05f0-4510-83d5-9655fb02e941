import { Card, Content, Link, LinkButton, Space, styled, useUser } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export default function Dashboard() {

    const [user] = useUser();

    return <Content title='工作台'>
        <div className='row g-3'>
            <div className={'col-md-8'}>
                <Card title={'常用产品'}>
                    <Space className={'flex-wrap'}>
                        <LinkButton to={'/api'} variant={'light'}>🍀 API服务</LinkButton>
                        <LinkButton to={'/sms'} variant={'light'}>📟 短信服务</LinkButton>
                        <LinkButton to={'/ssl'} variant={'light'}>🔒 SSL证书</LinkButton>
                        <LinkButton to={'/assistant'} variant={'light'}>📌 运营助理</LinkButton>
                        <LinkButton to={'/ai'} variant={'light'}>💎 ThinkAI</LinkButton>
                    </Space>
                </Card>
            </div>
            <div className={'col-md-4'}>
                <Card>
                    <UserInfo>
                        <Avatar><img src={user.avatar} /></Avatar>
                        <Detail>
                            <h3>{user.name}</h3>
                            <dl>
                                <dt>账号ID</dt>
                                <dd>{user.id}</dd>
                            </dl>
                            <dl>
                                <dt>实名认证</dt>
                                <dd>
                                    {user.is_certified ?
                                        <Space>
                                            <span className={'text-success'}>已认证</span>
                                            <Link to={'/user/certification'} className='text-primary'>立即查看</Link>
                                        </Space> :
                                        <Space>
                                            <span className='text-muted'>暂未认证</span>
                                            <Link to={'/user/certification'} className='text-primary'>立即认证</Link>
                                        </Space>
                                    }
                                </dd>
                            </dl>
                            <dl>
                                <dt>企业认证</dt>
                                <dd>
                                    {user.is_enterprise ?
                                        <Space>
                                            <span className={'text-success'}>已认证</span>
                                            <Link to={'/user/enterprise'} className='text-primary'>立即查看</Link>
                                        </Space> :
                                        <Space>
                                            <span className='text-muted'>暂未认证</span>
                                            <Link to={'/user/enterprise'} className='text-primary'>立即认证</Link>
                                        </Space>}
                                </dd>
                            </dl>
                        </Detail>
                    </UserInfo>
                </Card>
                <Card title={'快捷入口'}>
                    <Row className='mb-3'>
                        <Col><a href='https://k.topthink.com' target='_blank'
                                className={'w-100 btn btn-light'}>📖 知识管理</a></Col>
                        <Col><a href='https://bot.topthink.com' target='_blank'
                                className={'w-100 btn btn-light'}>🤖 ThinkBot启智</a></Col>
                    </Row>
                    <Row>
                        <Col><a href='https://developer.topthink.com' target='_blank'
                                className={'w-100 btn btn-light'}>💻 开发者中心</a></Col>
                        <Col><a href='https://chat.topthink.com' target='_blank'
                                className={'w-100 btn btn-light'}>🚀 AI助理</a></Col>
                    </Row>
                </Card>
            </div>
        </div>
    </Content>;
}

const Detail = styled.div`
  flex: 1;

  h3 {
    font-size: 16px;
    white-space: nowrap;
    margin-bottom: 6px;
    color: #2a2a2a;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.5;
  }

  dl {
    display: flex;
    margin-bottom: 0;
    line-height: 1.8;

    dt {
      font-weight: 400;
      color: rgba(0, 0, 0, .4);
      width: 80px;
    }

    dd {
      margin-bottom: 0;
    }
  }
`;

const Avatar = styled.div`
  margin-right: 24px;

  img {
    border-radius: 50%;
    width: 64px;
    height: 64px;
  }
`;

const UserInfo = styled.div`
  display: flex;
  flex-wrap: nowrap;
`;
