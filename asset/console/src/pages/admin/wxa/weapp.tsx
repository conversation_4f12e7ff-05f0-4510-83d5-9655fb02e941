import { Content, request, Space, Table } from '@topthink/common';
import InfoModal from '@/components/info-modal';

export function Component() {

    return <Content title={'小程序管理'}>
        <Table
            source={'/admin/wxa/weapp'}
            columns={[
                {
                    dataIndex: 'app_id',
                    title: 'AppID',
                    width: 120
                },
                {
                    dataIndex: 'avatar',
                    title: '头像',
                    render({ value }) {
                        return <img src={value} width={32} height={32} />;
                    },
                    width: 60
                },
                {
                    dataIndex: 'nickname',
                    title: '名称'
                },
                {
                    dataIndex: ['template', 'name'],
                    title: '模板'
                },
                {
                    dataIndex: 'version',
                    title: '当前版本'
                },
                {
                    dataIndex: 'create_time',
                    title: '注册时间'
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right',
                    render({ record }) {
                        return <Space>
                            <InfoModal
                                footer={true}
                                source={`/admin/wxa/weapp/${record.id}/audit`}
                                okText={'重新提审'}
                                onOk={async () => {
                                    await request({
                                        url: `/admin/wxa/weapp/${record.id}/audit`,
                                        method: 'post'
                                    });
                                }}
                                renderChildren={({ data }) => {
                                    if (data.errcode !== 0) {
                                        return <>获取失败</>;
                                    }
                                    return <>
                                        <dl className='row'>
                                            <dd className='col-2'>审核版本</dd>
                                            <dd className='col-10'>{data.user_version}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>版本说明</dd>
                                            <dd className='col-10'>{data.user_desc}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>审核状态</dd>
                                            <dd className='col-10'>{data.status}</dd>
                                        </dl>
                                        {data.reason && <dl className='row'>
                                            <dd className='col-2'>拒绝原因</dd>
                                            <dd className='col-10'>{data.reason}</dd>
                                        </dl>}
                                    </>;
                                }}
                                text={'审核状态'}
                            />
                            <InfoModal
                                source={`/admin/wxa/weapp/${record.id}/experience`}
                                renderChildren={({ data }) => {

                                    return <div className={'d-flex align-items-center justify-content-center'}>
                                        <img width={220} height={220} src={`data:image/png;base64,${data.image}`} />
                                    </div>;
                                }}
                                text={'体验版'}
                            />
                            <InfoModal
                                footer={true}
                                okText={'重设域名'}
                                onOk={async () => {
                                    await request({
                                        url: `/admin/wxa/weapp/${record.id}/domain`,
                                        method: 'post'
                                    });
                                }}
                                source={`/admin/wxa/weapp/${record.id}/domain`}
                                renderChildren={({ data }) => {
                                    return <>
                                        <dl className='row'>
                                            <dd className='col-3'>服务器域名</dd>
                                            <dd className='col-9'>{data.request}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-3'>业务域名</dd>
                                            <dd className='col-9'>{data.webview}</dd>
                                        </dl>
                                    </>;
                                }}
                                text={'域名信息'}
                            />
                        </Space>;
                    }
                },
            ]}
        />
    </Content>;
}
