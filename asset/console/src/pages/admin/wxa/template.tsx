import { Content, ModalForm, RequestButton, Space, Table } from '@topthink/common';

export function Component() {


    return <Content title={'小程序模板库'}>
        <Table
            toolBarRender={(action) => {
                return <Space>
                    <ModalForm
                        onSuccess={action.reload}
                        action={'/admin/wxa/template'}
                        method={'post'}
                        text={'添加模板'}
                        schema={{
                            type: 'object',
                            properties: {
                                name: {
                                    type: 'string',
                                    title: '模板名称'
                                }
                            }
                        }} />
                </Space>;
            }}
            source={'/admin/wxa/template'}
            columns={[
                {
                    dataIndex: 'id',
                    title: 'ID'
                },
                {
                    dataIndex: 'name',
                    title: '名称'
                },
                {
                    dataIndex: 'template_id',
                    title: 'templateID'
                },
                {
                    dataIndex: 'version',
                    title: '当前版本'
                },
                {
                    dataIndex: 'desc',
                    title: '版本描述'
                },
                {
                    key: 'action',
                    title: '操作',
                    width: 150,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <ModalForm
                                onSuccess={action.reload}
                                action={`/admin/wxa/template/${record.id}/release`}
                                method={'post'}
                                text={'发布新版'}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        template_id: {
                                            type: 'string',
                                            title: 'templateID'
                                        },
                                        version: {
                                            type: 'string',
                                            title: '版本号'
                                        },
                                        desc: {
                                            type: 'string',
                                            title: '版本描述'
                                        }
                                    }
                                }}
                            />
                            <RequestButton
                                method={'post'}
                                url={`/admin/wxa/template/${record.id}/retry`}
                                onSuccess={action.reload}
                            >重试</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
