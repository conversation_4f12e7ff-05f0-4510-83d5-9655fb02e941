import { Content, Table } from '@topthink/common';

export function Component() {
    return <Content title='公众号管理'>
        <Table
            source={'/admin/wxa/weaccount'}
            columns={[
                {
                    dataIndex: 'app_id',
                    title: 'AppID',
                    width: 120
                },
                {
                    dataIndex: 'avatar',
                    title: '头像',
                    render({ value }) {
                        return <img src={value} width={32} height={32} />;
                    },
                    width: 60
                },
                {
                    dataIndex: 'nickname',
                    title: '名称'
                },
                {
                    dataIndex: 'create_time',
                    title: '绑定时间'
                },
            ]}
        />
    </Content>;
}
