import { Columns, Content, FormProps, ModalForm, Space, Table } from '@topthink/common';

export function Component() {

    const schema: FormProps['schema'] = {
        type: 'object',
        required: [
            'name',
            'type',
            'url',
            'client_id',
            'client_secret'
        ],
        properties: {
            type: {
                title: '类型',
                type: 'string',
                enum: ['gitlab', 'gitee', 'github', 'topthink'],
                default: 'gitlab'
            },
            name: {
                title: '标识',
                type: 'string'
            },
            url: {
                title: '应用地址',
                type: 'string',
                description: 'https://github.com',
            },
            client_id: {
                title: 'Client ID',
                type: 'string'
            },
            client_secret: {
                title: 'Client Secret',
                type: 'string'
            },
            expire_time: {
                title: '有效期',
                type: 'string',
                description: '留空则永久有效'
            },
            remark: {
                title: '备注',
                type: 'string'
            }
        }
    };

    const uiSchema: FormProps['uiSchema'] = {
        type: {
            'ui:enumNames': ['Gitlab', 'Gitee', 'Github', 'Topthink']
        },
        expire_time: {
            'ui:widget': 'datetime'
        },
        remark: {
            'ui:widget': 'textarea'
        }
    };

    const columns: Columns<WritePrivatization> = [
        {
            title: '类型',
            dataIndex: 'type',
        },
        {
            title: '标识',
            dataIndex: 'name',
        },
        {
            title: '应用地址',
            dataIndex: 'url',
        },
        {
            title: '有效期',
            dataIndex: 'expire_time',
            render({ value }) {
                return value || '永久';
            }
        },
        {
            title: '操作',
            align: 'right',
            key: 'action',
            render({ record, action }) {
                return <Space>
                    <ModalForm
                        modalProps={{ size: 'lg', header: '编辑应用' }}
                        action={`/admin/product/write/privatization/${record.id}`}
                        method={'put'}
                        onSuccess={action.reload}
                        schema={schema}
                        formData={record}
                        uiSchema={uiSchema}
                        text={'编辑'}
                    />
                </Space>;
            }
        }
    ];

    return <Content
        title={'私有化应用'}
    >
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    modalProps={{ size: 'lg' }}
                    action={'/admin/product/write/privatization'}
                    onSuccess={reload}
                    schema={schema}
                    uiSchema={uiSchema}
                    text={'创建应用'}
                />;
            }}
            source={'/admin/product/write/privatization'}
            columns={columns}
        />
    </Content>;
}
