import { ModalForm } from '@topthink/common';

interface Props {
    brand: SslBrand;
    onSuccess: () => void;
}

export default function BrandSettingModal({ brand, onSuccess }: Props) {
    return <ModalForm
        onSuccess={onSuccess}
        modalProps={{ size: 'lg' }}
        buttonProps={{ variant: 'link' }}
        action={`/admin/product/ssl/brand/${brand.id}/cert`}
        schema={{
            type: 'object',
            properties: {
                dv: {
                    type: 'object',
                    title: '域名型(DV)',
                    properties: {
                        support: {
                            type: 'boolean',
                            title: '是否支持'
                        }
                    },
                    dependencies: {
                        support: {
                            '$ref': '#/definitions/support',
                        }
                    }
                },
                ov: {
                    type: 'object',
                    title: '企业型(OV)',
                    properties: {
                        support: {
                            type: 'boolean',
                            title: '是否支持'
                        }
                    },
                    dependencies: {
                        support: {
                            '$ref': '#/definitions/support',
                        }
                    }
                },
                auth_type: {
                    type: 'string',
                    title: '域名验证方式'
                },
            },
            definitions: {
                support: {
                    oneOf: [
                        {
                            properties: {
                                support: {
                                    const: true
                                },
                                price: {
                                    type: 'object',
                                    title: '定价',
                                    description: '格式：原价,优惠价,代理价',
                                    properties: {
                                        one: {
                                            type: 'string',
                                            title: '标准域名'
                                        },
                                        wildcard: {
                                            type: 'string',
                                            title: '泛域名'
                                        }
                                    }
                                },
                                product: {
                                    type: 'string',
                                    title: '产品ID',
                                    description: 'flex域名仅需要填写一个ID，非flex域名需使用逗号隔开单域名和泛域名的ID'
                                },
                            },
                        }
                    ]
                }
            }
        }}
        uiSchema={{
            dv: {
                price: {
                    one: {
                        'ui:col': 6
                    },
                    wildcard: {
                        'ui:col': 6
                    }
                }
            },
            ov: {
                price: {
                    one: {
                        'ui:col': 6
                    },
                    wildcard: {
                        'ui:col': 6
                    }
                }
            },
            auth_type: {
                'ui:placeholder': 'dns'
            }
        }}
        formData={{
            dv: brand.dv || {},
            ov: brand.ov || {},
            auth_type: brand.auth_type,
        }}
        text={'证书设置'}
    />;
}
