import { Content, FormSchema, FormUiSchema, ModalForm, RequestButton, Space, Table } from '@topthink/common';
import BrandSettingModal from '@/pages/admin/product/ssl/brand-setting-modal';

export function Component() {

    const schema: FormSchema = {
        type: 'object',
        properties: {
            name: {
                type: 'string',
                title: '品牌名称'
            },
            description: {
                type: 'string',
                title: '品牌描述'
            },
            sort: {
                type: 'number',
                title: '排序',
                default: 0
            }
        },
    };

    const uiSchema: FormUiSchema = {
        description: {
            'ui:widget': 'textarea'
        }
    };

    return <Content title={'品牌管理'}>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    action={'/admin/product/ssl/brand'}
                    onSuccess={reload}
                    schema={schema}
                    uiSchema={uiSchema}
                    text={'新增品牌'}
                />;
            }}
            source={`/admin/product/ssl/brand`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称'
                },
                {
                    dataIndex: 'status',
                    width: 100,
                    title: '状态',
                    render({ value }) {
                        return value === 1 ? '已上架' : '未上架';
                    }
                },
                {
                    key: 'action',
                    width: 200,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <RequestButton
                                url={{
                                    url: `/admin/product/ssl/brand/${record.id}/status`,
                                    data: { status: record.status === 1 ? 0 : 1 }
                                }}
                                method={'post'}
                                onSuccess={action.reload}
                            >
                                {record.status === 1 ? '下架' : '上架'}
                            </RequestButton>
                            <ModalForm
                                method={'put'}
                                action={`/admin/product/ssl/brand/${record.id}`}
                                text={'编辑'}
                                schema={schema}
                                uiSchema={uiSchema}
                                formData={record}
                            />
                            <BrandSettingModal brand={record} onSuccess={action.reload} />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
