import { Card, Content } from '@topthink/common';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';
import Chart from './chart';

export const Component = () => {
    const [period, setPeriod] = useState('30days');

    const extra = <PeriodButtons
        period={period}
        periods={['30days', 'last-month', '6months', '1year']}
        onChange={setPeriod}
    />;

    return <Content extra={extra}>
        <Card>
            <Chart period={period} />
        </Card>
    </Content>;
};
