import { Loader, useRequest } from '@topthink/common';
import { DualAxes, DualAxesConfig } from '@ant-design/plots';
import formatMoney from '@/utils/format-money';
import { formatPeriodDate } from '@/utils/format';

interface StatsData {
    total: StatsDataItem[];
    revenue: StatsDataItem[];
    cost: StatsDataItem[];
}

interface Props {
    period: string;
}

export default function Chart({ period }: Props) {
    const { result } = useRequest<StatsData>({
        url: '/admin/product/ssl/stats',
        params: { period }
    }, {
        refreshDeps: [period]
    });

    if (!result) {
        return <Loader />;
    }

    const config: DualAxesConfig = {
        data: [
            result.total.map(item => ({ date: item.date, total: item.value })),
            [
                ...result.revenue.map(item => ({ ...item, type: '营收' })),
                ...result.cost.map(item => ({ ...item, type: '成本' })),
            ],
        ],
        height: 400,
        xField: 'date',
        yField: ['total', 'value'],
        meta: {
            value: {
                formatter: (val) => formatMoney(val),
            },
            total: {
                alias: '数量'
            },
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                },
            },
        },
        geometryOptions: [
            {
                geometry: 'line',
                smooth: true,
                lineStyle: {
                    lineWidth: 2,
                },
            },
            {
                geometry: 'column',
                isStack: false,
                seriesField: 'type',
            },
        ],
        legend: {
            position: 'bottom',
        }
    };

    return <DualAxes {...config} />;
}
