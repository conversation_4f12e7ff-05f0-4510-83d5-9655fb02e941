import { Columns, Content, Table } from '@topthink/common';
import SslOrderStatus from '@/components/ssl-order-status';
import UserModal from '@/components/user-modal';

export function Component() {
    const columns: Columns<SslOrder> = [
        {
            dataIndex: 'brand',
            title: '证书品牌',
            render({ record }) {
                const types = {
                    dv: '域名型(DV)',
                    ov: '企业型(OV)',
                };
                return `${record.brand}-${types[record.type]}`;
            }
        },
        {
            dataIndex: 'domains',
            title: '域名',
            render({ value }) {
                return value || '--';
            }
        },
        {
            title: '用户',
            dataIndex: 'user',
            align: 'center',
            render({ value }) {
                return value ? <UserModal id={value.id} text={value.name} /> : '游客';
            }
        },
        {
            width: 80,
            dataIndex: 'status',
            title: '订单状态',
            render({ value }) {
                return <SslOrderStatus status={value} />;
            }
        },
        {
            width: 150,
            dataIndex: 'create_time',
            title: '创建时间',
        },
    ];

    return <Content title={'订单管理'}>
        <Table
            search
            source={`/admin/product/ssl/order`}
            columns={columns}
        />
    </Content>;
}
