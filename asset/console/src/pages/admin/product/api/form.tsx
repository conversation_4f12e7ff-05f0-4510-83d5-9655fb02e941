import { Form, FormProps, FormWidgetProps, LinkButton, Loader, Result, useRequest } from '@topthink/common';
import { Form as BsForm, InputGroup } from 'react-bootstrap';
import ApiGateway from './gateway';

export default function ApiForm(props: Partial<FormProps>) {
    const { result: categories } = useRequest('/admin/product/api/categories');

    if (!categories) {
        return <Loader wrap />;
    }

    if (categories.length == 0) {
        return <Result
            status='warning'
            title='请先添加分类'
            extra={<LinkButton to='/admin/product/api/category'>添加分类</LinkButton>}
        />;
    }

    return <Form
        {...props}
        schema={{
            type: 'object',
            properties: {
                name: {
                    title: '关联API',
                    type: 'string',
                },
                category_id: {
                    title: '分类',
                    type: 'string',
                    anyOf: categories
                },
                title: {
                    title: '标题',
                    type: 'string'
                },
                description: {
                    title: '简介',
                    type: 'string'
                },
                content: {
                    title: '内容',
                    type: 'string'
                },
                identify_type: {
                    title: '认证类型',
                    type: 'number',
                    enum: [0, 1, 2],
                    enumNames: ['无需认证', '个人认证', '企业认证'],
                    default: 0
                },
                price_type: {
                    title: '价格类型',
                    type: 'number',
                    anyOf: [
                        {
                            title: '免费接口',
                            type: 'number',
                            const: 0
                        },
                        {
                            title: '会员接口',
                            type: 'number',
                            const: 1
                        },
                        {
                            title: '付费接口',
                            type: 'number',
                            const: 2
                        }
                    ],
                    default: 0,
                },
                logo: {
                    title: '图标',
                    type: 'string'
                }
            },
            dependencies: {
                price_type: {
                    oneOf: [
                        {
                            properties: {
                                price_type: {
                                    const: 2
                                },
                                trial_nums: {
                                    title: '试用次数',
                                    type: 'number',
                                    default: 0
                                },
                            },
                        }
                    ]
                }
            }
        }}
        uiSchema={{
            'ui:order': ['*', 'price_type', 'trial_nums', 'logo'],
            name: {
                'ui:widget': ({ value, onChange, id }: FormWidgetProps) => {
                    return <InputGroup id={id} className='mb-3'>
                        <BsForm.Control readOnly value={value || ''} placeholder='请选择关联的API接口' />
                        <ApiGateway onSelect={onChange} />
                    </InputGroup>;
                },
            },
            category_id: {
                'ui:placeholder': '请选择分类',
            },
            description: {
                'ui:widget': 'textarea',
                'ui:options': {
                    rows: 2
                }
            },
            content: {
                'ui:widget': 'editor',
                'ui:options': {
                    endpoint: '/upload/api',
                }
            },
            identify_type: {
                'ui:widget': 'radio',
                'ui:options': {
                    button: true
                }
            },
            price_type: {
                'ui:widget': 'radio',
                'ui:options': {
                    button: true
                }
            },
            logo: {
                'ui:widget': 'upload',
                'ui:options': {
                    endpoint: '/upload/api',
                }
            }
        }}
    />;
}
