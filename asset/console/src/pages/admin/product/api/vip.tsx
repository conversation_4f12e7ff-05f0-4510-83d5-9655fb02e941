import { Content, ModalForm, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';


export function Component() {
    return <Content title={'会员管理'}>
        <Table
            source={'/admin/product/api/vip'}
            rowKey={'user_id'}
            columns={[
                {
                    title: '用户',
                    dataIndex: ['user', 'name'],
                    render: ({ value, record }) => {
                        return <UserModal text={value} id={record.user_id} />;
                    }
                },
                {
                    title: '等级',
                    dataIndex: 'plan_name'
                },
                {
                    title: '过期时间',
                    dataIndex: 'expire_time',
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ record, action }) {
                        return <ModalForm
                            action={`/admin/product/api/vip/${record.user_id}/plan`}
                            text={'版本设置'}
                            onSuccess={action.reload}
                            formData={{
                                plan: record.plan,
                                expire_time: record.expire_time,
                            }}
                            schema={{
                                type: 'object',
                                required: ['plan'],
                                properties: {
                                    plan: {
                                        type: 'string',
                                        title: '版本',
                                        enum: [
                                            'senior',
                                            'gold',
                                        ],
                                        default: 'senior'
                                    },
                                    expire_time: {
                                        type: 'string',
                                        format: 'date-time',
                                        title: '有效期'
                                    },
                                }
                            }}
                            uiSchema={{
                                plan: {
                                    'ui:options': {
                                        enumNames: ['高级会员', '黄金会员']
                                    }
                                },
                            }}
                        />;
                    }
                }
            ]}
        />
    </Content>;
}
