import { Content, LinkButton, RequestButton, Space, Table } from '@topthink/common';
import { PriceType } from './badges';

export function Component() {
    return <Content title={'API 管理'}>
        <Table
            search
            sync
            toolBarRender={() => {
                return <LinkButton to={'/admin/product/api/create'}>添加API</LinkButton>;
            }}
            source={'/admin/product/api'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '分类',
                    dataIndex: ['category', 'name'],
                    width: 100
                },
                {
                    title: '名称',
                    dataIndex: 'title',
                    render({ value, record }) {
                        return <Space>
                            <PriceType value={record.price_type} />
                            <a href={record.url} target={'_blank'}>{value}</a>
                        </Space>;
                    }
                },
                {
                    title: '申请次数',
                    dataIndex: 'users_count',
                    align: 'center',
                    width: 90,
                },
                {
                    title: '试用次数',
                    dataIndex: 'trial_nums',
                    align: 'center',
                    width: 90,
                    render({ record }) {
                        if (record.price_type == 2) {
                            return record.trial_nums;
                        }
                        return '-';
                    }
                },
                {
                    title: '创建时间',
                    width: 150,
                    dataIndex: 'create_time',
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    width: 80,
                    align: 'center',
                    render({ value, action, record }) {
                        return value === 1 ?
                            <RequestButton
                                confirm={'确定要下架吗？'}
                                className={'text-success'}
                                onSuccess={action.reload}
                                variant={'link'}
                                url={`/admin/product/api/${record.id}/down`}
                                method={'post'}>已上架</RequestButton> :
                            <RequestButton
                                confirm={'确定要上架吗？'}
                                className={'text-danger'}
                                onSuccess={action.reload}
                                variant={'link'}
                                url={`/admin/product/api/${record.id}/up`}
                                method={'post'}>未上架</RequestButton>;
                    }
                },
                {
                    title: '推荐',
                    dataIndex: 'recommend_time',
                    width: 80,
                    align: 'center',
                    render({ record, action }) {
                        return record.recommend_time ?
                            <RequestButton onSuccess={action.reload} variant={'link'} url={`/admin/product/api/${record.id}/recommend`} method={'delete'}>取消推荐</RequestButton> :
                            <RequestButton onSuccess={action.reload} variant={'link'} url={`/admin/product/api/${record.id}/recommend`} method={'post'}>推荐</RequestButton>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 160,
                    align: 'right',
                    render({ record }) {
                        return <Space>
                            {record.price_type === 2 &&
                                <LinkButton variant={'link'} to={`/admin/product/api/${record.id}/package`}>套餐设置</LinkButton>}
                            <LinkButton variant={'link'} to={`/admin/product/api/${record.id}/stats`}>统计</LinkButton>
                            <LinkButton variant={'link'} to={`/admin/product/api/${record.id}/edit`}>编辑</LinkButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
