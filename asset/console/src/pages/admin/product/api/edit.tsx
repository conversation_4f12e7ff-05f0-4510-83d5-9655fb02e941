import { Card, Content, useNavigate, useRouteLoaderData } from '@topthink/common';
import ApiForm from './form';


export function Component() {
    const navigate = useNavigate();

    const result = useRouteLoaderData('admin/api') as Api;

    return <Content title={'编辑API'}>
        <Card>
            <ApiForm
                formData={result}
                method={'put'}
                action={`/admin/product/api/${result.id}`}
                onSuccess={() => {
                    navigate(-1);
                }}
            />
        </Card>
    </Content>;
}
