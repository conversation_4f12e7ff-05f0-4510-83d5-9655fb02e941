import { Content, useRouteLoaderData } from '@topthink/common';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';
import StatsChart from '@/components/stats-chart';

export const Component = () => {
    const api = useRouteLoaderData('admin/api') as Api;

    const [period, setPeriod] = useState('24hours');

    return <Content
        showBack
        title={`${api.title} 接口统计`}
        extra={
            <PeriodButtons periods={['24hours', 'yesterday', '30days', 'half-year']} period={period} onChange={setPeriod} />}
    >
        <StatsChart url={`/admin/product/api/${api.id}/stats`} period={period} />
    </Content>;
};
