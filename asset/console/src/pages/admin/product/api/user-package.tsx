import { Content, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';

export function Component() {
    return <Content>
        <Table
            source={'/admin/product/api/package'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '用户',
                    dataIndex: 'user',
                    align: 'center',
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: 'API',
                    dataIndex: 'api',
                    render({ value }) {
                        return <a href={value.url} target={'_blank'}>{value.title}</a>;
                    }
                },
                {
                    title: '套餐名称',
                    dataIndex: 'name'
                },
                {
                    title: '价格',
                    dataIndex: 'price',
                    valueType: 'currency'
                },
                {
                    title: '使用情况',
                    dataIndex: 'nums',
                    align: 'center',
                    render({ record }) {
                        return `${record.used_nums}/${record.total_nums}`;
                    }
                },
                {
                    title: '失效时间',
                    dataIndex: 'expire_time'
                }
            ]}
            search={{
                fields: ['user', 'api'],
                extraFields: {
                    status: '类型',
                    usage: '状态',
                },
                ui: {
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/user/search',
                        }
                    },
                    api: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/product/api/search',
                        }
                    },
                    status: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '用户购买',
                                    value: 1
                                },
                                {
                                    label: '系统赠送',
                                    value: 2
                                },
                            ]
                        }
                    },
                    usage: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '未使用',
                                    value: 1
                                },
                                {
                                    label: '使用中',
                                    value: 2
                                },
                                {
                                    label: '已用完',
                                    value: 3
                                },
                            ]
                        }
                    }
                }
            }}
        />
    </Content>;
}
