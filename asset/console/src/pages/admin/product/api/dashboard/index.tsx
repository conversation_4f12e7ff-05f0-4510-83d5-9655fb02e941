import { useState } from 'react';
import { Card, Content } from '@topthink/common';
import PeriodButtons from '@/components/period-buttons';
import StatsChart from '@/components/stats-chart';
import { Col, Row } from 'react-bootstrap';
import MetricTable from '@/components/metric-table';
import UserModal from '@/components/user-modal';

export function Component() {
    const [period, setPeriod] = useState('24hours');

    return <Content
        extra={<PeriodButtons period={period} onChange={setPeriod} />}
    >
        <StatsChart period={period} url={'/admin/product/api/stats'} />
        <Row className={'g-3'}>
            <Col md={6} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'用户'}
                        renderLabel={(item) => {
                            return <UserModal variant={'link'} id={item.user_id} text={item.x} />;
                        }}
                        url={`/admin/product/api/stats/metric/user`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={6} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'付费接口'}
                        url={`/admin/product/api/stats/metric/paid`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={6} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'免费接口'}
                        url={`/admin/product/api/stats/metric/free`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={6} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'会员接口'}
                        url={`/admin/product/api/stats/metric/vip`}
                        period={period}
                    />
                </Card>
            </Col>
        </Row>
    </Content>;
}
