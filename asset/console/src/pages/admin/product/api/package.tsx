import { Content, FormSchema, ModalForm, RequestButton, Space, Table, useRouteLoaderData } from '@topthink/common';

export function Component() {

    const result = useRouteLoaderData('admin/api') as Api;

    const schema: FormSchema = {
        type: 'object',
        properties: {
            nums: {
                type: 'number',
                title: '套餐次数',
                minimum: 0
            },
            price: {
                type: 'number',
                title: '价格',
                minimum: 0,
                format: 'currency'
            },
            original_price: {
                type: 'number',
                title: '原价',
                minimum: 0,
                format: 'currency'
            },
            agency_price: {
                type: 'number',
                title: '代理价',
                minimum: 0,
                format: 'currency'
            },
        },
        required: ['nums', 'price', 'original_price', 'agency_price'],
    };

    const uiSchema = {
        price: {
            'ui:options': {
                prefix: '￥',
                suffix: '元',
            }
        },
        original_price: {
            'ui:options': {
                prefix: '￥',
                suffix: '元',
            }
        },
        agency_price: {
            'ui:options': {
                prefix: '￥',
                suffix: '元',
            }
        }
    };


    return <Content title={`${result.title} 套餐设置`}>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    method={'post'}
                    action={`/admin/product/api/${result.id}/package`}
                    text={'添加套餐'}
                    schema={schema}
                    uiSchema={uiSchema}
                    onSuccess={reload}
                />;
            }}
            source={`/admin/product/api/${result.id}/package`}
            columns={[
                {
                    title: '套餐次数',
                    dataIndex: 'nums',
                },
                {
                    title: '价格',
                    dataIndex: 'price',
                    valueType: 'currency',
                    align: 'center',
                },
                {
                    title: '原价',
                    dataIndex: 'original_price',
                    valueType: 'currency',
                    align: 'center',
                },
                {
                    title: '代理价',
                    dataIndex: 'agency_price',
                    valueType: 'currency',
                    align: 'center',
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render: ({ record, action: { reload } }) => {
                        return <Space>
                            <ModalForm
                                method={'put'}
                                action={`/admin/product/api/${result.id}/package/${record.id}`}
                                formData={record}
                                text={'编辑'}
                                modalProps={{ header: '编辑套餐' }}
                                schema={schema}
                                uiSchema={uiSchema}
                                onSuccess={reload}
                            />
                            <RequestButton
                                url={`/admin/product/api/${result.id}/package/${record.id}`}
                                confirm={'确定删除该套餐？'}
                                method={'delete'}
                                onSuccess={reload}>
                                删除
                            </RequestButton>
                        </Space>;
                    },
                },
            ]}
        />
    </Content>;
}
