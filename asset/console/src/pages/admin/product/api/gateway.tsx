import InfoModal from '@/components/info-modal';
import { Table } from '@topthink/common';

interface Props {
    onSelect: (value: string) => void;
}

export default function ApiGateway({ onSelect }: Props) {

    return <InfoModal
        text={'选择API接口'}
        variant={'secondary'}
        size={'lg'}
        compact={true}
        renderChildren={({ action }) => {
            return <Table
                toolBarRender={false}
                card={false}
                source={'/admin/product/api/gateway/list'}
                columns={[
                    {
                        title: '标识',
                        dataIndex: 'name',
                    },
                    {
                        title: '描述',
                        dataIndex: 'desc',
                    },
                    {
                        title: '地址',
                        dataIndex: 'uri',
                    },
                    {
                        title: '操作',
                        width: 80,
                        key: 'action',
                        align: 'right',
                        render({ record }) {
                            if (record.name) {
                                return <a
                                    className={'btn btn-link'}
                                    onClick={() => {
                                        onSelect(record.name);
                                        action.close();
                                    }}
                                >选择</a>;
                            }
                        }
                    }
                ]}
                expandable={{
                    rowExpandable(record) {
                        return !!record.children;
                    },
                }}
                scroll={{ y: 350 }}
            />;
        }}
    />;
}
