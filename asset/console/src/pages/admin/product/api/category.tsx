import { Content, ModalForm, RequestButton, Space, Table } from '@topthink/common';

export function Component() {
    return <Content title={'分类管理'}>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    text={'添加分类'}
                    action={'/admin/product/api/category'}
                    method={'post'}
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                title: '分类名称',
                                type: 'string'
                            },
                            sort: {
                                title: '排序',
                                type: 'number',
                                default: 0
                            }
                        }
                    }}
                    onSuccess={reload}
                />;
            }}
            source={`/admin/product/api/category`}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '名称',
                    dataIndex: 'name',
                },
                {
                    title: '排序',
                    dataIndex: 'sort',
                    width: 80,
                    align: 'center'
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ record, action: { reload } }) {
                        return <Space>
                            <ModalForm
                                text={'编辑'}
                                modalProps={{ header: '编辑分类' }}
                                action={`/admin/product/api/category/${record.id}`}
                                method={'put'}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        name: {
                                            title: '分类名称',
                                            type: 'string'
                                        },
                                        sort: {
                                            title: '排序',
                                            type: 'number',
                                            default: 0
                                        }
                                    }
                                }}
                                formData={record}
                                onSuccess={reload}
                            />
                            <RequestButton
                                url={`/admin/product/api/category/${record.id}`}
                                method={'delete'}
                                variant={'link'}
                                onSuccess={reload}
                                confirm={'确定要删除吗？'}>删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
