import { Badge } from 'react-bootstrap';

export function IdentifyType({ value }: { value: number }) {

    switch (value) {
        case 1:
            return <Badge bg={'info'}>个人认证</Badge>;
        case 2:
            return <Badge bg={'info'}>企业认证</Badge>;
        default:
            return <>--</>;
    }
}

export function PriceType({ value }: { value: number }) {
    switch (value) {
        case 1:
            return <Badge bg={'orange'}>会员</Badge>;
        case 2:
            return <Badge bg={'danger'}>付费</Badge>;
        default:
            return <Badge bg={'success'}>免费</Badge>;
    }
}
