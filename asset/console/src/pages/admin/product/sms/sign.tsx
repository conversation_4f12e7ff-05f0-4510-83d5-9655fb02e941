import { Content, RequestButton, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';
import InfoModal from '@/components/info-modal';

export function Component() {

    return <Content>
        <Table
            search={{
                fields: ['user', 'name'],
                ui: {
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/user/search',
                        }
                    },
                }
            }}
            columns={[
                {
                    title: '签名ID',
                    dataIndex: 'hash_id',
                    width: 100
                },
                {
                    title: '签名名称',
                    dataIndex: 'name',
                },
                {
                    title: '用户',
                    dataIndex: 'user',
                    align: 'center',
                    width: 150,
                    render({ record }) {
                        return <UserModal id={record.user.id} text={record.user.name} />;
                    }
                },
                {
                    title: '创建时间',
                    width: 150,
                    dataIndex: 'create_time'
                },
                {
                    title: '审核状态',
                    width: 100,
                    dataIndex: 'status',
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return <Space><i className={'bi bi-circle-fill text-success'} />通过</Space>;
                            case 2:
                                return <Space><i className={'bi bi-circle-fill text-secondary'} />已冻结</Space>;
                            case -1:
                                return <Space><i className={'bi bi-circle-fill text-danger'} />未通过</Space>;
                            default:
                                return <Space><i className={'bi bi-circle-fill text-warning'} />审核中</Space>;
                        }
                    }
                },
                {
                    title: '操作',
                    align: 'right',
                    key: 'action',
                    width: 100,
                    render({ record, action }) {
                        return <Space>
                            <InfoModal
                                text={'详情'}
                                renderChildren={() => {
                                    return <>
                                        <dl className='row'>
                                            <dd className='col-2'>签名名称</dd>
                                            <dd className='col-10'>{record.name}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>签名来源</dd>
                                            <dd className='col-10'>{record.source}</dd>
                                        </dl>
                                        <dl className='row'>
                                            <dd className='col-2'>场景说明</dd>
                                            <dd className='col-10'>{record.remark}</dd>
                                        </dl>
                                    </>;
                                }}
                            />
                            {record.status === 1 ?
                                <RequestButton url={`/admin/product/sms/sign/${record.id}/freeze`} method={'post'} confirm={'确定要冻结吗？'} onSuccess={action.reload}>冻结</RequestButton> :
                                <RequestButton url={`/admin/product/sms/sign/${record.id}/unfreeze`} method={'post'} confirm={'确定要解冻吗？'} onSuccess={action.reload}>解冻</RequestButton>}
                        </Space>;
                    }
                }
            ]}
            source={'/admin/product/sms/sign'}
        />
    </Content>;
}
