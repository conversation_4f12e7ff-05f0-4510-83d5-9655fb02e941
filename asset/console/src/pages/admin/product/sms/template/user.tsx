import { ModalForm, RequestButton, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';
import InfoModal from '@/components/info-modal';

export function Component() {
    return <Table
        source={`/admin/product/sms/template?type=user`}
        search={{
            fields: ['user', 'name'],
            extraFields: {
                content: '模板内容'
            },
            ui: {
                user: {
                    'ui:widget': 'typeahead',
                    'ui:options': {
                        endpoint: '/admin/user/search',
                    }
                },
            }
        }}
        columns={[
            {
                title: '模板ID',
                dataIndex: 'hash_id',
                width: 100
            },
            {
                title: '模板名称',
                dataIndex: 'name',
            },
            {
                title: '用户',
                dataIndex: 'user',
                align: 'center',
                width: 150,
                render({ record }) {
                    return <UserModal id={record.user.id} text={record.user.name} />;
                }
            },
            {
                title: '模板类型',
                dataIndex: 'type',
                width: 100,
                render({ value }) {
                    switch (value) {
                        case 1:
                            return '通知短信';
                        case 2:
                            return '营销短信';
                        default:
                            return '未知';
                    }
                }
            },
            {
                title: '创建时间',
                width: 150,
                dataIndex: 'create_time'
            },
            {
                title: '审核状态',
                width: 100,
                dataIndex: 'status',
                render({ value }) {
                    switch (value) {
                        case 1:
                            return <Space><i className={'bi bi-circle-fill text-success'} />通过</Space>;
                        case 2:
                            return <Space><i className={'bi bi-circle-fill text-secondary'} />已冻结</Space>;
                        case -1:
                            return <Space><i className={'bi bi-circle-fill text-danger'} />未通过</Space>;
                        default:
                            return <Space><i className={'bi bi-circle-fill text-warning'} />审核中</Space>;
                    }
                }
            },
            {
                title: '操作',
                align: 'right',
                key: 'action',
                width: 120,
                render({ record, action }) {
                    return <Space>
                        {record.status > 0 && (record.status === 1 ?
                            <RequestButton url={`/admin/product/sms/template/${record.id}/freeze`} method={'post'} confirm={'确定要冻结吗？'} onSuccess={action.reload}>冻结</RequestButton> :
                            <RequestButton url={`/admin/product/sms/template/${record.id}/unfreeze`} method={'post'} confirm={'确定要解冻吗？'} onSuccess={action.reload}>解冻</RequestButton>)}
                        <InfoModal
                            text={'详情'}
                            renderChildren={() => {
                                return <>
                                    <dl className='row'>
                                        <dd className='col-2'>模板名称</dd>
                                        <dd className='col-10'>{record.name}</dd>
                                    </dl>
                                    <dl className='row'>
                                        <dd className='col-2'>模板内容</dd>
                                        <dd className='col-10'>{record.sign && `【${record.sign.name}】`}{record.content}</dd>
                                    </dl>
                                    <dl className='row'>
                                        <dd className='col-2'>场景说明</dd>
                                        <dd className='col-10'>{record.remark}</dd>
                                    </dl>
                                </>;
                            }}
                        />
                        <ModalForm
                            text={'编辑'}
                            modalProps={{ header: '编辑模板' }}
                            action={`/admin/product/sms/template/${record.id}`}
                            method={'put'}
                            schema={{
                                type: 'object',
                                properties: {
                                    type: {
                                        type: 'string',
                                        title: '模板类型',
                                        enum: [1, 2],
                                        enumNames: ['通知短信', '营销短信'],
                                        default: 1
                                    },
                                }
                            }}
                            uiSchema={{
                                type: {
                                    'ui:widget': 'radio',
                                },
                            }}
                            formData={record}
                            onSuccess={action.reload}
                        />
                    </Space>;
                }
            }
        ]}
    />;
}
