import { LinkButton, RequestButton, Space, Table } from '@topthink/common';
import InfoModal from '@/components/info-modal';

export function Component() {
    return <Table
        toolBarRender={() => {
            return <LinkButton to={'/admin/product/sms/template/create'}>添加公共模板</LinkButton>;
        }}
        source={`/admin/product/sms/template?type=public`}
        columns={[
            {
                title: '模板ID',
                dataIndex: 'hash_id',
                width: 100
            },
            {
                title: '模板名称',
                dataIndex: 'name',
            },
            {
                title: '模板类型',
                dataIndex: 'type',
                width: 100,
                render({ value }) {
                    switch (value) {
                        case 1:
                            return '通知短信';
                        case 2:
                            return '营销短信';
                        default:
                            return '未知';
                    }
                }
            },
            {
                title: '创建时间',
                width: 150,
                dataIndex: 'create_time'
            },
            {
                title: '操作',
                align: 'right',
                width: 100,
                key: 'action',
                render({ record, action }) {
                    return <Space>
                        <InfoModal
                            text={'详情'}
                            renderChildren={() => {
                                return <>
                                    <dl className='row'>
                                        <dd className='col-2'>模板内容</dd>
                                        <dd className='col-10'>{record.content}</dd>
                                    </dl>
                                </>;
                            }}
                        />
                        <RequestButton onSuccess={action.reload} url={`/admin/product/sms/template/${record.id}`} method={'delete'} confirm={'确定要删除吗？'}>删除</RequestButton>
                    </Space>;
                }
            }
        ]}
    />;
}
