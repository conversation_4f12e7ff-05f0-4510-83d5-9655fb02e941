import { Content, useLoaderData } from '@topthink/common';
import SmsTemplateForm from '@/pages/sms/template/form';

export function Component() {

    const template = useLoaderData() as SmsTemplate;

    return <Content title={'修改公共模板'}>
        <SmsTemplateForm
            formData={template}
            method={'put'}
            action={`/admin/product/sms/template/${template.id}`}
        />
    </Content>;
}
