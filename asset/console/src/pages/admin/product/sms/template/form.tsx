import { Card, Form, FormProps, useNavigate } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

export default function TemplateForm(props: Omit<FormProps<SmsTemplate>, 'schema' | 'uiSchema'>) {
    const navigate = useNavigate();

    return <Card>
        <Row>
            <Col md={6}>
                <Form
                    {...props}
                    onSuccess={() => navigate('/admin/product/sms/template/public')}
                    schema={{
                        type: 'object',
                        required: ['name', 'content'],
                        properties: {
                            name: {
                                type: 'string',
                                title: '模板名称',
                            },
                            content: {
                                type: 'string',
                                title: '模板内容',
                            }
                        }
                    }}
                    uiSchema={{
                        name: {
                            'ui:placeholder': '请输入模板名称'
                        },
                        content: {
                            'ui:widget': 'textarea',
                            'ui:options': {
                                rows: 4,
                                placeholder: '请输入模板内容',
                            },
                            'ui:help': '变量格式：${code}<br />示例：您的验证码为：${code}，该验证码 5 分钟内有效，请勿泄漏于他人。'
                        }
                    }}
                />
            </Col>
            <Col md={6}>

            </Col>
        </Row>
    </Card>;
}
