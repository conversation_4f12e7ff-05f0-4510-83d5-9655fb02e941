import { useState } from 'react';
import { Card, Content, Tooltip } from '@topthink/common';
import PeriodButtons from '@/components/period-buttons';
import Overview from '@/pages/admin/product/sms/dashboard/overview';
import StatsChart from '@/components/stats-chart';
import { Col, Row } from 'react-bootstrap';
import MetricTable from '@/components/metric-table';
import UserModal from '@/components/user-modal';

export function Component() {
    const [period, setPeriod] = useState('24hours');

    return <Content
        extra={
            <PeriodButtons period={period} periods={['24hours', 'yesterday', '30days', 'last-month', 'half-year']} onChange={setPeriod} />}
    >
        <Overview period={period} />
        <StatsChart
            url={`/admin/product/sms/stats`}
            period={period}
            type={'line'}
            isStack={false}
            series={
                {
                    nums: {
                        text: '计费',
                        color: '#07a8ff'
                    },
                    success: {
                        text: '成功',
                        color: '#5B8FF9'
                    },
                    fail: {
                        text: '失败',
                        color: '#F4664A'
                    }
                }
            }
        />
        <Row className={'g-3'}>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'用户'}
                        renderLabel={(item) => {
                            return <UserModal variant={'link'} id={item.user_id} text={item.x} />;
                        }}
                        url={`/admin/product/sms/stats/metric/user`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'签名'}
                        url={`/admin/product/sms/stats/metric/sign`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'模板'}
                        renderLabel={(item) => {
                            if (item.content) {
                                return <Tooltip tooltip={item.content} placement={'top'}>
                                    <span role={'button'}>{item.x}</span>
                                </Tooltip>;
                            }
                            return item.x;
                        }}
                        url={`/admin/product/sms/stats/metric/template`}
                        period={period}
                    />
                </Card>
            </Col>
        </Row>
    </Content>;
}
