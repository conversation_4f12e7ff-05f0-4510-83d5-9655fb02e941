import UserModal from '@/components/user-modal';
import { Content, RequestButton, Space, Table } from '@topthink/common';
import dayjs from 'dayjs';

export function Component() {

    return <Content>
        <Table
            source={'/admin/product/sms/package'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '套餐名称',
                    dataIndex: 'name'
                },
                {
                    title: '用户',
                    dataIndex: 'user',
                    align: 'center',
                    width: 120,
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '套餐类型',
                    dataIndex: 'type',
                    width: 100,
                    render({ value }) {
                        switch (value) {
                            case 1:
                                return '通知短信';
                            case 2:
                                return '营销短信';
                            default:
                                return '未知';
                        }
                    }
                },
                {
                    title: '价格',
                    dataIndex: 'price',
                    valueType: 'currency',
                    width: 100,
                    align: 'center'
                },
                {
                    title: '使用情况',
                    dataIndex: 'nums',
                    align: 'center',
                    render({ record }) {
                        return `${record.used_nums}/${record.total_nums}`;
                    }
                },
                {
                    title: '失效时间',
                    dataIndex: 'expire_time',
                    width: 150,
                    render({ value }) {
                        const isExpired = dayjs(value).isBefore(dayjs());
                        return <span className={isExpired ? 'text-muted' : ''}>{value}</span>;
                    }
                },
                {
                    title: '操作',
                    align: 'right',
                    width: 150,
                    render({ record, action }) {
                        return <Space>
                            {dayjs(record.expire_time).isAfter(dayjs()) && record.used_nums < record.total_nums && (
                                <RequestButton
                                    url={`/admin/product/sms/package/${record.id}/disable`}
                                    method={'post'}
                                    confirm={'此操作无法撤销，确定要让该套餐失效吗？'}
                                    onSuccess={action.reload}
                                >使失效</RequestButton>
                            )}
                            <RequestButton
                                url={`/admin/product/sms/package/${record.id}/amend`}
                                method={'post'}
                                confirm={'确定要自动修正已使用次数吗？'}
                                onSuccess={action.reload}
                            >修正</RequestButton>
                            <RequestButton
                                url={`/admin/product/sms/package/${record.id}/sync`}
                                method={'post'}
                                confirm={'确定要自动同步已使用次数吗？'}
                                onSuccess={action.reload}
                            >同步</RequestButton>
                        </Space>;
                    }
                }
            ]}
            search={{
                fields: ['user', 'type'],
                extraFields: {
                    status: '来源',
                    usage: '状态',
                },
                ui: {
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/user/search',
                        }
                    },
                    type: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '通知短信',
                                    value: 1
                                },
                                {
                                    label: '营销短信',
                                    value: 2
                                },
                            ]
                        }
                    },
                    status: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '用户购买',
                                    value: 1
                                },
                                {
                                    label: '系统赠送',
                                    value: 2
                                },
                            ]
                        }
                    },
                    usage: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '未使用',
                                    value: 1
                                },
                                {
                                    label: '使用中',
                                    value: 2
                                },
                                {
                                    label: '已用完',
                                    value: 3
                                },
                                {
                                    label: '已超用',
                                    value: 4
                                },
                            ]
                        }
                    }
                }
            }}
        />
    </Content>;
}
