import UserModal from '@/components/user-modal';
import { Content, ModalForm, request, Table } from '@topthink/common';
import { Form } from 'react-bootstrap';

export const Component = () => {
    return (
        <Content>
            <Table
                source={`/admin/product/sms/user`}
                search={{
                    fields: ['user'],
                    ui: {
                        user: {
                            'ui:widget': 'typeahead',
                            'ui:options': {
                                endpoint: '/admin/user/search',
                            }
                        },
                    }
                }}
                columns={[
                    {
                        title: 'ID',
                        dataIndex: 'id',
                        width: 80
                    },
                    {
                        title: '用户',
                        dataIndex: 'user',
                        render: ({ value }) => {
                            return <UserModal text={value.name} id={value.id} />;
                        }
                    },
                    {
                        title: '批量发送',
                        dataIndex: 'batch_send',
                        width: 120,
                        align: 'center',
                        render({ value, record, action }) {
                            return <Form.Check
                                checked={value === 1}
                                type={'switch'}
                                onChange={async (e) => {
                                    await request({
                                        method: 'post',
                                        url: `/admin/product/sms/user/${record.id}/batch_send`,
                                        data: {
                                            batch_send: e.target.checked ? 1 : 0
                                        }
                                    });

                                    action.reload(true);
                                }}
                            />;
                        }
                    },
                    {
                        title: '当前频率',
                        dataIndex: 'current_rate_limit',
                        width: 120,
                        align: 'center',
                        render({ value }) {
                            return value + '/小时';
                        }
                    },
                    {
                        title: '频率限制',
                        dataIndex: 'rate_limit',
                        width: 120,
                        align: 'center',
                        render({ record, action }) {
                            return <ModalForm
                                text={`${record.rate_limit}/小时`}
                                action={`/admin/product/sms/user/${record.id}/rate_limit`}
                                method={'post'}
                                modalProps={{ header: '设置频率限制' }}
                                onSuccess={action.reload}
                                uiSchema={{
                                    rate_limit: {
                                        'ui:options': {
                                            label: false
                                        }
                                    }
                                }}
                                formData={{
                                    rate_limit: record.rate_limit
                                }}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        rate_limit: {
                                            type: 'number',
                                            title: '频率限制',
                                            minimum: 0
                                        }
                                    }
                                }}
                            />;
                        }
                    }
                ]}
            />
        </Content>
    );
};
