import { Columns, Content, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';

export function Component() {

    const columns: Columns<AssistantHecong> = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 60
        },
        {
            title: '团队ID',
            dataIndex: 'ent_id',
            width: 60
        },
        {
            title: '用户',
            dataIndex: 'user',
            align: 'center',
            render({ value }) {
                return <UserModal id={value.id} text={value.name} />;
            }
        },
        {
            title: '版本',
            dataIndex: 'plan',
            render({ value }) {
                switch (value) {
                    case 'trial':
                        return '试用版';
                    case 'standard':
                        return '标准版';
                    default:
                        return '未知';
                }
            }
        },
        {
            title: '座席数',
            dataIndex: 'seat',
            align: 'center',
            render({ value, record }) {
                if (record.plan === 'trial') {
                    return 50;
                }
                return value;
            }
        },
        {
            title: '到期时间',
            dataIndex: 'expire_time'
        },
        {
            title: '创建时间',
            dataIndex: 'create_time'
        }
    ];

    return <Content title={'合从客服'}>
        <Table
            search
            source={'/admin/product/assistant/hecong'}
            columns={columns}
        />
    </Content>;
}
