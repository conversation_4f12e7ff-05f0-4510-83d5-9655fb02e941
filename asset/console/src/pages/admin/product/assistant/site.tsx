import { Columns, Content, ModalForm, Space, Table } from '@topthink/common';
import classNames from 'classnames';

export function Component() {

    const columns: Columns<AssistantSite> = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: 60
        },
        {
            title: '名称',
            dataIndex: 'name',
            width: 140,
            className: 'text-truncate',
        },
        {
            title: '地址',
            align: 'left',
            dataIndex: 'url',
            render({ value }) {
                return <a className={'text-truncate d-block'} href={value} target={'_blank'}>{value}</a>;
            }
        },
        {
            title: '版本',
            dataIndex: 'plan',
            width: 80,
            render({ value }) {
                switch (value) {
                    case 'trial':
                        return '免费版';
                    case 'standard':
                        return '标准版';
                    default:
                        return '未知';
                }
            }
        },
        {
            title: '状态',
            dataIndex: 'status',
            width: 80,
            align: 'center',
            render({ value }) {
                return <i className={classNames('bi bi-circle-fill fs-7', {
                    'text-success': value === 1,
                    'text-danger': value === 0,
                    'text-warning': value === 2,
                })} />;
            }
        },
        {
            title: '创建时间',
            width: 160,
            dataIndex: 'create_time',
        },
        {
            title: '到期时间',
            width: 160,
            dataIndex: 'expire_time',
            render({ record }) {
                if (record.plan === 'trial') {
                    return '--';
                }
                return record.expire_time;
            }
        },
        {
            title: '操作',
            align: 'right',
            width: 100,
            key: 'action',
            render({ record, action }) {
                return <Space>
                    {<ModalForm
                        action={`/admin/product/assistant/site/${record.id}/plan`}
                        text={'版本设置'}
                        onSuccess={action.reload}
                        formData={{
                            plan: record.plan,
                            expire_time: record.expire_time
                        }}
                        schema={{
                            type: 'object',
                            properties: {
                                plan: {
                                    type: 'string',
                                    title: '版本',
                                    enum: [
                                        'trial',
                                        'standard'
                                    ],
                                },
                            },
                            dependencies: {
                                plan: {
                                    oneOf: [
                                        {
                                            properties: {
                                                plan: {
                                                    const: 'standard'
                                                },
                                                expire_time: {
                                                    type: 'string',
                                                    format: 'date-time',
                                                    title: '有效期'
                                                },
                                            }
                                        }
                                    ]
                                }
                            }
                        }}
                        uiSchema={{
                            plan: {
                                'ui:widget': 'radio',
                                'ui:options': {
                                    enumNames: ['免费版', '标准版'],
                                    button: true
                                }
                            }
                        }}
                    />}
                </Space>;
            }
        }
    ];

    return <Content title={'网站列表'}>
        <Table
            search
            source={'/admin/product/assistant/site'}
            columns={columns}
            tableLayout={'fixed'}
        />
    </Content>;
}
