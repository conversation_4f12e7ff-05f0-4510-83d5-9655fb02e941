import PeriodButtons from '@/components/period-buttons';
import { Card, Content } from '@topthink/common';
import { useState } from 'react';
import Stats from './stats';
import { Col, Row } from 'react-bootstrap';
import MetricTable from '@/components/metric-table';
import UserModal from '@/components/user-modal';

export const Component = () => {

    const [period, setPeriod] = useState('24hours');

    return <Content
        extra={<PeriodButtons period={period} onChange={setPeriod} />}
    >
        <Stats period={period} />
        <Row className={'g-3'}>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'用户'}
                        metric={'Tokens'}
                        renderLabel={(item) => {
                            return <UserModal variant={'link'} id={item.user_id} text={item.x} />;
                        }}
                        url={`/admin/product/ai/stats/metric/user`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'应用'}
                        metric={'Tokens'}
                        url={`/admin/product/ai/stats/metric/app`}
                        period={period}
                    />
                </Card>
            </Col>
            <Col md={4} className='d-flex'>
                <Card className='flex-fill'>
                    <MetricTable
                        title={'模型'}
                        metric={'Tokens'}
                        url={`/admin/product/ai/stats/metric/model`}
                        period={period}
                    />
                </Card>
            </Col>
        </Row>
    </Content>;
};
