import { Card, Loader, styled } from '@topthink/common';
import { Nav } from 'react-bootstrap';
import { Suspense, useState } from 'react';
import StatsChart from '@/components/stats-chart';

interface Props {
    period: string;
}

export default function Stats({ period }: Props) {

    const [tab, setTab] = useState('user');

    return <Card>
        <Nav variant='tabs' defaultActiveKey={tab} onSelect={key => key && setTab(key)}>
            <Nav.Item>
                <Nav.Link eventKey='user'>用户</Nav.Link>
            </Nav.Item>
            <Nav.Item>
                <Nav.Link eventKey='app'>应用</Nav.Link>
            </Nav.Item>
        </Nav>
        <Container key={tab}>
            <Suspense fallback={<Loader />}>
                <StatsChart
                    card={false}
                    formatNumber
                    url={`/admin/product/ai/stats/basic/${tab}`}
                    series={{
                        chat: { text: '会话', color: '#3c60ff' },
                        image: { text: '画图', color: '#ffc107' },
                        video: { text: '视频', color: '#ff5733' },
                        audio: { text: '语音', color: '#e83e8c' },
                        text: { text: '文本', color: '#198754' },
                        plugin: { text: '插件', color: '#07a8ff' },
                    }}
                    period={period}
                />
            </Suspense>
        </Container>
    </Card>;
}

const Container = styled.div`
    padding: 2rem .25rem 0;
    position: relative;
`;
