import { ModalForm } from '@topthink/common';

export default function ArtistConfig({ plugin, onSuccess }: { plugin: AiPlugin, onSuccess?: () => void }) {
    return <ModalForm
        text={'配置'}
        action={`/admin/product/ai/plugin/${plugin.id}/config`}
        modalProps={{
            header: 'Artist配置',
            scrollable: true,
            size: 'lg'
        }}
        transformData={(data) => ({ models: data })}
        formData={plugin.config?.models || []}
        omitExtraData
        schema={{
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: {
                        type: 'string',
                        title: '工具名'
                    },
                    name: {
                        type: 'string',
                        title: '工具标识'
                    },
                    code: {
                        type: 'string',
                        title: '模型标识',
                    },
                    size: {
                        type: 'object',
                        title: '预设尺寸',
                        properties: {
                            square: {
                                type: 'string',
                                title: '方形'
                            },
                            vertical: {
                                type: 'string',
                                title: '竖屏'
                            },
                            horizontal: {
                                type: 'string',
                                title: '横屏'
                            }
                        }
                    },
                    image: {
                        type: 'boolean',
                        title: '是否支持参考图'
                    },
                }
            },
            required: ['title', 'name', 'code']
        }}
        uiSchema={{
            items: {
                title: {
                    'ui:col': 4,
                },
                name: {
                    'ui:col': 4,
                },
                code: {
                    'ui:col': 4,
                },
                size: {
                    square: {
                        'ui:col': 4,
                        'ui:placeholder': '1024x1024'
                    },
                    vertical: {
                        'ui:col': 4,
                        'ui:placeholder': '720x1280'
                    },
                    horizontal: {
                        'ui:col': 4,
                        'ui:placeholder': '1280x720'
                    },
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
