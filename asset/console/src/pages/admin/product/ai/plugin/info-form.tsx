import { ModalForm, ModalFormProps, styled } from '@topthink/common';

export default function InfoForm(props: Omit<ModalFormProps, 'schema' | 'uiSchema'>) {
    return <ModalForm
        {...props}
        omitExtraData={true}
        schema={{
            type: 'object',
            properties: {
                icon: {
                    type: 'string',
                    title: '图标',
                },
                type: {
                    type: 'string',
                    title: '类型',
                },
                title: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },
                sort: {
                    type: 'number',
                    title: '排序',
                    default: 0
                }
            },
        }}
        uiSchema={{
            icon: {
                'ui:widget': 'avatar',
                'ui:options': {
                    as: Avatar,
                    endpoint: '/upload/ai',
                    label: false,
                }
            },
            name: {
                'ui:autofocus': true,
                'ui:placeholder': '输入插件名称'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入插件描述',
                'ui:options': {
                    rows: 3
                }
            },
            type: {
                'ui:widget': 'typeahead',
                'ui:options': {
                    endpoint: '/admin/product/ai/plugin/types',
                    placeholder: '选择插件类型',
                    async: false
                }
            }
        }}
    />;
}

const Avatar = styled.div`
    margin: 0 auto;
`;
