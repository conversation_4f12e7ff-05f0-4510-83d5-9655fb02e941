import { ModalForm } from '@topthink/common';

export default function VisionConfig({ plugin, onSuccess }: { plugin: AiPlugin, onSuccess?: () => void }) {
    return <ModalForm
        text={'配置'}
        action={`/admin/product/ai/plugin/${plugin.id}/config`}
        modalProps={{
            header: 'Vision配置',
            scrollable: true,
            size: 'lg'
        }}
        transformData={(data) => ({ models: data })}
        formData={plugin.config?.models || []}
        omitExtraData
        schema={{
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    title: {
                        type: 'string',
                        title: '工具名'
                    },
                    name: {
                        type: 'string',
                        title: '工具标识'
                    },
                    code: {
                        type: 'string',
                        title: '模型标识',
                    }
                },
                required: ['title', 'name', 'code']
            }
        }}
        uiSchema={{
            items: {
                title: {
                    'ui:col': 4,
                },
                name: {
                    'ui:col': 4,
                },
                code: {
                    'ui:col': 4,
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
