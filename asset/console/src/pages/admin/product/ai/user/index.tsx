import UserModal from '@/components/user-modal';
import { formatLongNumber } from '@/utils/format';
import { Content, Table } from '@topthink/common';
import Stats from './stats';

export const Component = () => {
    return (
        <Content>
            <Table
                source={`/admin/product/ai/user`}
                search={{
                    fields: ['user'],
                    ui: {
                        user: {
                            'ui:widget': 'typeahead',
                            'ui:options': {
                                endpoint: '/admin/user/search',
                            }
                        },
                    }
                }}
                columns={[
                    {
                        title: 'ID',
                        dataIndex: 'id',
                        width: 80
                    },
                    {
                        title: '用户',
                        dataIndex: 'user',
                        render: ({ value }) => {
                            return <UserModal text={value.name} id={value.id} />;
                        }
                    },
                    {
                        title: '余额',
                        dataIndex: 'token',
                        width: 120,
                        render: ({ value }) => {
                            return formatLongNumber(value);
                        }
                    },
                    {
                        title: '开通时间',
                        width: 180,
                        dataIndex: 'create_time',
                    },
                    {
                        title: '操作',
                        width: 100,
                        align: 'right',
                        key: 'action',
                        render: ({ record }) => {
                            return <Stats id={record.user.id} />;
                        }
                    }
                ]}
            />
        </Content>
    );
};
