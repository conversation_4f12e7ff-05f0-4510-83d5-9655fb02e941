import InfoModal from '@/components/info-modal';
import StatsChart from '@/components/stats-chart';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';

interface Props {
    id: string;
}

export default function Stats({ id }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            const [period, setPeriod] = useState('24hours');

            return <>
                <PeriodButtons period={period} onChange={setPeriod} />
                <StatsChart
                    card={false}
                    formatNumber
                    url={`/admin/product/ai/user/${id}/stats`}
                    series={{
                        chat: { text: '会话', color: '#3c60ff' },
                        image: { text: '画图', color: '#ffc107' },
                        video: { text: '视频', color: '#ff5733' },
                        audio: { text: '语音', color: '#e83e8c' },
                        text: { text: '文本', color: '#198754' },
                        plugin: { text: '插件', color: '#07a8ff' },
                    }}
                    period={period}
                />
            </>;
        }}
        text={'统计'}
    />;
}
