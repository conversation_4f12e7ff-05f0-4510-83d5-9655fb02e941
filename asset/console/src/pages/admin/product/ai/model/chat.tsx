import {
    FormSchema,
    FormUiSchema,
    ModalForm,
    request,
    RequestButton,
    Space,
    Table,
    useRequestData
} from '@topthink/common';
import { Badge, FormCheck } from 'react-bootstrap';

export const Component = () => {
    const channels = useRequestData<{ name: string, code: string }[]>();

    const schema: FormSchema = {
        type: 'object',
        properties: {
            channel: {
                type: 'string',
                title: '渠道',
                enum: channels.map(({ code }) => code),
                enumNames: channels.map(({ name }) => name),
                default: 'openai'
            },
            label: {
                type: 'string',
                title: '名称'
            },
            description: {
                type: 'string',
                title: '描述'
            },
            code: {
                type: 'string',
                title: '标识',
                description: '我们对外提供的模型名'
            },
            version: {
                type: 'string',
                title: '版本',
                description: '实际厂商那边的模型名，留空则和标识一致'
            },
            factor: {
                type: 'string',
                title: '费率',
                description: '区分输入输出时使用"/"隔开'
            },
            params: {
                type: 'object',
                title: '其他参数',
                properties: {
                    context_tokens: {
                        type: 'number',
                        title: '上下文长度'
                    },
                    tool: {
                        type: 'boolean',
                        title: '工具调用'
                    },
                    vision: {
                        type: 'boolean',
                        title: '视觉'
                    },
                    reasoning: {
                        type: 'boolean',
                        title: '推理'
                    },
                }
            },
            sort: {
                type: 'number',
                title: '排序',
                default: 0
            },
        },
    };

    const uiSchema: FormUiSchema = {
        description: {
            'ui:widget': 'textarea',
            'ui:options': {
                rows: 3
            }
        },
        code: {
            'ui:col': 6
        },
        version: {
            'ui:col': 6
        },
        params: {
            tool: {
                'ui:col': 4
            },
            vision: {
                'ui:col': 4
            },
            reasoning: {
                'ui:col': 4
            },
        }
    };

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                method='post'
                modalProps={{ size: 'lg' }}
                transformData={data => ({
                    type: 'chat',
                    ...data,
                })}
                action={`/admin/product/ai/model`}
                text={'添加模型'}
                schema={schema}
                uiSchema={uiSchema}
                onSuccess={action.reload}
            />;
        }}
        source={`/admin/product/ai/model?type=chat`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                render({ value, record }) {
                    return <Space>
                        <Badge>{channels.find(({ code }) => code === record.channel)?.name || '未知'}</Badge>
                        {value}
                    </Space>;
                }
            },
            {
                dataIndex: 'code',
                title: '标识',
                width: 250,
                render({ value, record }) {
                    return <>
                        {value}
                        {record.version && record.version !== record.code ?
                            <span className={'text-muted'}> ({record.version})</span> : ''}
                    </>;
                }
            },
            {
                dataIndex: 'factor',
                title: '费率',
                width: 120,
            },
            {
                dataIndex: 'sort',
                title: '排序',
                width: 50,
                align: 'center'
            },
            {
                key: 'hide',
                title: '显示',
                width: 50,
                align: 'center',
                render({ record, action }) {
                    const status = 2;
                    return <FormCheck
                        checked={(record.status & status) === 0}
                        type={'switch'}
                        onChange={async (e) => {
                            await request({
                                method: 'post',
                                url: `/admin/product/ai/model/${record.id}/status`,
                                data: {
                                    status: e.target.checked ? record.status & ~status : record.status | status
                                }
                            });

                            action.reload(true);
                        }}
                    />;
                }
            },
            {
                key: 'action',
                title: '操作',
                align: 'right',
                width: 100,
                render({ record, action }) {
                    return <Space>
                        <ModalForm
                            text={'编辑'}
                            method='put'
                            modalProps={{ size: 'lg' }}
                            action={`/admin/product/ai/model/${record.id}`}
                            schema={schema}
                            uiSchema={uiSchema}
                            formData={record}
                            onSuccess={action.reload}
                        />
                        <RequestButton
                            confirm={'确定要删除吗？'}
                            url={`/admin/product/ai/model/${record.id}`}
                            method='delete'
                            onSuccess={action.reload}
                        >删除</RequestButton>
                    </Space>;
                }
            }
        ]}
    />;
};
