import { Content, NumberFormat, RequestButton, Table } from '@topthink/common';

export function Component() {
    return <Content title='退款管理'>
        <Table
            source={'/admin/finance/refund'}
            columns={[
                {
                    title: '退款单号',
                    dataIndex: 'refund_no',
                    width: 160,
                },
                {
                    title: '名称',
                    dataIndex: ['charge', 'full_subject'],
                },
                {
                    title: '退款金额',
                    dataIndex: ['charge', 'net_amount'],
                    width: 220,
                    render({ value }) {
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    title: '退款回调',
                    align: 'center',
                    dataIndex: 'status',
                    width: 80,
                    render({ value, record, action }) {
                        switch (value) {
                            case 1:
                                return '已完成';
                            case 2:
                                return '执行中';
                            case 0:
                                return <RequestButton
                                    url={`/admin/finance/refund/${record.id}/retry`}
                                    variant={'link'}
                                    method={'post'}
                                    onSuccess={action.reload}
                                >
                                    重试
                                </RequestButton>;
                        }
                    }
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                    width: 150
                },
            ]}
        />
    </Content>;
}
