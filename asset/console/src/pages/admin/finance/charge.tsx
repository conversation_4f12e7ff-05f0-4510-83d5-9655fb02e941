import UserModal from '@/components/user-modal';
import { Columns, Content, NumberFormat, RequestButton, RequestConfig, Space, Table } from '@topthink/common';
import { Dropdown } from 'react-bootstrap';
import ChargeStatus from '@/components/charge-status';

export function Component() {

    const columns: Columns<Charge> = [
        {
            title: '支付单号',
            dataIndex: 'trade_no',
            width: 140
        },
        {
            title: '用户',
            dataIndex: 'user',
            align: 'center',
            render({ value }) {
                return value ? <UserModal id={value.id} text={value.name} /> : '游客';
            }
        },
        {
            title: '名称',
            dataIndex: 'full_subject',
        },
        {
            title: '金额',
            dataIndex: 'amount',
            align: 'center',
            render({ value, record }) {
                if (value != record.net_amount) {
                    return <Space size={5}>
                        <NumberFormat className='text-muted text-decoration-line-through' value={value / 100} />
                        <NumberFormat value={record.net_amount / 100} />
                    </Space>;
                }
                return <NumberFormat value={value / 100} />;
            }
        },
        {
            title: '交易状态',
            align: 'center',
            dataIndex: 'status',
            width: 80,
            render({ record }) {
                return <ChargeStatus charge={record} />;
            }
        },
        {
            title: '支付回调',
            align: 'center',
            key: 'webhook',
            width: 80,
            render({ record, action }) {
                if (!record.is_paid || record.revoke_time) {
                    return '--';
                }
                switch (record.status) {
                    case 1:
                        return '已完成';
                    case 2:
                        return '执行中';
                    case 0:
                        return <RequestButton
                            url={`/admin/finance/charge/${record.id}/retry`}
                            variant={'link'}
                            method={'post'}
                            onSuccess={action.reload}
                        >
                            重试
                        </RequestButton>;
                }
            }
        },
        {
            title: '创建时间',
            dataIndex: 'create_time',
            width: 150
        },
        {
            title: '操作',
            align: 'right',
            key: 'confirm',
            width: 100,
            render({ record, action }) {
                if (!record.is_paid) {
                    const paid = (channel: string): RequestConfig => {
                        return {
                            url: `/admin/finance/charge/${record.id}/paid`,
                            method: 'POST',
                            data: { channel }
                        };
                    };

                    return <Dropdown>
                        <Dropdown.Toggle variant='link' id='dropdown-channel'>
                            确认支付
                        </Dropdown.Toggle>
                        <Dropdown.Menu className={'shadow'} renderOnMount popperConfig={{ strategy: 'fixed' }}>
                            <RequestButton
                                as={Dropdown.Item}
                                url={paid('alipay')}
                                onSuccess={action.reload}
                            >
                                支付宝
                            </RequestButton>
                            <RequestButton
                                as={Dropdown.Item}
                                url={paid('wechat')}
                                onSuccess={action.reload}
                            >
                                微信
                            </RequestButton>
                            <RequestButton
                                as={Dropdown.Item}
                                url={paid('transfer')}
                                confirm='确定已对公转账支付吗？'
                                onSuccess={action.reload}
                            >
                                对公转账
                            </RequestButton>
                        </Dropdown.Menu>
                    </Dropdown>;
                }
                if (record.can_revoke) {
                    return <RequestButton
                        url={`/admin/finance/charge/${record.id}/revoke`}
                        confirm={'确定要撤销此交易吗？'}
                        method={'post'}
                        variant={'link'}
                        onSuccess={action.reload}
                    >
                        撤销
                    </RequestButton>;
                }
                return '--';
            }
        }
    ];

    return <Content>
        <Table
            scroll={{ x: 1000 }}
            search={{
                fields: ['trade_no', 'user', 'create_time', 'status'],
                extraFields: {
                    subject: '名称',
                    app: '所属应用',
                },
                order: ['trade_no', 'user', 'app', 'subject', '*'],
                ui: {
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/user/search',
                        }
                    },
                    app: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/application/search',
                            minLength: 0
                        }
                    },
                    create_time: {
                        'ui:widget': 'date-range'
                    },
                    status: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '未支付',
                                    value: 0
                                },
                                {
                                    label: '已支付',
                                    value: 1
                                },
                                {
                                    label: '已撤销',
                                    value: -1
                                }
                            ]
                        }
                    }
                }
            }}
            source={'/admin/finance/charge'}
            columns={columns}
        />
    </Content>;
}
