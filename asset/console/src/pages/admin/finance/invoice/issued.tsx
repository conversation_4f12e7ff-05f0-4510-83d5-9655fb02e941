import InvoiceOrderModal from '@/components/invoice-order-modal';
import UserModal from '@/components/user-modal';
import { NumberFormat, Table } from '@topthink/common';

export function Component() {
    return <Table
        source={'/admin/finance/invoice'}
        columns={[
            {
                title: '申请时间',
                dataIndex: 'create_time',
                width: 160,
            },
            {
                title: '申请人',
                align: 'center',
                dataIndex: 'user',
                width: 150,
                render({ value }) {
                    return <UserModal id={value.id} text={value.name} />;
                }
            },
            {
                title: '发票类型',
                dataIndex: 'type',
                width: 120,
                render({ value }) {
                    return value === 1 ? '增值税普通发票' : '增值税专用发票';
                }
            },
            {
                title: '发票抬头',
                dataIndex: 'title',
                width: 220,
            },
            {
                title: '纳税人识别号',
                dataIndex: 'taxpayer_number',
            },
            {
                title: '发票总额',
                width: 120,
                dataIndex: 'amount',
                render({ value }) {
                    return <NumberFormat value={value / 100} />;
                }
            },
            {
                title: '关联交易',
                width: 100,
                key: 'order',
                align: 'center',
                render({ record }) {
                    return <InvoiceOrderModal source={`/admin/finance/invoice/${record.id}/order`} />;
                }
            },
            {
                title: '操作',
                key: 'option',
                width: 150,
                align: 'right',
                render({ record }) {
                    return <a className='btn btn-link' href={`${record.remark}`} rel='noopener noreferrer'
                        target='_blank'>下载</a>;
                }
            }
        ]}
    />;
}
