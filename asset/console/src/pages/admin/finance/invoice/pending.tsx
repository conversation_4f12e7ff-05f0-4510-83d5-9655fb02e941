import UserModal from '@/components/user-modal';
import { Columns, ModalForm, NumberFormat, RequestButton, Space, Table } from '@topthink/common';


export function Component() {

    const columns: Columns<Charge> = [
        {
            title: '支付单号',
            dataIndex: 'trade_no',
            width: 140
        },
        {
            title: '用户',
            dataIndex: 'user',
            align: 'center',
            render({ value }) {
                return value ? <UserModal id={value.id} text={value.name} /> : '游客';
            }
        },
        {
            title: '名称',
            dataIndex: 'full_subject',
        },
        {
            title: '金额',
            dataIndex: 'net_amount',
            align: 'center',
            render({ value }) {
                return <NumberFormat value={value / 100} />;
            }
        },
        {
            title: '创建时间',
            dataIndex: 'create_time',
            width: 150
        },
        {
            title: '操作',
            align: 'right',
            key: 'confirm',
            width: 100,
            render({ record, action }) {

                return <Space>
                    <ModalForm
                        method='post'
                        action={`/admin/finance/invoice`}
                        text='开票'
                        transformData={(data) => {
                            return {
                                ...data,
                                order: record.id
                            };
                        }}
                        schema={{
                            type: 'object',
                            required: ['title', 'taxpayer_number', 'file'],
                            properties: {
                                type: {
                                    type: 'number',
                                    title: '发票类型',
                                    enum: [1, 2],
                                    default: 1
                                },
                                title: {
                                    type: 'string',
                                    title: '发票抬头'
                                },
                                taxpayer_number: {
                                    type: 'string',
                                    title: '纳税人识别号'
                                },
                                file: {
                                    title: '电子发票',
                                    type: 'string'
                                }
                            }
                        }}
                        uiSchema={{
                            type: {
                                'ui:widget': 'radio',
                                'ui:options': {
                                    enumNames: ['增值税普通发票', '增值税专用发票'],
                                },
                            },
                            file: {
                                'ui:widget': 'upload',
                                'ui:options': {
                                    endpoint: '/upload/invoice',
                                    accept: '.pdf,application/pdf'
                                }
                            }
                        }}
                        onSuccess={action.reload}
                    />
                    <RequestButton
                        confirm='确定标记为已开票吗？'
                        method='post'
                        url={`/admin/finance/charge/${record.id}/invoiced`}
                        onSuccess={action.reload}
                    >已开</RequestButton>
                </Space>;
            }
        }
    ];

    return <Table
        source={'/admin/finance/charge/invoicing'}
        columns={columns}
    />;
}
