import { Columns, Content, FormProps, Link, ModalForm, RequestButton, Space, Table } from '@topthink/common';

export function Component() {

    const schema: FormProps['schema'] = {
        type: 'object',
        required: [
            'name',
        ],
        properties: {
            name: {
                title: '名称',
                type: 'string'
            },
            redirect_uri: {
                title: 'Redirect URI',
                type: 'string',
                description: '每个URI占一行',
            },
            logout_uri: {
                title: 'Logout URI',
                type: 'string',
                description: '登出回调地址',
            },
            is_confidential: {
                title: '私密',
                type: 'boolean',
                default: false,
                description: '应用程序可用于客户端密钥可以保持保密的地方。原生移动应用和单页应用被视为为非保密。'
            },
            scopes: {
                title: '范围',
                type: 'array',
                items: {
                    type: 'string',
                    enum: [
                        'coin',
                        'money',
                        'charge',
                        'verify',
                        'notification',
                        'write',
                        'ai',
                        'internal'
                    ]
                },
                uniqueItems: true
            },
            product: {
                title: '关联产品',
                type: 'string',
            }
        }
    };

    const uiSchema = {
        redirect_uri: {
            'ui:widget': 'textarea'
        },
        scopes: {
            'ui:widget': 'checkboxes',
            'ui:enumNames': [
                '云币',
                '余额',
                '支付',
                '认证',
                '通知',
                '云写作',
                'AI',
                '内部接口'
            ],
            'ui:enumDescriptions': [
                '授予云币的相关操作权限，包括增加、扣除',
                '授予余额的相关操作权限，包括增加、扣除',
                '授予支付的相关权限，包括创建、查询支付单',
                '授予认证的相关权限，包括实名认证、企业认证',
                '授予通知的相关权限',
                '授予云写作的相关权限，包括查询私有化信息',
                '授予AI的相关权限',
                '授予一些内部迁移数据用的接口',
            ]
        },
        product: {
            'ui:widget': 'typeahead',
            'ui:options': {
                'minLength': 0,
                'endpoint': '/admin/sales/promo/products',
            }
        }
    };

    const columns: Columns<Application> = [
        {
            title: '名称',
            dataIndex: 'name',
            render({ record, value }) {
                return <Link to={`/admin/application/${record.id}`}>{value}</Link>;
            }
        },
        {
            title: '回调URL',
            dataIndex: 'redirect_uri',
            render({ value }) {
                return <pre className='mb-0'>{value}</pre>;
            }
        },
        {
            title: '操作',
            align: 'right',
            key: 'action',
            render({ record, action }) {
                return <Space>
                    <ModalForm
                        modalProps={{ size: 'lg', header: '编辑应用' }}
                        action={`/admin/application/${record.id}`}
                        method={'put'}
                        onSuccess={action.reload}
                        schema={schema}
                        formData={record}
                        uiSchema={uiSchema}
                        text={'编辑'}
                    />
                    <RequestButton
                        url={`/admin/application/${record.id}`}
                        method={'delete'}
                        confirm='确定要删除吗？'
                        onSuccess={action.reload}
                    >删除</RequestButton>
                </Space>;
            }
        }
    ];


    return <Content title={'应用'}>
        <Table
            toolBarRender={({ reload }) => {
                return <ModalForm
                    modalProps={{ size: 'lg' }}
                    action={'/admin/application'}
                    onSuccess={reload}
                    schema={schema}
                    uiSchema={uiSchema}
                    text={'创建应用'}
                />;
            }}
            source={'/admin/application'}
            columns={columns}
        />
    </Content>;
}
