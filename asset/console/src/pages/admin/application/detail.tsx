import { Card, Content, Loader, useParams, useRequest } from '@topthink/common';

export function Component() {

    const { id } = useParams();

    const { result } = useRequest<Application>(`/admin/application/${id}`);

    if (!result) {
        return <Loader />;
    }

    return <Content title='应用'>
        <Card title={result.name}>
            <dl className='row'>
                <dd className='col-2'>Client Id</dd>
                <dd className='col-10'>{result.client_id}</dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>Client Secret</dd>
                <dd className='col-10'>{result.client_secret}</dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>回调URL</dd>
                <dd className='col-10'>
                    <pre className='mb-0'>{result.redirect_uri || '--'}</pre>
                </dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>Logout URI</dd>
                <dd className='col-10'>
                    {result.logout_uri || '--'}
                </dd>
            </dl>
            <dl className='row'>
                <dd className='col-2'>私密的</dd>
                <dd className='col-10'>
                    {result.is_confidential ? '是' : '否'}
                </dd>
            </dl>
        </Card>
    </Content>;
}
