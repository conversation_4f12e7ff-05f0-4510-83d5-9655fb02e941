import { ReactNode } from 'react';
import InfoModal from '@/components/info-modal';
import { NumberFormat, Table } from '@topthink/common';

interface Props {
    source: string;
    children: ReactNode;
}

export default function MoneyLogsModal({ children, source }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            return <Table
                toolBarRender={false}
                card={false}
                source={source}
                columns={[
                    {
                        title: '日期',
                        align: 'left',
                        dataIndex: 'create_time',
                        width: 200
                    },
                    {
                        title: '说明',
                        align: 'left',
                        dataIndex: 'info',
                    },
                    {
                        title: '金额',
                        align: 'right',
                        dataIndex: 'number',
                        render({ value, record }) {
                            const className = record.type === 1 ? 'text-success' : 'text-danger';

                            return <span className={className}>
                                {record.type === 1 ? '+' : '-'}
                                <NumberFormat currency={false} value={value / 100} />
                            </span>;
                        }
                    }
                ]}
            />;
        }}
        text={children}
        header={'余额记录'}
    />;
}
