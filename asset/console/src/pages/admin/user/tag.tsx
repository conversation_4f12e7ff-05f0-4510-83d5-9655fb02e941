import { Content, ModalForm, RequestButton, Space, Table } from "@topthink/common";

export function Component() {
    return <Content>
        <Table
            toolBarRender={(table) => {
                return <ModalForm
                    text='添加标签'
                    action="/admin/user/tag"
                    method="post"
                    schema={{
                        type: 'object',
                        properties: {
                            name: {
                                title: '名称',
                                type: 'string'
                            }
                        }
                    }}
                    onSuccess={table.reload}
                />;
            }}
            source={'/admin/user/tag'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 100
                },
                {
                    title: '名称',
                    dataIndex: 'name'
                },
                {
                    title: '操作',
                    width: 100,
                    align: 'right',
                    key: 'action',
                    render({ record, action }) {
                        return <Space>
                            <ModalForm
                                text='编辑'
                                action={`/admin/user/tag/${record.id}`}
                                method="put"
                                formData={{
                                    name: record.name
                                }}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        name: {
                                            title: '名称',
                                            type: 'string'
                                        }
                                    }
                                }}
                                onSuccess={action.reload}
                            />
                            <RequestButton url={`/admin/user/tag/${record.id}`} method="delete" onSuccess={action.reload} confirm="确定删除吗？">删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}