import { Content, ModalForm, NumberFormat, request, RequestButton, Space, Table } from '@topthink/common';
import CertificationModal from '@/components/certification-modal';
import EnterpriseModal from '@/components/enterprise-modal';
import { Dropdown } from 'react-bootstrap';
import CoinLogsModal from '@/pages/admin/user/coin-logs-modal';
import MoneyLogsModal from '@/pages/admin/user/money-logs-modal';
import { ASSISTANT_FEATURE } from '@/utils/constants';
import UserModal from '@/components/user-modal';

export function Component() {
    return <Content title={'用户列表'}>
        <Table
            search={{
                fields: ['id', 'name', 'email', 'mobile'],
                extraFields: {
                    tag: '标签'
                },
                ui: {
                    tag: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            async: false,
                            async onSearch(params: any) {
                                const result = await request<UserTag[]>({
                                    url: '/admin/user/tag',
                                    params
                                });

                                return result.map((tag) => {
                                    return { value: tag.id, label: tag.name };
                                });
                            },
                            minLength: 0
                        }
                    }
                }
            }}
            sync
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '头像',
                    dataIndex: 'avatar',
                    width: 45,
                    render({ value }) {
                        return <img className='rounded-circle' width={30} height={30} src={value} />;
                    }
                },
                {
                    title: '昵称',
                    dataIndex: 'name',
                    render({ record }) {
                        return <UserModal id={record.id} text={record.name} />;
                    }
                },
                {
                    title: '手机',
                    dataIndex: 'mobile'
                },
                {
                    title: '邮箱',
                    dataIndex: 'email'
                },
                {
                    title: '余额',
                    dataIndex: 'money',
                    align: 'center',
                    render({ value, record, action }) {
                        return <Space size={3}>
                            <MoneyLogsModal source={`/admin/user/${record.id}/money/logs`}>
                                <NumberFormat value={value / 100} />
                            </MoneyLogsModal>
                            <RequestButton
                                tooltip={'同步'}
                                method={'post'}
                                url={`/admin/user/${record.id}/money/sync`}
                                onSuccess={() => action.reload()}
                            ><i className={'bi bi-arrow-clockwise'} /></RequestButton>
                        </Space>;
                    }
                },
                {
                    title: '云币',
                    dataIndex: 'coin',
                    align: 'center',
                    render({ value, record, action }) {
                        return <Space size={3}>
                            <CoinLogsModal source={`/admin/user/${record.id}/coin/logs`}>
                                <NumberFormat value={value / 100} />
                            </CoinLogsModal>
                            <RequestButton
                                tooltip={'同步'}
                                method={'post'}
                                url={`/admin/user/${record.id}/coin/sync`}
                                onSuccess={() => action.reload()}
                            ><i className={'bi bi-arrow-clockwise'} /></RequestButton>
                        </Space>;
                    }
                },
                {
                    title: '注册时间',
                    dataIndex: 'create_time',
                    width: 150
                },
                {
                    title: '实名认证',
                    dataIndex: 'is_certified',
                    align: 'center',
                    render({ value, record, action }) {
                        return value ? <CertificationModal
                            text={'查看'}
                            id={record.id}
                            footer={({ action: modalAction }) => {
                                return <RequestButton
                                    className={'me-auto'}
                                    variant={'danger'}
                                    method={'DELETE'}
                                    url={`/admin/user/${record.id}/certification`}
                                    confirm={'确定要删除吗?'}
                                    onSuccess={() => {
                                        modalAction.close();
                                        action.reload();
                                    }}
                                >删除认证</RequestButton>;
                            }}
                        /> : '否';
                    }
                },
                {
                    title: '企业认证',
                    dataIndex: 'is_enterprise',
                    align: 'center',
                    render({ value, record, action }) {
                        return value ? <EnterpriseModal
                            text={'查看'}
                            id={record.id}
                            footer={({ action: modalAction }) => {
                                return <RequestButton
                                    className={'me-auto'}
                                    variant={'danger'}
                                    method={'DELETE'}
                                    url={`/admin/user/${record.id}/enterprise`}
                                    confirm={'确定要删除吗?'}
                                    onSuccess={() => {
                                        modalAction.close();
                                        action.reload();
                                    }}
                                >删除认证</RequestButton>;
                            }}
                        /> : '否';
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ action, record }) {
                        return <Space>
                            <Dropdown align={'end'}>
                                <Dropdown.Toggle
                                    className={'border-0 no-caret'}
                                    variant={'outline-secondary'}
                                ><i className='bi bi-three-dots-vertical' /></Dropdown.Toggle>
                                <Dropdown.Menu className={'shadow'} renderOnMount popperConfig={{ strategy: 'fixed' }}>
                                    <ModalForm
                                        text={'云币设置'}
                                        buttonProps={{ as: Dropdown.Item }}
                                        action={`/admin/user/${record.id}/coin`}
                                        method={'post'}
                                        schema={{
                                            type: 'object',
                                            properties: {
                                                type: {
                                                    title: '类型',
                                                    type: 'number',
                                                    enum: [1, 2],
                                                },
                                                number: {
                                                    title: '数量',
                                                    type: 'number',
                                                    minimum: 0
                                                },
                                                info: {
                                                    title: '操作原因',
                                                    type: 'string'
                                                }
                                            }
                                        }}
                                        uiSchema={{
                                            type: {
                                                'ui:widget': 'radio',
                                                'ui:options': {
                                                    enumNames: ['增加', '减少'],
                                                    label: false
                                                }
                                            },
                                            number: {
                                                'ui:options': {
                                                    placeholder: '数量',
                                                    label: false
                                                }
                                            },
                                            info: {
                                                'ui:widget': 'textarea',
                                                'ui:options': {
                                                    placeholder: '操作原因',
                                                    label: false
                                                }
                                            }
                                        }}
                                        onSuccess={action.reload}
                                    />
                                    <ModalForm
                                        text={'余额设置'}
                                        buttonProps={{ as: Dropdown.Item }}
                                        action={`/admin/user/${record.id}/money`}
                                        method={'post'}
                                        schema={{
                                            type: 'object',
                                            properties: {
                                                type: {
                                                    title: '类型',
                                                    type: 'number',
                                                    enum: [1, 2],
                                                },
                                                number: {
                                                    title: '金额',
                                                    type: 'number',
                                                    minimum: 0
                                                },
                                                info: {
                                                    title: '操作原因',
                                                    type: 'string'
                                                }
                                            }
                                        }}
                                        uiSchema={{
                                            type: {
                                                'ui:widget': 'radio',
                                                'ui:options': {
                                                    enumNames: ['增加', '减少'],
                                                    label: false
                                                }
                                            },
                                            number: {
                                                'ui:options': {
                                                    placeholder: '金额',
                                                    label: false
                                                }
                                            },
                                            info: {
                                                'ui:widget': 'textarea',
                                                'ui:options': {
                                                    placeholder: '操作原因',
                                                    label: false
                                                }
                                            }
                                        }}
                                        onSuccess={action.reload}
                                    />
                                    <ModalForm
                                        text={'功能设置'}
                                        buttonProps={{ as: Dropdown.Item }}
                                        action={`/admin/user/${record.id}/features`}
                                        method={'post'}
                                        schema={{
                                            type: 'object',
                                            properties: {
                                                assistant: {
                                                    title: '网站助理',
                                                    type: 'boolean',
                                                }
                                            }
                                        }}
                                        formData={{
                                            assistant: (record.features & ASSISTANT_FEATURE) !== 0
                                        }}
                                        onSuccess={action.reload}
                                    />
                                </Dropdown.Menu>
                            </Dropdown>
                        </Space>;
                    }
                }
            ]}
            source={'/admin/user'}
        />
    </Content>;
}
