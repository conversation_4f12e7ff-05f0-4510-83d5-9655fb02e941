import InfoModal from '@/components/info-modal';
import { NumberFormat, Table } from '@topthink/common';
import { ReactNode } from 'react';

interface Props {
    source: string;
    children: ReactNode;
}

export default function CoinLogsModal({ source, children }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            return <Table
                toolBarRender={false}
                card={false}
                source={source}
                columns={[
                    {
                        title: '日期',
                        align: 'left',
                        dataIndex: 'create_time',
                        width: 200
                    },
                    {
                        title: '说明',
                        align: 'left',
                        dataIndex: 'info',
                    },
                    {
                        title: '数量',
                        align: 'right',
                        dataIndex: 'number',
                        render({ value, record }) {
                            const className = record.type === 1 ? 'text-success' : 'text-danger';

                            return <span className={className}>
                                {record.type === 1 ? '+' : '-'}
                                <NumberFormat currency={false} value={value / 100} />
                            </span>;
                        }
                    }
                ]}
            />;
        }}
        text={children}
        header={'云币记录'}
    />;
}
