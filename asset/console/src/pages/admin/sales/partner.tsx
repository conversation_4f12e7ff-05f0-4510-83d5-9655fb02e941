import { Content, ModalForm, NumberFormat, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';
import Licenses from './components/licenses';
import AiLicenses from './components/ai-licenses';

export const Component = () => {
    return <Content>
        <Table
            source={`/admin/sales/partner`}
            columns={[
                {
                    title: '用户',
                    dataIndex: 'user',
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '应用代理',
                    dataIndex: 'license',
                    align: 'center',
                    width: 100,
                    render({ value, record, action }) {
                        return <ModalForm
                            action={`/admin/sales/partner/${record.id}/license`}
                            method={'post'}
                            modalProps={{
                                header: '设置代理价'
                            }}
                            text={value ? `${value}%` : '未开通'}
                            formData={{
                                license: value
                            }}
                            schema={{
                                type: 'object',
                                properties: {
                                    license: {
                                        type: 'number',
                                        title: '代理价',
                                        minimum: 0,
                                        maximum: 100,
                                        description: '0-100之间的整数, 0表示关闭代理'
                                    }
                                }
                            }}
                            uiSchema={{
                                license: {
                                    'ui:options': {
                                        suffix: '%'
                                    }
                                }
                            }}
                            onSuccess={() => {
                                action.reload(true);
                            }}
                        />;
                    }
                },
                {
                    title: 'ThinkWiki',
                    dataIndex: 'wikis_count',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        return <Licenses
                            title={'ThinkWiki'}
                            source={`/admin/sales/partner/${record.id}/wikis`}
                            text={value}
                        />;
                    }
                },
                {
                    title: 'ThinkBot',
                    dataIndex: 'bots_count',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        return <Licenses
                            title={'ThinkBot'}
                            source={`/admin/sales/partner/${record.id}/bots`}
                            text={value}
                        />;
                    }
                },
                {
                    title: 'ThinkChat',
                    dataIndex: 'chats_count',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        return <Licenses
                            title={'ThinkChat'}
                            source={`/admin/sales/partner/${record.id}/chats`}
                            text={value}
                        />;
                    }
                },
                {
                    title: 'ThinkAI',
                    dataIndex: 'ais_count',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        return <AiLicenses
                            title={'ThinkAI'}
                            source={`/admin/sales/partner/${record.id}/ais`}
                            text={value}
                        />;
                    }
                },
                {
                    title: '未结算佣金',
                    dataIndex: 'commission',
                    width: 120,
                    align: 'center',
                    render({ value }) {
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    dataIndex: 'create_time',
                    width: 160,
                    title: '加入时间'
                }
            ]}
            search={{
                fields: ['user', 'license'],
                ui: {
                    user: {
                        'ui:widget': 'typeahead',
                        'ui:options': {
                            endpoint: '/admin/user/search',
                        }
                    },
                    license: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    label: '是',
                                    value: 1
                                },
                                {
                                    label: '否',
                                    value: -1
                                }
                            ]
                        }
                    },
                }
            }}
        />
    </Content>;
};
