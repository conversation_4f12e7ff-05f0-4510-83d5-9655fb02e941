import {
    Content,
    FormSchema,
    FormUiSchema,
    Loader,
    ModalForm,
    RequestButton,
    Space,
    Table,
    Toast,
    useClipboard,
    useRequest
} from '@topthink/common';
import UserModal from '@/components/user-modal';
import Charges from './components/charges';

export const Component = () => {

    const { copy } = useClipboard();
    const { result: products } = useRequest<{ label: string, value: string }[]>('/admin/sales/promo/products');
    if (!products) {
        return <Loader />;
    }
    const schema: FormSchema = {
        type: 'object',
        properties: {
            title: {
                type: 'string',
                title: '名称',
            },
            code: {
                type: 'string',
                title: '代码'
            },
            product: {
                type: 'array',
                title: '适用产品',
                items: {
                    type: 'string',
                },
            },
            discount: {
                type: 'number',
                title: '折扣'
            },
            partner_id: {
                type: 'string',
                title: '合作伙伴',
                description: '留空则为平台优惠码'
            },
            expire_time: {
                type: 'string',
                title: '有效期'
            }
        },
        dependencies: {
            partner_id: {
                properties: {
                    rebate: {
                        type: 'number',
                        title: '佣金比例'
                    }
                }
            }
        }
    };

    const uiSchema: FormUiSchema = {
        'ui:order': ['*', 'expire_time'],
        product: {
            'ui:widget': 'typeahead',
            'ui:options': {
                filter: true,
                enumOptions: products
            }
        },
        discount: {
            'ui:options': {
                suffix: '%'
            }
        },
        partner_id: {
            'ui:widget': 'typeahead',
            'ui:options': {
                endpoint: '/admin/sales/partner/search',
                minLength: 0
            }
        },
        rebate: {
            'ui:options': {
                suffix: '%'
            }
        },
        expire_time: {
            'ui:widget': 'date',
            'ui:placeholder': '留空则不限'
        }
    };

    return <Content>
        <Table
            toolBarRender={(action) => {
                return <ModalForm
                    text={'添加优惠码'}
                    action={'/admin/sales/promo'}
                    omitExtraData
                    schema={schema}
                    uiSchema={uiSchema}
                    onSuccess={() => {
                        action.reload();
                    }}
                />;
            }}
            source={`/admin/sales/promo`}
            columns={[
                {
                    title: '名称',
                    dataIndex: 'title',
                },
                {
                    title: '代码',
                    dataIndex: 'code',
                    render: ({ value }) => {
                        return <Space>
                            <span className={'bg-secondary text-light px-2 rounded-2'}>{value}</span>
                            <a onClick={() => {
                                copy(value);
                                Toast.success('已复制');
                            }} className={'fs-7'} role={'button'}><i className={'bi bi-clipboard'} /></a>
                        </Space>;
                    }
                },
                {
                    title: '折扣',
                    dataIndex: 'discount',
                    render: ({ value }) => {
                        return <>{value}%</>;
                    }
                },
                {
                    title: '适用产品',
                    dataIndex: 'product',
                    width: 300,
                    render: ({ value }) => {
                        return (value as string[]).map((item) => {
                            return products.find((product) => product.value === item)?.label;
                        }).join(', ');
                    }
                },
                {
                    title: '关联交易',
                    dataIndex: 'charges_count',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        return <Charges
                            source={`/admin/sales/promo/${record.id}/charges`}
                            text={value}
                        />;
                    }
                },
                {
                    title: '合作伙伴',
                    dataIndex: 'partner',
                    render: ({ value, record }) => {
                        return value ?
                            <Space size={5}>
                                <UserModal user={value.user} text={`${value.user.name}`} />
                                <div>({record.rebate}%)</div>
                            </Space> : '--';
                    }
                },
                {
                    dataIndex: 'expire_time',
                    width: 120,
                    title: '到期时间',
                    empty: '永久有效',
                    valueType: 'date'
                },
                {
                    title: '操作',
                    width: 160,
                    align: 'right',
                    render: ({ record, action }) => {
                        return <Space>
                            <ModalForm
                                method={'put'}
                                action={`/admin/sales/promo/${record.id}`}
                                text={'编辑'}
                                omitExtraData
                                schema={schema}
                                uiSchema={uiSchema}
                                formData={{
                                    ...record,
                                    partner_id: record.partner?.id
                                }}
                                onSuccess={() => action.reload()}
                            />
                            <RequestButton
                                url={`/admin/sales/promo/${record.id}`}
                                method={'delete'}
                                confirm={'确认删除吗？'}
                                onSuccess={() => action.reload()}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
