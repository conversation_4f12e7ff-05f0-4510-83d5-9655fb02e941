import InfoModal from '@/components/info-modal';
import { dayjs, Table } from '@topthink/common';

interface Props {
    source: string;
    text: string;
    title: string;
}

export default function AiLicenses({ source, text, title }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            return <Table
                toolBarRender={false}
                card={false}
                source={source}
                columns={[
                    {
                        title: '站点名称',
                        dataIndex: 'name',
                    },
                    {
                        title: '版本',
                        dataIndex: 'plan',
                        width: 100,
                        render({ value }) {
                            return <div className={value.value == 'trial' ? 'text-orange' : 'text-success'}>{value.label}</div>;
                        }
                    },
                    {
                        title: '有效日期',
                        dataIndex: 'expire_time',
                        width: 150,
                        render({ value }) {
                            if (value) {
                                if (dayjs(value).isBefore(dayjs())) {
                                    return <div className='text-danger'>已过期</div>;
                                }
                                return value;
                            }
                            return '--';
                        }
                    },
                    {
                        title: '创建日期',
                        dataIndex: 'create_time',
                        width: 150,
                    },
                ]}
            />;
        }}
        text={text}
        header={title}
    />;
}
