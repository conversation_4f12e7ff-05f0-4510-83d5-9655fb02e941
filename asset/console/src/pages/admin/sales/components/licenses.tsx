import InfoModal from '@/components/info-modal';
import { dayjs, formatLongNumber, Table } from '@topthink/common';

interface Props {
    source: string;
    text: string;
    title: string;
}

export default function Licenses({ source, text, title }: Props) {
    return <InfoModal
        size={'lg'}
        renderChildren={() => {
            return <Table
                toolBarRender={false}
                card={false}
                source={source}
                columns={[
                    {
                        title: '站点名称',
                        dataIndex: 'name',
                        render({ record }) {
                            return <a className={'link-primary'} target={'_blank'} href={record.url}>{record.name}</a>;
                        }
                    },
                    {
                        title: '版本',
                        dataIndex: 'plan',
                        width: 100,
                        render({ value }) {
                            return <div className={value.value == 'trial' ? 'text-orange' : 'text-success'}>{value.label}</div>;
                        }
                    },
                    {
                        title: 'Token 余额',
                        dataIndex: 'token',
                        width: 150,
                        align: 'center',
                        render({ value }) {
                            return formatLongNumber(value);
                        }
                    },
                    {
                        title: '有效日期',
                        dataIndex: 'expire_time',
                        width: 150,
                        render({ value }) {
                            if (value) {
                                if (dayjs(value).isBefore(dayjs())) {
                                    return <div className='text-danger'>已过期</div>;
                                }
                                return value;
                            }
                            return '--';
                        }
                    },
                    {
                        title: '创建日期',
                        dataIndex: 'create_time',
                        width: 150,
                    },
                ]}
            />;
        }}
        text={text}
        header={title}
    />;
}
