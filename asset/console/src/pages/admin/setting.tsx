import { Card, Content, Form, Loader, useRequest } from '@topthink/common';

export function Component() {

    const { result } = useRequest('/admin/setting');

    if (!result) {
        return <Loader />;
    }

    return <Content title={'设置'}>
        <Card>
            <Form
                action={'/admin/setting'}
                submitText={'保存'}
                formData={result}
                schema={{
                    type: 'object',
                    properties: {
                        scripts: {
                            type: 'string',
                            title: '自定义脚本'
                        }
                    }
                }}
                uiSchema={{
                    scripts: {
                        'ui:widget': 'textarea',
                        'ui:options': {
                            rows: 5
                        }
                    }
                }}
            />
        </Card>
    </Content>;
}
