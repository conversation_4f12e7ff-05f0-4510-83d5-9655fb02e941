import { Card, LinkButton } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';

interface ItemProps {
    title: string;
    link: string;
    value: number;
}

const Item = ({ title, link, value }: ItemProps) => {
    return <Col xs={6} className={'d-grid'}>
        <LinkButton to={link} variant={'light'} className={'d-flex justify-content-between'}>
            <span>{title}</span>
            <span className={value > 0 ? 'text-primary' : 'text-secondary'}>{value}</span>
        </LinkButton>
    </Col>;
};

export default function Approval({ approval }: { approval: BasicStatistics['approval'] }) {
    return <Card title={'审批'}>
        <Row className={'g-3'}>
            <Item title='企业认证' link='/admin/approval/enterprise' value={approval.enterprise} />
            <Item title='发票申请' link='/admin/approval/invoice' value={approval.invoice} />
            <Item title='余额提现' link='/admin/approval/withdraw/money' value={approval.withdraw.money} />
            <Item title='云币提现' link='/admin/approval/withdraw/coin' value={approval.withdraw.coin} />
            <Item title='短信签名' link='/admin/approval/sms/sign' value={approval.sms.sign} />
        </Row>
    </Card>;
}
