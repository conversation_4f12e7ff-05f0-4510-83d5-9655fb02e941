import PeriodButtons from '@/components/period-buttons';
import Statistic from '@/components/statistic';
import { Card, Content, LinkButton, Loader, NumberFormat, Space, styled, useRequest } from '@topthink/common';
import { Suspense, useState } from 'react';
import { Col, Nav, Row } from 'react-bootstrap';
import Approval from './approval';
import ChargeChart from './charge-chart';
import OrderChart from './order-chart';
import UserChart from './user-chart';

export function Component() {

    const [period, setPeriod] = useState<string>('30days');
    const [tab, setTab] = useState('charge');
    const { result } = useRequest<BasicStatistics>('/admin/statistic/basic');

    if (!result) {
        return <Loader />;
    }

    const charts = {
        charge: <ChargeChart period={period} />,
        user: <UserChart period={period} />,
        order: <OrderChart period={period} />,
    };

    return <Content title={'概览'}>
        <Row className={'g-3'}>
            <Col md={9}>
                <Row className={'g-3 mb-3'}>
                    <Col md={4}>
                        <Statistic
                            title={'收入'}
                            content={
                                <NumberFormat value={result.charge.amount / 100} />
                            }
                            footer={
                                <>交易笔数 <NumberFormat value={result.charge.total} currency={false} options={{ minimumFractionDigits: 0 }} /></>
                            }
                        />
                    </Col>
                    <Col md={4}>
                        <Statistic
                            title={'用户'}
                            content={
                                <NumberFormat value={result.user.total} currency={false} options={{ minimumFractionDigits: 0 }} />
                            }
                            footer={
                                <Space>
                                    <span>企业数 <NumberFormat value={result.user.enterprise} currency={false} options={{ minimumFractionDigits: 0 }} /></span>
                                    <span>昨日新增 {result.user.yesterday}</span>
                                </Space>
                            }
                        />
                    </Col>
                    <Col md={4}>
                        <Statistic
                            title={'自营'}
                            content={
                                <NumberFormat value={result.order.amount / 100} />
                            }
                            footer={
                                <>订单数 <NumberFormat value={result.order.total} currency={false} options={{ minimumFractionDigits: 0 }} /></>
                            }
                        />
                    </Col>
                </Row>
                <Card>
                    <Nav variant='tabs' defaultActiveKey={tab} onSelect={key => key && setTab(key)}>
                        <Nav.Item>
                            <Nav.Link eventKey='charge'>收入</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey='user'>用户</Nav.Link>
                        </Nav.Item>
                        <Nav.Item>
                            <Nav.Link eventKey='order'>自营</Nav.Link>
                        </Nav.Item>
                        <StyledPeriodButtons periods={['30days', 'last-month', '6months', '1year']} period={period} onChange={setPeriod} />
                    </Nav>
                    <Container>
                        <Suspense fallback={<Loader />}>
                            {charts[tab as keyof typeof charts]}
                        </Suspense>
                    </Container>
                </Card>
            </Col>
            <Col>
                <Card title={'快捷链接'}>
                    <Row className={'g-3'}>
                        <Col xs={6} className={'d-grid'}>
                            <LinkButton to={'/admin/product/api'} variant={'light'}>API服务</LinkButton>
                        </Col>
                        <Col xs={6} className={'d-grid'}>
                            <LinkButton to={'/admin/product/sms'} variant={'light'}>短信服务</LinkButton>
                        </Col>
                    </Row>
                </Card>
                <Approval approval={result.approval} />
            </Col>
        </Row>
    </Content>;
}

const Container = styled.div`
    padding: 2rem .25rem 0;
    position: relative;
`;

const StyledPeriodButtons = styled(PeriodButtons)`
    position: absolute;
    right: 1rem;

    .btn {
        line-height: 1.2;
    }
`;
