import { Loader, Space, useRequest } from '@topthink/common';
import { Column, ColumnConfig, Pie, PieConfig } from '@ant-design/plots';
import formatMoney from '@/utils/format-money';
import { Col, Row } from 'react-bootstrap';
import { DataItem } from './types';
import { formatPeriodDate } from '@/utils/format';

type ChargeData = {
    application_name: string
    value?: number
    items?: DataItem[];
    preview?: number
}[]

interface Props {
    period: string;
}

export default function ChargeChart({ period }: Props) {
    const { result } = useRequest<ChargeData>({
        url: '/admin/statistic/charge',
        params: { period }
    }, {
        refreshDeps: [period]
    });

    if (!result) {
        return <Loader />;
    }

    const columnData = result.flatMap((app) => {
        return (app.items || []).map(item => {
            return {
                date: item.date,
                app: app.application_name,
                value: item.value,
            };
        });
    });

    const columnConfig: ColumnConfig = {
        data: columnData,
        height: 300,
        xField: 'date',
        yField: 'value',
        meta: {
            value: {
                formatter: (val: any) => formatMoney(val / 100),
            },
        },
        tooltip: {
            customContent: (title, data) => {
                const total = data.reduce((r, d) => {
                    return r + d.data.value;
                }, 0);

                return <>
                    <div className='g2-tooltip-title d-flex justify-content-between'>
                        <span>{title}</span>
                        <span>{formatMoney(total / 100)}</span>
                    </div>
                    <ul className='g2-tooltip-list'>
                        {data.map((item) => {
                            return <li className='g2-tooltip-list-item'>
                                <span className='g2-tooltip-marker' style={{ backgroundColor: item.color }}></span>
                                <span className='g2-tooltip-name'>{item.name}</span>:
                                <span className='g2-tooltip-value'>{item.value}</span>
                            </li>;
                        })}
                    </ul>
                </>;
            },
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                },
            },
        },
        isStack: true,
        seriesField: 'app',
        legend: {
            position: 'bottom',
        },
    };

    const pieConfig: PieConfig = {
        data: result.map((v) => ({
            value: Number(v.value || 0),
            preview: Number(v.preview || 0),
            type: v.application_name
        })),
        height: 250,
        appendPadding: 10,
        angleField: 'value',
        colorField: 'type',
        radius: 1,
        innerRadius: 0.64,
        meta: {
            value: {
                formatter: (v) => `${v} ¥`,
            },
        },
        legend: false,
        label: {
            type: 'inner',
            offset: '-50%',
            style: {
                textAlign: 'center',
            },
            autoRotate: false,
            content: '{percentage}',
        },
        statistic: {
            title: {
                offsetY: -20,
                style: {
                    fontSize: '14px',
                },
                customHtml: (_, __, datum) => {
                    return datum ? datum.type : '总计';
                },
            },
            content: {
                offsetY: 0,
                style: {
                    fontSize: '14px',
                    fontWeight: '400'
                },
                // @ts-ignore
                customHtml: (_, __, datum, data) => {
                    const value = datum ? datum.value : (data || []).reduce((r, d) => r + d.value, 0);
                    const preview = datum ? datum.preview : (data || []).reduce((r, d) => r + d.preview, 0);
                    const arrow = value - preview > 0 ? ' ↑' : ' ↓';
                    return <Space direction={'vertical'}>
                        {formatMoney(value / 100)}
                        {preview > 0 &&
                            <div className={'fs-7 text-secondary'}>
                                同比：<span className={value - preview > 0 ? 'text-danger' : 'text-success'}>{((value - preview) / preview * 100).toFixed(1)}%{arrow}</span>
                            </div>}
                    </Space>;
                },
            },
        },
        // 添加 中心统计文本 交互
        interactions: [
            {
                type: 'element-selected',
            },
            {
                type: 'element-active',
            },
            {
                type: 'pie-statistic-active',
            },
        ],
    };

    return <Row className={'g-0'}>
        <Col md={9} xs={12}>
            <Column {...columnConfig} />
        </Col>
        <Col md={3} xs={12}>
            <Pie {...pieConfig} />
        </Col>
    </Row>;
}
