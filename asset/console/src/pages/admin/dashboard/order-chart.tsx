import { Loader, useRequest } from '@topthink/common';
import { Column, ColumnConfig, Pie, PieConfig } from '@ant-design/plots';
import formatMoney from '@/utils/format-money';
import { Col, Row } from 'react-bootstrap';
import { DataItem } from './types';
import { formatPeriodDate } from '@/utils/format';

type ChargeData = {
    goods_name: string
    value: number
    items: DataItem[];
}[]

interface Props {
    period: string;
}

export default function OrderChart({ period }: Props) {
    const { result } = useRequest<ChargeData>({
        url: '/admin/statistic/order',
        params: { period }
    }, {
        refreshDeps: [period]
    });

    if (!result) {
        return <Loader />;
    }

    const columnData = result.flatMap((app) => {
        return app.items.map(item => {
            return {
                date: item.date,
                goods: app.goods_name,
                value: item.value
            };
        });
    });

    const columnConfig: ColumnConfig = {
        data: columnData,
        height: 300,
        xField: 'date',
        yField: 'value',
        meta: {
            value: {
                formatter: (val: any) => formatMoney(val / 100),
            },
        },
        tooltip: {
            customContent: (title, data) => {
                const total = data.reduce((r, d) => {
                    return r + d.data.value;
                }, 0);

                return <>
                    <div className='g2-tooltip-title d-flex justify-content-between'>
                        <span>{title}</span>
                        <span>{formatMoney(total / 100)}</span>
                    </div>
                    <ul className='g2-tooltip-list'>
                        {data.map((item) => {
                            return <li className='g2-tooltip-list-item'>
                                <span className='g2-tooltip-marker' style={{ backgroundColor: item.color }}></span>
                                <span className='g2-tooltip-name'>{item.name}</span>:
                                <span className='g2-tooltip-value'>{item.value}</span>
                            </li>;
                        })}
                    </ul>
                </>;
            },
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                },
            },
        },
        isStack: true,
        seriesField: 'goods',
        legend: {
            position: 'bottom',
        },
    };

    const pieConfig: PieConfig = {
        data: result.map((v) => ({
            value: Number(v.value),
            type: v.goods_name
        })),
        height: 250,
        appendPadding: 10,
        angleField: 'value',
        colorField: 'type',
        radius: 1,
        innerRadius: 0.64,
        meta: {
            value: {
                formatter: (v) => `${v} ¥`,
            },
        },
        legend: false,
        label: {
            type: 'inner',
            offset: '-50%',
            style: {
                textAlign: 'center',
            },
            autoRotate: false,
            content: '{percentage}',
        },
        statistic: {
            title: {
                offsetY: -10,
                style: {
                    fontSize: '14px',
                },
                customHtml: (_, __, datum) => {
                    return datum ? datum.type : '总计';
                },
            },
            content: {
                offsetY: 6,
                style: {
                    fontSize: '14px',
                    fontWeight: '400'
                },
                customHtml: (_, __, datum, data) => {
                    const value = datum ? datum.value : (data || []).reduce((r, d) => r + d.value, 0);
                    return formatMoney(value / 100);
                },
            },
        },
        // 添加 中心统计文本 交互
        interactions: [
            {
                type: 'element-selected',
            },
            {
                type: 'element-active',
            },
            {
                type: 'pie-statistic-active',
            },
        ],
    };


    return <Row className={'g-0'}>
        <Col xs={12} md={9}>
            <Column {...columnConfig} />
        </Col>
        <Col xs={12} md={3}>
            <Pie {...pieConfig} />
        </Col>
    </Row>;
}
