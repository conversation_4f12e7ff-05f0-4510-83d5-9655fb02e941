import { DataItem } from './types';
import { Loader, useRequest } from '@topthink/common';
import { Line, LineConfig, RadialBar, RadialBarConfig } from '@ant-design/plots';
import { Col, Row } from 'react-bootstrap';
import { formatPeriodDate } from '@/utils/format';

interface UserData {
    new: DataItem[];
    app: {
        application_name: string
        value: number
    }[];
}

interface Props {
    period: string;
}

export default function UserChart({ period }: Props) {

    const { result } = useRequest<UserData>({
        url: '/admin/statistic/user',
        params: { period }
    }, {
        refreshDeps: [period]
    });

    if (!result) {
        return <Loader />;
    }

    const lineConfig: LineConfig = {
        data: result.new,
        height: 250,
        xField: 'date',
        yField: 'value',
        meta: {
            value: {
                alias: '新增用户'
            }
        },
        xAxis: {
            label: {
                formatter(text) {
                    return formatPeriodDate(period, text);
                }
            },
        },
        smooth: true,
    };

    const radiaBarConfig: RadialBarConfig = {
        data: result.app,
        height: 250,
        xField: 'application_name',
        yField: 'value',
        radius: 0.8,
        innerRadius: 0.2,
        barStyle: {
            lineCap: 'round',
        },
        colorField: 'application_name', // 部分图表使用 seriesField
        color: ['#5B8FF9', '#5AD8A6', '#5D7092', '#F6BD16', '#6F5EF9', '#6DC8EC', '#945FB9', '#FF9845', '#1E9493', '#FF99C3'],
        minBarWidth: 8,
        maxBarWidth: 12,
        tooltip: {
            formatter: (datum) => {
                return {
                    name: '新增用户',
                    value: datum.value,
                };
            },
        },
    };

    return <Row className={'g-0'}>
        <Col xs={12} md={9}>
            <Line {...lineConfig} />
        </Col>
        <Col xs={12} md={3}>
            <RadialBar {...radiaBarConfig} />
        </Col>
    </Row>;
}
