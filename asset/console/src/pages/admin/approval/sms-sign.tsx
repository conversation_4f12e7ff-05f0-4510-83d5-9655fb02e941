import UserModal from '@/components/user-modal';
import { Content, ModalForm, RequestButton, Space, styled, Table, Toast } from '@topthink/common';
import { Button, OverlayTrigger, Popover } from 'react-bootstrap';
import ImageZoom from '../../../components/image-zoom';
import getUploadsUrl from '../../../utils/get-uploads-url';

export function Component() {
    return <Content>
        <Table
            source={'/admin/approval/sms/sign'}
            columns={[
                {
                    title: '申请时间',
                    dataIndex: 'create_time',
                    width: 160,
                },
                {
                    title: '申请人',
                    align: 'center',
                    dataIndex: 'user',
                    width: 150,
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '签名名称',
                    dataIndex: 'name',
                },
                {
                    title: '签名来源',
                    dataIndex: 'source',
                },
                {
                    title: '企业信息',
                    dataIndex: 'company',
                    render({ value }) {
                        return value ?
                            <Space>
                                {value.img && <ImageZoom>
                                    <img src={getUploadsUrl(value.img)} height={30} />
                                </ImageZoom>}
                                <OverlayTrigger trigger='hover' placement={'top'} overlay={
                                    <StyledPopover id='popover-company'>
                                        <Popover.Body>
                                            <dl className='row'>
                                                <dd className='col-3 text-muted'>公司名称</dd>
                                                <dd className='col-9 fw-bold'>{value.name}</dd>
                                            </dl>
                                            <dl className='row'>
                                                <dd className='col-3 text-muted'>信用代码</dd>
                                                <dd className='col-9 fw-bold'>{value.identity}</dd>
                                            </dl>
                                            <dl className='row mb-0'>
                                                <dd className='col-3 text-muted'>企业法人</dd>
                                                <dd className='col-9 fw-bold'>{value.corporation}</dd>
                                            </dl>
                                        </Popover.Body>
                                    </StyledPopover>}>
                                    <Button variant={'link'}>{value.name}</Button>
                                </OverlayTrigger>
                            </Space> : '--';
                    }
                },
                {
                    title: '证明材料',
                    dataIndex: 'proof',
                    align: 'center',
                    render({ value }) {
                        return value ? <ImageZoom>
                            <img src={getUploadsUrl(value)} height={30} />
                        </ImageZoom> : '--';
                    }
                },
                {
                    title: '操作',
                    key: 'option',
                    width: 150,
                    align: 'right',
                    render({ action, record }) {
                        return <Space>
                            {record.status === 3 ? <Button
                                variant={'link'}
                                disabled
                                className={'text-dark'}
                            >已报备</Button> : <RequestButton
                                url={`/admin/approval/sms/sign/${record.id}/submit`}
                                method={'post'}
                                onSuccess={() => {
                                    Toast.success('已提交报备');
                                    action.reload();
                                }}
                                confirm='确定提交报备吗？'
                                variant={'link'}
                                className={'text-success'}
                            >报备</RequestButton>}
                            <RequestButton
                                url={`/admin/approval/sms/sign/${record.id}/pass`}
                                method={'post'}
                                onSuccess={action.reload}
                                confirm='确定通过吗？'
                                variant={'link'}
                            >通过</RequestButton>
                            <ModalForm
                                text={'驳回'}
                                action={`/admin/approval/sms/sign/${record.id}/reject`}
                                method={'post'}
                                onSuccess={action.reload}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        error: {
                                            type: 'string'
                                        }
                                    }
                                }}
                                uiSchema={{
                                    error: {
                                        'ui:widget': 'textarea',
                                        'ui:options': {
                                            label: false,
                                            placeholder: '驳回原因'
                                        }
                                    }
                                }}
                            />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}

const StyledPopover = styled(Popover)`
    max-width: 400px;
    width: 300px;
`;
