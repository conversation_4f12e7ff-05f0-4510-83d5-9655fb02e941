import { Columns, Content, RequestButton, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';
import ImageZoom from '@/components/image-zoom';

export function Component() {

    const columns: Columns = [
        {
            title: '申请时间',
            dataIndex: 'create_time',
            width: 160,
        },
        {
            title: '申请人',
            align: 'center',
            dataIndex: 'user',
            width: 150,
            render({ value }) {
                return <UserModal id={value.id} text={value.name} />;
            }
        },
        {
            title: '企业法人',
            dataIndex: 'corporation',
        },
        {
            title: '公司名称',
            width: 200,
            dataIndex: 'name',
        },
        {
            title: '统一社会信用代码',
            dataIndex: 'identity',
        },
        {
            title: '营业执照',
            dataIndex: 'img',
            render({ value }) {
                return <ImageZoom>
                    <img src={value} height={20} />
                </ImageZoom>;
            }
        },
        {
            title: '操作',
            key: 'option',
            width: 150,
            align: 'right',
            render({ action, record }) {
                return <Space>
                    <RequestButton
                        url={`/admin/approval/enterprise/${record.id}/pass`}
                        method={'post'}
                        onSuccess={action.reload}
                        confirm='确定通过吗？'
                        variant={'link'}
                    >通过</RequestButton>
                    <RequestButton
                        url={`/admin/approval/enterprise/${record.id}/reject`}
                        method={'post'}
                        onSuccess={action.reload}
                        confirm='确定驳回吗？'
                        variant={'link'}
                    >驳回</RequestButton>
                </Space>;
            }
        }
    ];

    return <Content title='企业认证'>
        <Table
            columns={columns}
            source={'/admin/approval/enterprise'}
        />
    </Content>;
}
