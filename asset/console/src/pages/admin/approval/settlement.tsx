import { Content, ModalForm, NumberFormat, RequestButton, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';

export const Component = () => {
    return <Content>
        <Table
            source={'/admin/approval/settlement'}
            columns={[
                {
                    title: '申请时间',
                    dataIndex: 'create_time',
                    width: 160,
                },
                {
                    title: '申请人',
                    align: 'center',
                    dataIndex: ['partner', 'user'],
                    width: 150,
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    render({ value }) {
                        return <NumberFormat className={'text-orange'} value={value / 100} />;
                    }
                },
                {
                    title: '发票',
                    width: 100,
                    dataIndex: 'invoice',
                    render({ record }) {
                        return <a className='btn btn-link' href={`${record.invoice}`} rel='noopener noreferrer'
                                  target='_blank'>查看</a>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 200,
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <RequestButton
                                method={'post'}
                                confirm={'确认结算？'}
                                url={`/admin/approval/settlement/${record.id}/confirm`}
                                variant={'link'}
                                onSuccess={action.reload}
                            >确认结算</RequestButton>
                            <ModalForm
                                text={'取消结算'}
                                action={`/admin/approval/settlement/${record.id}/cancel`}
                                method={'post'}
                                onSuccess={action.reload}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        remark: { type: 'string', title: '取消原因' }
                                    }
                                }}
                                uiSchema={{
                                    remark: {
                                        'ui:options': {
                                            label: false,
                                            placeholder: '取消原因'
                                        }
                                    }
                                }}
                            />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
