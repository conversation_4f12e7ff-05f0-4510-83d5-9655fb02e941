import { Content, ModalForm, NumberFormat, RequestButton, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';

export function Component() {
    return <Content
        title={'余额提现'}
    >
        <Table
            source={'/admin/approval/withdraw/money'}
            columns={[
                {
                    title: '申请时间',
                    dataIndex: 'create_time',
                    width: 160,
                },
                {
                    title: '申请人',
                    align: 'center',
                    dataIndex: 'user',
                    width: 150,
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '账号',
                    align: 'left',
                    dataIndex: 'account',
                    render({ record }) {
                        return `${record.name} | ${record.account}`;
                    }
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    width: 200,
                    render({ value }) {
                        return <NumberFormat className={'text-orange'} value={value / 100} />;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    render({ record, action }) {
                        return <Space>
                            <RequestButton
                                method={'post'}
                                confirm={'确认给对方打款吗？'}
                                url={`/admin/approval/withdraw/money/${record.id}/confirm`}
                                onSuccess={action.reload}
                            >确认打款</RequestButton>
                            <ModalForm
                                text={'取消提现'}
                                action={`/admin/approval/withdraw/money/${record.id}/cancel`}
                                method={'post'}
                                onSuccess={action.reload}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        remark: { type: 'string', title: '取消原因' }
                                    }
                                }}
                                uiSchema={{
                                    remark: {
                                        'ui:options': {
                                            label: false,
                                            placeholder: '取消原因'
                                        }
                                    }
                                }}
                            />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
