import { Content, ModalForm, NumberFormat, Space, Table } from '@topthink/common';
import UserModal from '@/components/user-modal';
import InvoiceOrderModal from '@/components/invoice-order-modal';

export function Component() {

    return <Content title='发票申请'>
        <Table
            source={'/admin/approval/invoice'}
            columns={[
                {
                    title: '申请时间',
                    dataIndex: 'create_time',
                    width: 160,
                },
                {
                    title: '申请人',
                    align: 'center',
                    dataIndex: 'user',
                    width: 150,
                    render({ value }) {
                        return <UserModal id={value.id} text={value.name} />;
                    }
                },
                {
                    title: '发票类型',
                    dataIndex: 'type',
                    width: 120,
                    render({ value }) {
                        return value === 1 ? '增值税普通发票' : '增值税专用发票';
                    }
                },
                {
                    title: '发票抬头',
                    dataIndex: 'title',
                    width: 220,
                },
                {
                    title: '纳税人识别号',
                    dataIndex: 'taxpayer_number',
                },
                {
                    title: '发票总额',
                    width: 120,
                    dataIndex: 'amount',
                    render({ value }) {
                        return <NumberFormat value={value / 100} />;
                    }
                },
                {
                    title: '关联订单',
                    width: 100,
                    key: 'order',
                    align: 'center',
                    render({ record }) {
                        return <InvoiceOrderModal source={`/admin/finance/invoice/${record.id}/order`} />;
                    }
                },
                {
                    title: '操作',
                    key: 'option',
                    width: 150,
                    align: 'right',
                    render({ action, record }) {
                        return <Space>
                            <ModalForm
                                text={'驳回申请'}
                                action={`/admin/approval/invoice/${record.id}/reject`}
                                method={'post'}
                                onSuccess={action.reload}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        remark: {
                                            type: 'string'
                                        }
                                    }
                                }}
                                uiSchema={{
                                    remark: {
                                        'ui:widget': 'textarea',
                                        'ui:options': {
                                            label: false,
                                            placeholder: '驳回原因'
                                        }
                                    }
                                }}
                            />
                            <ModalForm
                                text={'上传发票'}
                                action={`/admin/approval/invoice/${record.id}/pass`}
                                method={'post'}
                                onSuccess={action.reload}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        file: {
                                            type: 'string'
                                        }
                                    }
                                }}
                                uiSchema={{
                                    file: {
                                        'ui:widget': 'upload',
                                        'ui:options': {
                                            label: false,
                                            endpoint: '/upload/invoice',
                                            accept: '.pdf,application/pdf'
                                        }
                                    }
                                }}
                            />
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
