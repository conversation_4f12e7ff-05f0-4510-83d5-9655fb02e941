import { Space, Table, Tooltip } from '@topthink/common';
import { Badge } from 'react-bootstrap';

export const Component = () => {

    return <Table
        source={`/ai/model?type=text`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                width: 120
            },
            {
                dataIndex: 'code',
                title: '标识',
                width: 150,
            },
            {
                dataIndex: 'factor',
                title: '费率',
                render({ value }) {
                    return <Space>
                        {value.embedding > 0 && <Tooltip placement={'top'} tooltip={`费率:${value.embedding}`}>
                            <Badge role={'button'} bg={'success'}>向量</Badge>
                        </Tooltip>}
                        {value.rerank > 0 && <Tooltip placement={'top'} tooltip={`费率:${value.rerank}`}>
                            <Badge role={'button'} bg={'success'}>排序</Badge>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'description',
                title: '介绍',
            },
        ]}
    />;
};
