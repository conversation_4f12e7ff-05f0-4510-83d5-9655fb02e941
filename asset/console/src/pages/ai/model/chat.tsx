import { Space, styled, Table, Tooltip } from '@topthink/common';

export const Component = () => {

    return <Table
        source={`/ai/model?type=chat`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                width: 120
            },
            {
                dataIndex: 'params',
                title: '功能',
                render({ record }) {
                    return <Space size={4}>
                        {record.params?.tool && <Tooltip tooltip={'支持工具调用'}>
                            <Feature><i className='bi bi-tools' /></Feature>
                        </Tooltip>}
                        {record.params?.vision && <Tooltip tooltip={'支持视觉'}>
                            <Feature><i className='bi bi-eye' /></Feature>
                        </Tooltip>}
                        {record.params?.reasoning && <Tooltip tooltip={'支持推理'}>
                            <Feature><i className='bi bi-lightbulb' /></Feature>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'code',
                title: '标识',
                width: 150,
            },
            {
                dataIndex: 'factor',
                title: '费率',
                width: 120,
            },
            {
                dataIndex: 'description',
                title: '介绍'
            },
        ]}
    />;
};

const Feature = styled.div`
    font-size: 10px;
    line-height: 1;
    background-color: hsla(0, 0%, 100%, .48);
    border: 1px solid var(--bs-border-color);
    border-radius: 5px;
    padding-left: .25rem;
    padding-right: .25rem;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(107 114 128);
`;
