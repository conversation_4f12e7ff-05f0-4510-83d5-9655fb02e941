import { Space, Table, Tooltip } from '@topthink/common';
import { Badge } from 'react-bootstrap';

export const Component = () => {

    return <Table
        source={`/ai/model?type=image`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                width: 120
            },
            {
                dataIndex: 'code',
                title: '标识',
                width: 150,
            },
            {
                dataIndex: 'factor',
                title: '费率',
                render({ value }) {
                    return <Space>
                        {value.generations && <Tooltip placement={'top'} tooltip={`费率:${value.generations}`}>
                            <Badge role={'button'} bg={'success'}>文字作画</Badge>
                        </Tooltip>}
                        {value.inpainting && <Tooltip placement={'top'} tooltip={`费率:${value.inpainting}`}>
                            <Badge role={'button'} bg={'success'}>局部重绘</Badge>
                        </Tooltip>}
                        {value.outpainting && <Tooltip placement={'top'} tooltip={`费率:${value.outpainting}`}>
                            <Badge role={'button'} bg={'success'}>图片扩展</Badge>
                        </Tooltip>}
                        {value.upscale && <Tooltip placement={'top'} tooltip={`费率:${value.upscale}`}>
                            <Badge role={'button'} bg={'success'}>高清修复</Badge>
                        </Tooltip>}
                        {value.poster && <Tooltip placement={'top'} tooltip={`费率:${value.poster}`}>
                            <Badge role={'button'} bg={'success'}>创意海报</Badge>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'description',
                title: '介绍',
            },
        ]}
    />;
};
