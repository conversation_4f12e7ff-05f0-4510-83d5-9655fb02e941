import {
    Card,
    NumberFormat,
    RequestButton,
    Space,
    useNavigate,
    useRevalidator,
    useRouteLoaderData
} from '@topthink/common';
import { Col, Row } from 'react-bootstrap';
import classNames from 'classnames';
import awaitPayComplete from '@/utils/await-pay-complete';
import { useState } from 'react';

export const Component = function() {
    const navigate = useNavigate();
    const { revalidate } = useRevalidator();
    const { packages } = useRouteLoaderData('ai') as Ai;
    const [pkg, setPkg] = useState(Object.keys(packages)[0]);
    const selector = packages[pkg];

    return <Card>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={2}>选择套餐：</Col>
            <Col md={10}>
                <Space>
                    {Object.entries(packages).map(([key, item]) => {
                        return <div
                            key={key}
                            onClick={() => setPkg(key)}
                            role='button'
                            className={classNames('border rounded px-5 py-2 ', {
                                'border-primary text-primary bg-primary bg-opacity-10 fw-bold': pkg === key,
                            })}
                        >{item.name}</div>;
                    })}
                </Space>
            </Col>
        </Row>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={2}>套餐金额：</Col>
            <Col md={10} className={'d-flex align-items-center'}>
                <NumberFormat className='text-muted fs-3 text-decoration-line-through me-2' value={selector.origin} />
                <NumberFormat className='text-danger fs-3' value={selector.amount} />
            </Col>
        </Row>
        <Row>
            <Col md={2}></Col>
            <Col md={10}>
                <RequestButton
                    className={'rounded-pill px-5'}
                    size={'lg'}
                    url={{
                        url: `/ai/buy`,
                        method: 'post',
                        data: {
                            package: pkg
                        }
                    }}
                    onSuccess={(result) => {
                        awaitPayComplete({
                            result,
                            onComplete: () => {
                                revalidate();
                                navigate(`/ai/usage`);
                            }
                        });
                    }}
                >去支付</RequestButton>
            </Col>
        </Row>
    </Card>;
}
