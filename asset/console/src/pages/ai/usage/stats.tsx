import { Card, ModalForm, Space, useRevalidator, useRouteLoaderData } from '@topthink/common';
import { Col, Row } from 'react-bootstrap';
import Statistic from '@/components/statistic';
import { formatLongNumber } from '@/utils/format';
import StatsChart from '@/components/stats-chart';
import { useState } from 'react';
import PeriodButtons from '@/components/period-buttons';

export const Component = function() {
    const ai = useRouteLoaderData('ai') as Ai;
    const { revalidate } = useRevalidator();
    const [period, setPeriod] = useState('24hours');

    return <Row className={'g-3'}>
        <Col md={9}>
            <Card>
                <PeriodButtons period={period} onChange={setPeriod} />
            </Card>
            <StatsChart
                url={`/ai/stats`}
                series={{
                    chat: { text: '会话', color: '#3c60ff' },
                    image: { text: '画图', color: '#ffc107' },
                    video: { text: '视频', color: '#ff5733' },
                    audio: { text: '语音', color: '#e83e8c' },
                    text: { text: '文本', color: '#198754' },
                }}
                period={period}
            />
        </Col>
        <Col md={3}>
            <Statistic
                title={'Token 余额'}
                content={<Space>
                    {formatLongNumber(ai.token)}
                    <ModalForm
                        method={'post'}
                        action={`/ai/warn`}
                        modalProps={{ header: '预警设置' }}
                        buttonProps={{ variant: 'link' }}
                        formContext={{ layout: 'horizontal' }}
                        formData={{
                            token: ai.warn_token
                        }}
                        schema={{
                            type: 'object',
                            properties: {
                                token: {
                                    title: '预警余额',
                                    type: 'number',
                                    minimum: 0,
                                }
                            }
                        }}
                        uiSchema={{
                            token: {
                                'ui:options': {
                                    'suffix': 'K'
                                },
                                'ui:help': '低于预警余额将发送通知提醒，设置为0的时候关闭预警',
                            }
                        }}
                        text={'预警'}
                        onSuccess={revalidate}
                    />
                </Space>}
            />
        </Col>
    </Row>;
}
