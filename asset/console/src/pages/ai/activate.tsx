import { useLoaderD<PERSON>, Card, RequestButton, useRevalidator } from '@topthink/common';
import { PropsWithChildren, useState } from 'react';
import { Col, Container, Form, Row } from 'react-bootstrap';

export default function Activate({ children }: PropsWithChildren<any>) {
    const ai = useLoaderData();
    const { revalidate } = useRevalidator();
    const [confirmed, setConfirmed] = useState(false);

    if (!ai) {
        return <Container className={'mt-5'}>
            <Row>
                <Col md={{ offset: 2, span: 8 }}>
                    <Card title={'ThinkAI服务开通'}>
                        <p>
                            <strong>产品简介：</strong>
                            ThinkAI
                        </p>
                        <p>
                            <strong>开通说明：</strong>
                            产品免费开通，赠送Token 100K。
                        </p>
                        <Form.Check
                            id={'sms-apply-confirm'}
                            checked={confirmed}
                            onChange={e => setConfirmed(e.target.checked)}
                            className='mb-3'
                            label={<>我已阅读并同意<a className='link-primary' href='https://doc.topthink.com/think-ai/license.html' target='_blank'>《启智AI服务协议》</a></>}
                        />
                        <RequestButton
                            url={`/ai`}
                            method={'post'}
                            onSuccess={() => {
                                revalidate();
                            }}
                            disabled={!confirmed}
                            tooltip={!confirmed ? '请先勾选相关服务协议' : undefined}
                        >立即开通</RequestButton>
                    </Card>
                </Col>
            </Row>
        </Container>;
    }

    return children;
}
