import { Content, Space, styled, Table } from '@topthink/common';
import { Badge } from 'react-bootstrap';

export const Component = () => {
    return <Content>
        <Table
            rowKey={'name'}
            source={`/ai/plugin`}
            expandable={{
                expandRowByClick: true,
                columnWidth: 40,
                expandedRowRender(plugin: AiPlugin) {
                    return <ToolList>
                        <div className='d-flex border rounded bg-light flex-column'>
                            {plugin.tools.map((tool) => {
                                return <ToolItem key={tool.name}>
                                    <PluginInfo>
                                        <h3 className={'d-flex gap-2 align-items-center'}>
                                            {tool.title}
                                            <Badge bg={'success'}>{tool.name}</Badge>
                                            <Badge bg={'secondary'}>{tool.fee < 0 ? '动态计费' : tool.fee > 0 ? `${tool.fee} Token / 次` : '免费'}</Badge>
                                        </h3>
                                        <p>{tool.description}</p>
                                    </PluginInfo>
                                </ToolItem>;
                            })}
                        </div>
                    </ToolList>;
                }
            }}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        return <Space size={'middle'}>
                            <img className={'rounded'} src={record.icon} width={30} height={30} />
                            <div>
                                <Space>
                                    {record.title}
                                    <Badge bg={'success'}>{record.name}</Badge>
                                </Space>
                                <div className={'text-muted'}>{record.description}</div>
                            </div>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};

const PluginInfo = styled.div`
    flex: 1;

    h3 {
        font-size: 1.15rem;
        line-height: 1.5rem;
        font-weight: 600;
        margin-bottom: .25rem;

        .btn {
            --bs-btn-padding-y: 0;
            --bs-btn-padding-x: 0.5rem;
            --bs-btn-font-size: 11px;
            line-height: 19px;
        }

        .badge {
            --bs-badge-padding-x: 0.5em;
            --bs-badge-padding-y: 0.25em;
            --bs-badge-font-weight: 500;
        }
    }

    p {
        margin-bottom: 0;
        color: var(--bs-secondary);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
    }
`;

const ToolItem = styled.div`
    padding: .75rem 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;

    &:not(:last-child) {
        border-bottom: 1px solid var(--bs-border-color);
    }
`;

const ToolList = styled.div`
    padding-left: 36px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

`;
