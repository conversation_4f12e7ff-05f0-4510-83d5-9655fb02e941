import { Button, Col, Form, FormControl, InputGroup, Row } from 'react-bootstrap';
import { Card, Loader, NumberFormat, Space, styled, Table, Toast, useClipboard, useRequest } from '@topthink/common';
import Statistic from '@/components/statistic';
import { ChangeEvent, useState } from 'react';

export const Component = () => {
    const [url, setUrl] = useState('');
    const { copy } = useClipboard();
    const { result } = useRequest<{
        urls: Record<string, string>,
        user: number,
        charge: number,
        amount: number,
        commission: number,
        products: { label: string, value: string }[]
    }>('/partner/promo/stats', {
        onSuccess({ urls }) {
            setUrl(urls['首页']);
        }
    });

    if (!result) {
        return <Loader />;
    }

    const { urls, user, charge, amount, commission, products } = result;

    return <Row>
        <Col md={9}>
            <Row className={'g-3 mb-3'}>
                <Col>
                    <Statistic
                        title={'当月关联人数'}
                        content={
                            <NumberFormat value={user} currency={false} options={{ minimumFractionDigits: 0 }} />
                        }
                    />
                </Col>
                <Col>
                    <Statistic
                        title={'当月有效订单数'}
                        content={
                            <NumberFormat value={charge} currency={false} options={{ minimumFractionDigits: 0 }} />
                        }
                    />
                </Col>
                <Col>
                    <Statistic
                        title={'当月实付金额'}
                        content={
                            <NumberFormat value={amount / 100} />
                        }
                    />
                </Col>
                <Col>
                    <Statistic
                        title={'当月佣金收入'}
                        content={
                            <NumberFormat value={commission / 100} />
                        }
                    />
                </Col>
            </Row>
            <Card title={'专属推广链接'}>
                <p className={'text-muted'}>分享推广链接，好友或客户登录即可成为关联客户，90天内可享受5~15%消费返利</p>
                <InputGroup className={'flex-nowrap'}>
                    <Select onChange={(e: ChangeEvent<HTMLSelectElement>) => setUrl(e.target.value)}>
                        {Object.entries(urls).map(([key, value]) => {
                            return <option key={value} value={value}>{key}</option>;
                        })}
                    </Select>
                    <FormControl className={'w-100'} readOnly value={url} />
                    <Button className={'text-nowrap'} onClick={() => {
                        copy(url);
                        Toast.success('复制成功');
                    }}>复制链接</Button>
                </InputGroup>
            </Card>
            <Table
                toolBarRender={() => {
                    return <div className={'fs-5'}>专属优惠码</div>;
                }}
                source={'/partner/promo'}
                columns={[
                    {
                        title: '名称',
                        dataIndex: 'title',
                    },
                    {
                        title: '代码',
                        dataIndex: 'code',
                        render: ({ value }) => {
                            return <Space>
                                <span className={'bg-secondary text-light px-2 rounded-2'}>{value}</span>
                                <a onClick={() => {
                                    copy(value);
                                    Toast.success('已复制');
                                }} className={'fs-7'} role={'button'}><i className={'bi bi-clipboard'} /></a>
                            </Space>;
                        }
                    },
                    {
                        title: '折扣',
                        dataIndex: 'discount',
                        render: ({ value }) => {
                            return <>{value}%</>;
                        }
                    },
                    {
                        title: '适用产品',
                        dataIndex: 'product',
                        width: 300,
                        render: ({ value }) => {
                            return (value as string[]).map((item) => {
                                return products.find((product) => product.value === item)?.label;
                            }).join('，');
                        }
                    },
                    {
                        title: '佣金',
                        dataIndex: 'rebate',
                        render: ({ value }) => {
                            return `${value}%`;
                        }
                    },
                    {
                        dataIndex: 'expire_time',
                        width: 100,
                        title: '到期时间',
                        empty: '永久有效',
                        valueType: 'date',
                    },
                ]}
            />
        </Col>
        <Col md={3}>
            <Card title={'帮助文档'}>
                <Row>
                    <Col md={6}><a href={'https://doc.topthink.com/public/cooperation.html'} target='_blank' className={'w-100 btn btn-light'}>推广奖励</a></Col>
                    <Col md={6}><a href={'https://doc.topthink.com/public/partner.html'} target='_blank' className={'w-100 btn btn-light'}>申请代理</a></Col>
                </Row>
            </Card>
        </Col>
    </Row>;
};

const Select = styled(Form.Select)`
    width: 120px !important;
`;
