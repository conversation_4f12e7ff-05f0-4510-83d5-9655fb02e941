import { Space, Table } from '@topthink/common';

export const Component = () => {
    return <Table
        source={`/partner/promo/customer`}
        columns={[
            {
                title: '用户',
                dataIndex: 'name',
                render({ record }) {
                    return <Space>
                        <img className='rounded-circle' width={24} height={24} src={record.avatar} />
                        {record.name}
                    </Space>;
                }
            },
            {
                title: '关联时间',
                dataIndex: ['pivot', 'create_time'],
                width: 160,
            }
        ]}
    />;
};
