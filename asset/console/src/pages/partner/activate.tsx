import { Card, RequestButton, SiderLayout, useLoaderData, useRevalidator } from '@topthink/common';
import { Col, Container, Row } from 'react-bootstrap';
import CertificationAccess from '@/components/certification-access';

export const Component = function() {
    const partner = useLoaderData();
    const { revalidate } = useRevalidator();

    if (!partner) {
        return <CertificationAccess>
            <Container className={'mt-5'}>
                <Row>
                    <Col md={{ offset: 2, span: 8 }}>
                        <Card title={'成为合作伙伴'}>
                            <p>
                                顶想云将以敬畏之心真诚合作，与伙伴共创数字经济时代新价值
                            </p>
                            <RequestButton
                                url={`/partner`}
                                method={'post'}
                                onSuccess={() => {
                                    revalidate();
                                }}
                            >立即加入</RequestButton>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </CertificationAccess>;
    }

    return <SiderLayout />;
};
