import { Card, Content, Result, TabLayout, useRouteLoaderData } from '@topthink/common';

export const Component = () => {
    const partner = useRouteLoaderData('partner') as Partner;

    if (partner.fee) {
        return <TabLayout />;
    }

    return <Content>
        <Card>
            <Result
                status={'info'}
                title={'尚未开通应用代理'}
                extra={<a href={'https://doc.topthink.com/public/partner.html'} target={'_blank'} className={'btn btn-primary'}>申请开通</a>}
            />
        </Card>
    </Content>;
};
