import { ButtonGroup, Col, FormControl, InputGroup, Row, ToggleButton } from 'react-bootstrap';
import { Message, ModalButton, NumberFormat, RequestButton, useClipboard, useRouteLoaderData } from '@topthink/common';
import awaitPayComplete from '@/utils/await-pay-complete';
import { useEffect, useState } from 'react';

interface Props {
    url: string;
    onSuccess: () => void;
}

export default function Recharge({ url, onSuccess }: Props) {

    const partner = useRouteLoaderData('partner') as Partner;
    const [nums, setNums] = useState<number>(1);
    const [selfPay, setSelfPay] = useState<boolean>(false);
    const fee = partner.fee?.token || 0;
    const [amount, setAmount] = useState<number>(nums * fee);
    const { copy } = useClipboard();

    useEffect(() => {
        setAmount(Math.max(nums * fee, amount));
    }, [nums]);

    return <ModalButton
        text={'充值'}
        modalProps={{
            footer: function({ cancelButton, action }) {
                return <>
                    {cancelButton}
                    {selfPay ? <RequestButton
                        variant={'primary'}
                        url={{
                            url,
                            method: 'post',
                            data: {
                                nums,
                                amount,
                            }
                        }}
                        onSuccess={(result) => {
                            action.close();
                            Message.success({
                                title: '支付链接已创建',
                                html: `<p>${result.pay_url}</p>`,
                                timer: undefined,
                                showConfirmButton: true,
                                toast: false,
                                position: 'center',
                                showCancelButton: true,
                                allowOutsideClick: false,
                                confirmButtonText: '复制链接',
                                cancelButtonText: '关闭',
                                preConfirm() {
                                    copy(result.pay_url);
                                    Message.success({ title: '支付链接已复制到剪贴板' });
                                }
                            });
                        }}
                    >创建支付链接</RequestButton> : <RequestButton
                        variant={'primary'}
                        url={{
                            url,
                            method: 'post',
                            data: {
                                nums
                            }
                        }}
                        onSuccess={(result) => {
                            action.close();
                            awaitPayComplete({
                                result,
                                onComplete: onSuccess
                            });
                        }}
                    >去支付</RequestButton>}
                </>;
            }
        }}
    >
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>数量：</Col>
            <Col md={5}>
                <InputGroup>
                    <FormControl
                        type={'number'}
                        value={nums}
                        min={1}
                        step={1}
                        onChange={(e) => setNums(parseInt(e.target.value))}
                    />
                    <InputGroup.Text>M</InputGroup.Text>
                </InputGroup>
            </Col>
        </Row>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>费用：</Col>
            <Col md={9} className={'d-flex align-items-center'}>
                <NumberFormat className='text-danger fs-4' value={nums * fee} />
            </Col>
        </Row>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>支付方式：</Col>
            <Col md={9}>
                <ButtonGroup>
                    <ToggleButton
                        variant='outline-primary'
                        id={'radio1'}
                        type='radio'
                        name='radio'
                        value={0}
                        checked={!selfPay}
                        onChange={() => setSelfPay(false)}
                    >
                        代理代付
                    </ToggleButton>
                    <ToggleButton
                        id={'radio2'}
                        variant='outline-primary'
                        type='radio'
                        name='radio'
                        value={1}
                        checked={selfPay}
                        onChange={() => setSelfPay(true)}
                    >
                        用户自付
                    </ToggleButton>
                </ButtonGroup>
            </Col>
        </Row>
        {selfPay && <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>售价：</Col>
            <Col md={5}>
                <InputGroup>
                    <InputGroup.Text>￥</InputGroup.Text>
                    <FormControl
                        type={'number'}
                        value={amount}
                        onChange={(e) => setAmount(parseFloat(e.target.value))}
                        min={nums * fee}
                        step={1}
                    />
                    <InputGroup.Text>元</InputGroup.Text>
                </InputGroup>
            </Col>
        </Row>}
    </ModalButton>;
}
