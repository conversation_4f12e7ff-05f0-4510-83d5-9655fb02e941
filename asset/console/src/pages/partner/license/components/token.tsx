import { Space, Toast, useClipboard, useRequest } from '@topthink/common';
import { maskString } from 'mask-sensitive-data';
import { useState } from 'react';

interface Props {
    url: string;
}

export default function Token({ url }: Props) {

    const [mask, setMask] = useState(true);
    const { result } = useRequest(url);
    const { copy } = useClipboard();

    if (!result) {
        return null;
    }
    return <Space>
        <span className='font-monospace'>
            {mask ? maskString(result.token, {
                maskSymbol: '*',
                maxCharsToMask: result.token.length,
                visibleCharsFromStart: 9,
                visibleCharsFromEnd: 13,
            }) : result.token}
        </span>
        <span role='button' onClick={() => setMask(!mask)}>
            {mask ? <i className={'bi bi-eye-fill text-primary'} /> :
                <i className={'bi bi-eye-slash-fill text-primary'} />}
        </span>
        <i role='button'
           onClick={() => {
               copy(result.token);
               Toast.success('复制成功');
           }}
           className={'bi bi-copy text-primary'}
        />
    </Space>;
}
