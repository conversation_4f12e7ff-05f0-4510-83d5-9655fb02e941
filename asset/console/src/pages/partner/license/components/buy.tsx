import { ButtonGroup, Col, FormControl, InputGroup, Row, ToggleButton } from 'react-bootstrap';
import { Message, ModalButton, NumberFormat, RequestButton, useClipboard } from '@topthink/common';
import awaitPayComplete from '@/utils/await-pay-complete';
import { useState } from 'react';

interface Props {
    url: string;
    token?: number;
    text?: string;
    onSuccess: () => void;
    fee?: number;
    years?: number;
}

export default function Buy({ url, token = 10, text = '开通', fee = 0, years, onSuccess }: Props) {
    const [selfPay, setSelfPay] = useState<boolean>(false);
    const [amount, setAmount] = useState<number>(fee);
    const { copy } = useClipboard();

    return <ModalButton
        text={text}
        modalProps={{
            footer: function({ cancelButton, action }) {
                return <>
                    {cancelButton}
                    {selfPay ? <RequestButton
                        variant={'primary'}
                        url={{
                            url,
                            method: 'post',
                            data: {
                                amount,
                            }
                        }}
                        onSuccess={(result) => {
                            action.close();
                            Message.success({
                                title: '支付链接已创建',
                                html: `<p>${result.pay_url}</p>`,
                                timer: undefined,
                                showConfirmButton: true,
                                toast: false,
                                position: 'center',
                                showCancelButton: true,
                                allowOutsideClick: false,
                                confirmButtonText: '复制链接',
                                cancelButtonText: '关闭',
                                preConfirm() {
                                    copy(result.pay_url);
                                    Message.success({ title: '支付链接已复制到剪贴板' });
                                }
                            });
                        }}
                    >创建支付链接</RequestButton> : <RequestButton
                        variant={'primary'}
                        url={{
                            url,
                            method: 'post',
                        }}
                        onSuccess={(result) => {
                            action.close();
                            awaitPayComplete({
                                result,
                                onComplete: onSuccess
                            });
                        }}
                    >去支付</RequestButton>}
                </>;
            }
        }}
    >
        {years && <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>开通时长：</Col>
            <Col md={4}>
                1年
            </Col>
        </Row>}
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>赠送Token：</Col>
            <Col md={4}>
                {token}M
            </Col>
        </Row>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>费用：</Col>
            <Col md={9} className={'d-flex align-items-center'}>
                <NumberFormat className='text-danger fs-4' value={fee} />
            </Col>
        </Row>
        <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>支付方式：</Col>
            <Col md={9}>
                <ButtonGroup>
                    <ToggleButton
                        variant='outline-primary'
                        id={'radio1'}
                        type='radio'
                        name='radio'
                        value={0}
                        checked={!selfPay}
                        onChange={() => setSelfPay(false)}
                    >
                        代理代付
                    </ToggleButton>
                    <ToggleButton
                        id={'radio2'}
                        variant='outline-primary'
                        type='radio'
                        name='radio'
                        value={1}
                        checked={selfPay}
                        onChange={() => setSelfPay(true)}
                    >
                        用户自付
                    </ToggleButton>
                </ButtonGroup>
            </Col>
        </Row>
        {selfPay && <Row className='mb-4 align-items-center'>
            <Col className={'text-end'} md={3}>售价：</Col>
            <Col md={5}>
                <InputGroup>
                    <InputGroup.Text>￥</InputGroup.Text>
                    <FormControl
                        type={'number'}
                        value={amount}
                        onChange={(e) => setAmount(parseFloat(e.target.value))}
                        min={fee}
                        step={1}
                    />
                    <InputGroup.Text>元</InputGroup.Text>
                </InputGroup>
            </Col>
        </Row>}
    </ModalButton>;
}
