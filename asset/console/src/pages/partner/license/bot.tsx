import { dayjs, ModalForm, Space, Table, useRouteLoaderData } from '@topthink/common';
import { formatLongNumber } from '@/utils/format';
import Recharge from './components/recharge';
import Token from './components/token';
import Buy from './components/buy';
import Renew from './components/renew';

export const Component = () => {
    const partner = useRouteLoaderData('partner') as Partner;

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                text={'创建授权码'}
                action={`/partner/license/bot`}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            title: '网站名称',
                            type: 'string'
                        },
                    }
                }}
                onSuccess={() => {
                    action.reload();
                }}
            />;
        }}
        source={'/partner/license/bot'}
        columns={[
            {
                title: '站点名称',
                dataIndex: 'name',
            },
            {
                title: '版本',
                dataIndex: 'plan',
                width: 100,
                render({ value }) {
                    return <div className={value.value == 'trial' ? 'text-orange' : 'text-success'}>{value.label}</div>;
                }
            },
            {
                title: '授权码',
                key: 'code',
                width: 380,
                render({ record }) {
                    return <Token key={record.expire_time} url={`/partner/license/bot/${record.id}/token`} />;
                }
            },
            {
                title: 'Token 余额',
                dataIndex: 'token',
                width: 150,
                align: 'center',
                render({ value, record, action }) {
                    return <Space size={5}>
                        {formatLongNumber(value)}
                        <Recharge url={`/partner/license/bot/${record.id}/recharge`} onSuccess={() => action.reload()} />
                    </Space>;
                }
            },
            {
                title: '有效日期',
                dataIndex: 'expire_time',
                width: 150,
                render({ value, record, action }) {
                    if (value) {
                        if (dayjs(value).isBefore(dayjs())) {
                            return <Space>
                                <div className='text-danger'>已过期</div>
                                {record.plan.value == 'trial' && <Renew
                                    url={`/partner/license/bot/${record.id}/renew`}
                                    onSuccess={() => action.reload()}
                                />}
                            </Space>;
                        }
                        return value;
                    }
                    return '--';
                }
            },
            {
                title: '创建日期',
                dataIndex: 'create_time',
                width: 150,
            },
            {
                title: '操作',
                key: 'action',
                width: 150,
                align: 'right',
                render({ record, action }) {
                    return <Space>
                        {record.plan.value == 'trial' && <Buy
                            url={`/partner/license/bot/${record.id}/buy`}
                            fee={partner.fee?.bot}
                            token={100}
                            onSuccess={() => action.reload()}
                        />}
                        <ModalForm
                            text={'编辑'}
                            action={`/partner/license/bot/${record.id}`}
                            method={'put'}
                            formData={{
                                name: record.name,
                            }}
                            schema={{
                                type: 'object',
                                properties: {
                                    name: {
                                        title: '网站名称',
                                        type: 'string'
                                    },
                                }
                            }}
                            onSuccess={() => {
                                action.reload();
                            }}
                        />
                    </Space>;
                }
            }
        ]}
    />;
};
