import { Button, Content, Loader, ModalForm, NumberFormat, Table, useRequest } from '@topthink/common';
import { Alert } from 'react-bootstrap';
import { formatMoney } from '@/utils/format';

export const Component = () => {
    const { result, execute } = useRequest('/partner/settlement/create');

    if (!result) {
        return <Loader />;
    }

    return <Content showBack>
        <Table
            toolBarRender={(action) => {
                if (result.total <= 0) {
                    return <Button tooltip={'暂无可结算佣金'} disabled>申请结算</Button>;
                }
                return <ModalForm
                    text={'申请结算'}
                    method={'post'}
                    action={'/partner/settlement'}
                    schema={{
                        type: 'object',
                        properties: {
                            amount: {
                                title: '结算金额',
                                type: 'number',
                                description: `当前最多可结算${formatMoney(result.total / 100)}元`
                            },
                            type: {
                                title: '结算方式',
                                type: 'string',
                                enum: [1, 2],
                                enumNames: ['云币', '余额'],
                                default: 1,
                            }
                        },
                        dependencies: {
                            type: {
                                oneOf: [
                                    {
                                        properties: {
                                            type: {
                                                const: 2,
                                            },
                                            invoice: {
                                                title: '发票',
                                                type: 'string',
                                                description: '请上传电子发票（仅支持 PDF 格式）'
                                            }
                                        },
                                    },
                                ]
                            }
                        }
                    }}
                    uiSchema={{
                        type: {
                            'ui:widget': 'radio',
                        },
                        invoice: {
                            'ui:widget': 'upload',
                            'ui:options': {
                                endpoint: '/upload/invoice',
                                accept: '.pdf,application/pdf',
                            }
                        }
                    }}
                    onSuccess={() => {
                        action.reload();
                        execute();
                    }}
                >
                    <Alert variant={'info'}>
                        <h6 className={'fw-bold'}>结算方式说明：</h6>
                        <ul className={'mb-0'}>
                            <li>云币结算由平台完税需扣除6.5%的税费</li>
                            <li>余额结算需<a className={'link-primary fw-bold'} href={'https://doc.topthink.com'} target={'_blank'}>提供发票</a>
                            </li>
                        </ul>
                    </Alert>
                </ModalForm>;
            }}
            source={'/partner/settlement'}
            columns={[
                {
                    title: '日期',
                    align: 'left',
                    dataIndex: 'create_time',
                    width: 200
                },
                {
                    title: '金额',
                    dataIndex: 'amount',
                    width: 200,
                    render({ value }) {
                        return <NumberFormat className={'text-orange'} value={value / 100} />;
                    }
                },
                {
                    title: '备注',
                    dataIndex: 'remark',
                    render({ value }) {
                        return value || '--';
                    }
                },
                {
                    title: '状态',
                    dataIndex: 'status',
                    width: 200,
                    align: 'right',
                    render({ record }) {
                        return <span className={`text-${record.status_color}`}>{record.status_text}</span>;
                    }
                }
            ]}
        />
    </Content>;
};
