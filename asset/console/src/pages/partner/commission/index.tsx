import { Columns, Content, Link, Loader, NumberFormat, Space, Table, useRequest } from '@topthink/common';

export const Component = () => {
    const { result: commission } = useRequest('/partner/commission');

    if (!commission) {
        return <Loader />;
    }

    const columns: Columns = [
        {
            title: '日期',
            align: 'left',
            dataIndex: 'create_time',
            width: 200
        },
        {
            title: '说明',
            align: 'left',
            dataIndex: 'info',
        },
        {
            title: '数量',
            align: 'right',
            dataIndex: 'number',
            render({ value, record }) {
                const className = record.type === 1 ? 'text-success' : 'text-danger';

                return <span className={className}>
                    {record.type === 1 ? '+' : '-'}
                    <NumberFormat value={value / 100} />
                </span>;
            }
        }
    ];

    return <Content>
        <Table
            toolBarRender={() => {
                return <Space>
                    <span className={'d-flex align-items-center'}>
                        共
                        <span className='fs-4 text-orange mx-2'>
                            <NumberFormat value={commission.total / 100} />
                        </span>
                        元
                    </span>
                    <Link className={'text-primary'} to={'/partner/commission/settlement'}>结算</Link>
                </Space>;
            }}
            columns={columns}
            source={'/partner/commission/logs'}
        />
    </Content>;
};
