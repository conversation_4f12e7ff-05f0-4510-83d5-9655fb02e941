const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");

module.exports = (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/main.tsx",
        cache    : {
            type: "filesystem"
        },
        output   : {
            filename     : "app.[contenthash:6].js",
            chunkFilename: "[id].[contenthash:6].js",
            path         : path.resolve(__dirname, "dist/asset"),
            clean        : true,
            publicPath   : isServer ? "/" : "/asset/"
        },
        module   : {
            rules: [
                {
                    test: /\.xlsx$/,
                    use : [
                        {
                            loader : require.resolve("file-loader"),
                            options: {
                                name: "media/[name].[hash:8].[ext]"
                            }
                        }
                    ]
                }
            ]
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : {
                    filename          : isServer ? "index.html" : "../index.html",
                    template          : "public/index.ejs",
                    inject            : false,
                    scriptLoading     : "blocking",
                    favicon           : "public/favicon.ico",
                    templateParameters: {
                        isDevelopment
                    }
                },
                react: true
            })
        ].filter(Boolean),
        externals: {
            "react"           : "React",
            "react-dom"       : "ReactDOM",
            "react-dom/client": "ReactDOM"
        },
        devServer: {
            hot               : true,
            historyApiFallback: true,
            proxy             : {
                "/data"   : {
                    target      : "http://console.topthink.org",
                    changeOrigin: true
                },
                "/uploads": {
                    target      : "http://www.topthink.org",
                    changeOrigin: true
                }
            },
            compress          : false
        }
    };
};
