{"name": "@topthink/console", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "dependencies": {"@ant-design/maps": "^1.0.6", "@ant-design/plots": "^1.2.5", "@react-hook/hover": "^4.0.0", "@topthink/common": "^1.5.7", "ace-builds": "^1.33.2", "bootstrap": "^5.0.2", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.2", "dayjs": "^1.11.5", "file-saver": "^2.0.5", "jszip": "^3.10.1", "loadjs": "4.3.0-rc1", "mask-sensitive-data": "^0.11.4", "qrcode.react": "^3.0.2", "rc-rate": "^2.12.0", "rc-switch": "^4.1.0", "react-ace": "^11.0.1", "react-bootstrap": "^2.1.2", "react-countdown-hook": "^1.1.1", "react-easy-crop": "^4.0.3", "react-infinite-scroller": "^1.2.6", "react-medium-image-zoom": "^5.1.6", "use-debounce": "^9.0.4", "write-excel-file": "^1.4.29"}, "devDependencies": {"@topthink/webpack-config-plugin": "^1.0.16", "@types/file-saver": "^2.0.5", "@types/loadjs": "^4.0.1", "@types/lodash": "^4.14.161", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-infinite-scroller": "^1.2.3", "babel-plugin-styled-components": "^1.13.1", "typescript": "^4.3.2", "webpack": "^5.85.1", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}}