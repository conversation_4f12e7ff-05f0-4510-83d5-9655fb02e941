{"name": "@topthink/notification", "version": "0.0.0", "scripts": {"build": "webpack --progress", "build:dev": "webpack --progress --env dev", "serve": "webpack serve --env dev"}, "devDependencies": {"@restart/ui": "^1.6.1", "@topthink/webpack-config-plugin": "^1.0.16", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-transition-group": "^4.4.5", "@types/styled-components": "^5.1.11", "axios": "^1.3.4", "react": "^18.0.0", "react-async-hook": "^3.6.2", "react-dom": "^18.0.0", "react-easy-infinite-scroll-hook": "^2.1.2", "react-transition-group": "^4.4.5", "styled-components": "^5.3.6", "typescript": "^4.0.2", "webpack": "^5.85.1", "webpack-cli": "^5.1.3", "webpack-dev-server": "^4.15.0"}, "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "license": "MIT"}