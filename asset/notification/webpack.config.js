const path                = require("path");
const WebpackConfigPlugin = require("@topthink/webpack-config-plugin");
const Server              = require("webpack-dev-server");

module.exports = async (env) => {
    const isDevelopment = !!env.dev;
    const isServer      = !!env.WEBPACK_SERVE;

    const port = await Server.getFreePort();

    const publicPath = isServer ? `http://localhost:${port}/` : "/asset/";

    return {
        mode     : isDevelopment ? "development" : "production",
        entry    : "./src/index.tsx",
        cache    : {
            type: "filesystem"
        },
        output   : {
            filename: "notification.min.js",
            path    : path.resolve(__dirname, "dist"),
            clean   : true,
            publicPath
        },
        plugins  : [
            new WebpackConfigPlugin({
                serve: isServer,
                html : false,
                react: true
            })
        ],
        devServer: {
            hot         : true,
            port,
            headers     : {
                "Access-Control-Allow-Origin" : "*",
                "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
                "Access-Control-Allow-Headers": "X-Requested-With, content-type, Authorization"
            },
            allowedHosts: "all",
            client      : {
                webSocketURL: {
                    hostname: "localhost"
                }
            },
            proxy       : {
                "/notification": {
                    target : "http://localhost",
                    headers: {
                        host: "www.topthink.org"
                    }
                }
            }
        }
    };
};
