import { createGlobalStyle, StyleSheetManager } from 'styled-components';
import App from './components/app';
import ReactDOM from 'react-dom';
import axios from 'axios';

export default class Notification {
    #inited = false;

    constructor(script: HTMLScriptElement) {
        const attr = script.getAttribute.bind(script);

        axios.defaults.baseURL = new URL(script.src).origin;

        const token = attr('data-token');
        const channel = attr('data-channel');
        const handler = document.querySelector<HTMLElement>('[data-notification]');

        if (token && handler) {
            this.init(handler, token, channel);
        }
    }

    init(handler: HTMLElement, token: string, channel: string | null) {
        if (this.#inited) {
            return;
        }
        this.#inited = true;

        axios.interceptors.request.use(
            config => {
                config.headers = Object.assign({}, config.headers, {
                    Authorization: `Bearer ${token}`,
                    Channel: channel
                });
                return config;
            },
            error => {
                return Promise.reject(error);
            }
        );

        const root = document.createElement('div');
        document.body.appendChild(root);

        const shadow = root.attachShadow({ mode: 'closed' });

        const app = document.createElement('div');
        shadow.appendChild(app);

        const GlobalStyle = createGlobalStyle`
          *,
          *::before,
          *::after {
            box-sizing: border-box;
          }

          ::-webkit-scrollbar {
            -webkit-appearance: none;
            width: 6px;
            height: 6px;
          }

          ::-webkit-scrollbar-thumb {
            cursor: pointer;
            border-radius: 6px;
            background: rgba(0, 0, 0, .25);
            transition: color .2s ease;
          }

          ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, .1);
            border-radius: 0;
          }
        `;

        const RootGlobalStyle = createGlobalStyle`
          [data-notification] {
            [data-badge] {
              display: none !important;
            }

            &[data-unread] {
              [data-badge] {
                display: block !important;
              }

            }
          }
        `;

        const element = <>
            <RootGlobalStyle />
            <StyleSheetManager target={shadow}>
                <>
                    <GlobalStyle />
                    <App target={app} handler={handler} />
                </>
            </StyleSheetManager>
        </>;

        ReactDOM.render(element, app);
    }
}




