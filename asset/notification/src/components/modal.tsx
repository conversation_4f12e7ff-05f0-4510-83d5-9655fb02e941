import BaseModal, { ModalProps } from '@restart/ui/Modal';
import styled from 'styled-components';
import Transition, { TransitionStatus } from 'react-transition-group/Transition';
import { cloneElement } from 'react';

const fadeStyles: Record<TransitionStatus, string> = {
    entering: 'show',
    entered: 'show',
    exited: '',
    exiting: '',
    unmounted: ''
};

const Fade: ModalProps['transition'] = ({ children, ...props }) => <Transition {...props} timeout={150}>
    {(status) => cloneElement(children, {
        className: `fade ${fadeStyles[status]}`,
    })}
</Transition>;


export default function Modal({ children, onHide, ...props }: ModalProps) {
    const renderDialog: ModalProps['renderDialog'] = (props) => <Container
        {...props}
    >
        <InnerBackdrop onClick={onHide} />
        <Dialog>{children}</Dialog>
    </Container>;

    const renderBackdrop: ModalProps['renderBackdrop'] = (props) => <Backdrop {...props} />;

    return <BaseModal
        {...props}
        transition={Fade}
        backdropTransition={Fade}
        renderBackdrop={renderBackdrop}
        renderDialog={renderDialog}
    />;
}

const InnerBackdrop = styled.div`
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
`;

const Backdrop = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;

  &.fade {
    transition: opacity .15s linear;
    opacity: 0;
  }

  &.show {
    opacity: .45;
  }
`;

const Dialog = styled.div`
  width: 880px;
  margin: 100px auto 28px;
`;

const Container = styled.div`
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  overflow-x: hidden;
  overflow-y: auto;

  &:focus-visible {
    outline: none;
  }

  &.fade {
    transition: opacity .15s linear;

    ${Dialog} {
      transition: transform .3s ease-out;
      transform: translateY(-50px);
    }
  }

  &.show {
    ${Dialog} {
      transform: none;
    }
  }
`;
