import { useAsync } from 'react-async-hook';
import axios from 'axios';
import styled from 'styled-components';

export default function Qrcode() {
    const { result } = useAsync(async () => {
        const { data } = await axios.get('/notification/qrcode');
        return data;
    }, []);

    if (!result) {
        return null;
    }
    return <Container>
        <img src={result.qrcode} />
        <Desc>关注微信服务号<br />获得通知提醒</Desc>
    </Container>;
}
const Desc = styled.div`
  color: #727272;
  font-size: 12px;
  padding: 0 12px 12px;
  margin-top: -5px;
`;

const Container = styled.div`

  img {
    width: 100%;
    padding: 0 3px;
  }
`;
