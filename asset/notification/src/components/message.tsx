import styled from 'styled-components';
import { useAsyncCallback } from 'react-async-hook';
import axios from 'axios';

interface Props {
    notification: {
        id: string
        avatar: string
        content: string
        create_time: string
        url?: string
    };
    onClear: Function;
}

export default function Message({ notification: { id, avatar, content, create_time, url }, onClear }: Props) {

    const asyncClear = useAsyncCallback(async () => {
        await axios.put(`/notification/${id}`);
    }, {
        onSuccess() {
            onClear();
        }
    });

    return <Container onClick={(e) => {
        if (e.target instanceof HTMLAnchorElement) {
            asyncClear.execute();
        }
    }}>
        <Avatar><img src={avatar} /></Avatar>
        <Body>
            <Content>
                <p dangerouslySetInnerHTML={{ __html: content }} />
                <time>{create_time}</time>
            </Content>
            {url && <Action>
                <a href={url} target='_blank'>查看</a>
            </Action>}
        </Body>
    </Container>;
}

const Action = styled.div`
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;

  a {
    border: 1px solid #e7e9e8;
    border-radius: 6px;
    cursor: pointer;
    padding: 4px 12px;
    color: #262626;
    text-decoration: none;

    &:hover {
      background: #FFFFFF;
    }
  }
`;

const Content = styled.div`
  color: #585a5a;
  flex: 1;

  a {
    text-decoration: none;
    color: #3c60ff;
  }

  h3, p {
    font-weight: 500;
    margin: 0 0 12px;
  }

  time {
    font-size: 12px;
    color: #8a8f8d;
  }
`;

const Body = styled.div`
  margin-left: 12px;
  flex: 1;
  display: flex;
`;

const Avatar = styled.div`
  img {
    width: 32px;
    height: 32px;
    border-radius: 100%;
  }
`;

const Container = styled.div`
  padding: 16px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  display: flex;

  &:hover {
    background: #fafafa;
  }
`;
