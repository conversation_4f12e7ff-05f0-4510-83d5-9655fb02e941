import { ForwardedRef, forwardRef, ReactNode, useImperativeHandle } from 'react';
import { useAsync, useAsyncFetchMore } from 'react-async-hook';
import styled from 'styled-components';
import useInfiniteScroll from 'react-easy-infinite-scroll-hook';

const InfiniteListComponent = ({ children, dataLength, canLoadMore, next }) => {

    const ref = useInfiniteScroll<HTMLDivElement>({
        next,
        rowCount: dataLength,
        scrollThreshold: '0.1',
        hasMore: { up: false, down: canLoadMore },
    });

    return <ScrollContainer ref={ref}>
        {children}
    </ScrollContainer>;
};


interface ScrollListData<T> {
    data: T[];
    hasMore: boolean;
    page: number;
}

interface ScrollListProps<R> {
    fetchData: (result?: ScrollListData<R>) => Promise<ScrollListData<R>>;
    renderItem: (item: R, index: number) => ReactNode;
    deps?: any[];
}

interface ScrollList {
    refresh: Function;
}

type ScrollListType = <T>(
    props: ScrollListProps<T> & { ref?: ForwardedRef<ScrollList> }
) => ReturnType<typeof ScrollListInner>

const ScrollListInner = function <R>({
    fetchData,
    renderItem,
    deps = []
}: ScrollListProps<R>, ref: ForwardedRef<ScrollList>) {
    const value = useAsync(() => fetchData(), deps);

    useImperativeHandle(ref, () => ({
        refresh: () => {
            value.execute();
        }
    }));

    const { fetchMore: next, isEnd } = useAsyncFetchMore({
        value,
        fetchMore: (result) => fetchData(result),
        merge: (result, moreResult) => {
            return {
                ...moreResult,
                data: [
                    ...result.data,
                    ...moreResult.data
                ]
            };
        },
        isEnd: (moreResult) => {
            return !moreResult.hasMore;
        }
    });

    const { result } = value;

    if (!result) {
        return null;
    }
    if (result.data.length === 0) {
        return <Empty>暂无消息</Empty>;
    }

    return <InfiniteListComponent
        dataLength={result.data.length}
        next={next}
        canLoadMore={!isEnd}
    >
        {result.data.map((item, index) => renderItem(item, index))}
    </InfiniteListComponent>;
};


const ScrollList: ScrollListType = forwardRef(ScrollListInner) as ScrollListType;

export default ScrollList;

const Empty = styled.div`
  height: 100%;
  color: #bec0bf;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const ScrollContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;
