import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import Qrcode from './qrcode';
import { ReactComponent as CloseIcon } from '../images/close.svg';
import { ReactComponent as ClearIcon } from '../images/clear.svg';
import { ReactComponent as RemoveIcon } from '../images/remove.svg';
import Message from './message';
import Modal from './modal';
import Nav from '@restart/ui/Nav';
import { useAsync, useAsyncCallback } from 'react-async-hook';
import axios from 'axios';
import ScrollList from './scroll-list';

interface Props {
    target: HTMLElement;
    handler: HTMLElement;
}

export default function App({ target, handler }: Props) {
    const [show, setShow] = useState(false);
    const [type, setType] = useState('unread');
    const listRef = useRef<ScrollList>(null);

    useEffect(() => {
        const listener = function(e: MouseEvent) {
            e.preventDefault();
            setShow(true);
        };
        handler.addEventListener('click', listener);
        return () => {
            handler.removeEventListener('click', listener);
        };
    }, [setShow]);

    const asyncCount = useAsync(async () => {
        const { data } = await axios.get('/notification/count');
        return data;
    }, [], {
        onSuccess(data) {
            if (data.unread > 0) {
                handler.setAttribute('data-unread', '');
            } else {
                handler.removeAttribute('data-unread');
            }
        }
    });

    const asyncClear = useAsyncCallback(async () => {
        await axios.put('/notification');
    }, {
        onSuccess() {
            asyncCount.execute();
            listRef.current?.refresh();
        }
    });

    const asyncRemove = useAsyncCallback(async () => {
        await axios.delete('/notification');
    }, {
        onSuccess() {
            asyncCount.execute();
            listRef.current?.refresh();
        }
    });

    useEffect(() => {
        if (show) {
            setType('unread');
        }
    }, [show]);

    return <Modal
        show={show}
        onHide={() => setShow(false)}
        container={target}
    >
        <Container>
            <Side>
                <Title>消息中心</Title>
                <StyledNav activeKey={type} onSelect={(eventKey: string) => {
                    setType(eventKey);
                }}>
                    <NavItem eventKey={'unread'}>
                        未读消息
                        {asyncCount.result && <span>{asyncCount.result.unread}</span>}
                    </NavItem>
                    <NavItem eventKey={'all'}>
                        全部消息
                        {asyncCount.result && <span>{asyncCount.result.all}</span>}
                    </NavItem>
                </StyledNav>
                <Qrcode />
            </Side>
            <Body>
                <Header>
                    {type === 'unread' ? <Action onClick={asyncClear.execute}><ClearIcon />全部已读</Action> :
                        <Action onClick={asyncRemove.execute}><RemoveIcon />全部删除</Action>}
                    <Close onClick={() => setShow(false)}><CloseIcon /></Close>
                </Header>
                <Messages>
                    <ScrollList<any>
                        deps={[type]}
                        ref={listRef}
                        fetchData={async (result) => {
                            const { data: list } = await axios.get('/notification', {
                                params: {
                                    type,
                                    page: result ? result.page + 1 : 1
                                }
                            });

                            return {
                                data: list.data,
                                hasMore: list.current_page < list.last_page,
                                page: list.current_page
                            };
                        }}
                        renderItem={(notification) => <Message onClear={() => {
                            asyncCount.execute();
                            listRef.current?.refresh();
                        }} notification={notification} key={notification.id} />}
                    />
                </Messages>
            </Body>
        </Container>
    </Modal>;
}

const Action = styled.a`
  display: flex;
  align-items: center;
  margin-right: 10px;
  cursor: pointer;
  color: #585a5a;

  svg {
    margin-right: 3px;
  }

  &:hover {
    color: #262626;
  }
`;

const Close = styled.a`
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.45);

  &:hover {
    color: #262626;
  }
`;

const Messages = styled.div`
  flex: 1;
  overflow: hidden;
`;

const Header = styled.div`
  height: 52px;
  padding: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: end;
`;

const Body = styled.div`
  border-left: 1px solid rgba(0, 0, 0, 0.06);
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const NavItem = styled(Nav.Item)`
  padding: 5px 12px;
  height: 32px;
  line-height: 22px;
  margin-top: 8px;
  border-radius: 6px;
  cursor: pointer;
  background-color: transparent;
  color: #8A8F8D;
  border: none;
  outline: none;
  width: 100%;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &[data-rr-ui-active="true"], &:hover {
    background-color: #EFF0F0;
    color: #262626;
  }
`;
const StyledNav = styled(Nav)`
  flex: 1;
  padding: 0 12px;
  user-select: none;
`;

const Title = styled.div`
  padding-left: 24px;
  font-size: 16px;
  height: 52px;
  line-height: 52px;
  user-select: none;
`;

const Side = styled.div`
  width: 160px;
  display: flex;
  flex-direction: column;
`;

const Container = styled.div`
  position: relative;
  background-color: #FFFFFF;
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  display: flex;
  height: 610px;
  font-family: Chinese Quote, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: #262626;
`;
